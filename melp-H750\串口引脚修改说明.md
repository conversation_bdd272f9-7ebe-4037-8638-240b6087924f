# 串口引脚修改说明

## 📋 修改概述

将项目中的串口配置从UART4(PA0/PA1)修改为USART1(PA9/PA10)，以满足硬件设计要求。

## 🔧 修改内容

### 1. 主要配置文件修改

#### main.c
- 将 `UART_HandleTypeDef huart4` 改为 `UART_HandleTypeDef huart1`
- 将 `MX_UART4_Init()` 改为 `MX_USART1_Init()`
- 将 `huart4.Instance = UART4` 改为 `huart1.Instance = USART1`
- 更新所有相关的函数调用

#### stm32h7xx_hal_msp.c
- 修改MSP初始化函数，从UART4改为USART1
- 将引脚配置从PA0/PA1改为PA9/PA10
- 将复用功能从GPIO_AF8_UART4改为GPIO_AF7_USART1
- 更新时钟配置从UART4改为USART1

#### hal_uart.c
- 将所有 `huart4` 引用改为 `huart1`
- 更新所有UART句柄比较和操作

### 2. 配置文件修改

#### melp-H750.ioc
- 更新CubeMX配置文件中的引脚分配
- 将PA0/PA1的UART4配置改为PA9/PA10的USART1配置

### 3. 测试文件修改

#### test_hal_drivers.c
- 更新测试文件中的UART句柄引用

### 4. 文档更新

#### 设计文档
- 更新MELP-STM32H750VBT6-设计文档.md中的串口配置说明

#### 工作日志
- 在开发工作日志中记录此次修改

## 📌 技术细节

### 引脚复用配置
- **原配置**: PA0(UART4_TX) + PA1(UART4_RX) - GPIO_AF8_UART4
- **新配置**: PA9(USART1_TX) + PA10(USART1_RX) - GPIO_AF7_USART1

### 时钟配置
- **原配置**: RCC_PERIPHCLK_UART4, RCC_USART234578CLKSOURCE_D2PCLK1
- **新配置**: RCC_PERIPHCLK_USART1, RCC_USART16CLKSOURCE_D2PCLK2

### 波特率保持不变
- 921600 bps，8位数据，1停止位，无奇偶校验

## ✅ 验证检查

- [x] 所有源文件中的UART4引用已更新为USART1
- [x] 所有huart4引用已更新为huart1
- [x] GPIO引脚配置已从PA0/PA1更新为PA9/PA10
- [x] 复用功能已从AF8更新为AF7
- [x] 时钟配置已正确更新
- [x] 测试文件已同步更新
- [x] 文档已更新
- [x] 编译检查无错误

## 🎯 影响范围

此修改仅影响串口硬件配置，不影响：
- MELP算法实现
- 应用层逻辑
- 通信协议
- 其他外设配置

## 📝 注意事项

1. 硬件连接需要相应调整，将串口线从PA0/PA1改接到PA9/PA10
2. 如果使用调试器，确保PA9/PA10没有与调试接口冲突
3. 波特率和通信参数保持不变，上位机软件无需修改

## 🔄 回滚方案

如需回滚到原配置，执行相反操作：
1. 将所有USART1改回UART4
2. 将所有huart1改回huart4
3. 将PA9/PA10改回PA0/PA1
4. 将GPIO_AF7_USART1改回GPIO_AF8_UART4
5. 恢复相应的时钟配置
