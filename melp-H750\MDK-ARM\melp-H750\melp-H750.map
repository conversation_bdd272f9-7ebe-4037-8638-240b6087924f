Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32h750xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h750xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h750xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h750xx.o(RESET) refers to startup_stm32h750xx.o(STACK) for __initial_sp
    startup_stm32h750xx.o(RESET) refers to startup_stm32h750xx.o(.text) for Reset_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32h750xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h750xx.o(.text) refers to system_stm32h7xx.o(i.ExitRun0Mode) for ExitRun0Mode
    startup_stm32h750xx.o(.text) refers to system_stm32h7xx.o(i.SystemInit) for SystemInit
    startup_stm32h750xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32h750xx.o(.text) refers to startup_stm32h750xx.o(HEAP) for Heap_Mem
    startup_stm32h750xx.o(.text) refers to startup_stm32h750xx.o(STACK) for Stack_Mem
    main.o(i.Error_Handler) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    main.o(i.Error_Handler) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.MX_ADC1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.MX_ADC1_Init) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    main.o(i.MX_ADC1_Init) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) for HAL_ADCEx_MultiModeConfigChannel
    main.o(i.MX_ADC1_Init) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    main.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.MX_ADC1_Init) refers to main.o(.bss) for .bss
    main.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.MX_GPIO_Init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.MX_GPIO_Init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    main.o(i.MX_USART1_Init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    main.o(i.MX_USART1_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    main.o(i.MX_USART1_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    main.o(i.MX_USART1_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    main.o(i.MX_USART1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.MX_USART1_Init) refers to main.o(.bss) for .bss
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply) for HAL_PWREx_ConfigSupply
    main.o(i.SystemClock_Config) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable) for HAL_MPU_Disable
    main.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion) for HAL_MPU_ConfigRegion
    main.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable) for HAL_MPU_Enable
    main.o(i.main) refers to stm32h7xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to main.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to main.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.main) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_Init) for HAL_DAC_Init
    main.o(i.main) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    main.o(i.main) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.main) refers to main.o(i.MX_USART1_Init) for MX_USART1_Init
    main.o(i.main) refers to app_main.o(i.app_init) for app_init
    main.o(i.main) refers to app_main.o(i.app_run) for app_run
    main.o(i.main) refers to main.o(.bss) for .bss
    stm32h7xx_it.o(i.SysTick_Handler) refers to stm32h7xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32h7xx_hal_msp.o(i.HAL_ADC_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32h7xx_hal_msp.o(i.HAL_ADC_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32h7xx_hal_msp.o(i.HAL_ADC_MspInit) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    stm32h7xx_hal_msp.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    stm32h7xx_hal_msp.o(i.HAL_ADC_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_msp.o(i.HAL_DAC_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32h7xx_hal_msp.o(i.HAL_DAC_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32h7xx_hal_msp.o(i.HAL_DAC_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_msp.o(i.HAL_QSPI_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32h7xx_hal_msp.o(i.HAL_QSPI_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32h7xx_hal_msp.o(i.HAL_QSPI_MspInit) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    stm32h7xx_hal_msp.o(i.HAL_QSPI_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    stm32h7xx_hal_msp.o(i.HAL_QSPI_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_msp.o(i.HAL_UART_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_adc.o(i.ADC_ConfigureBoostMode) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_adc.o(i.ADC_ConfigureBoostMode) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32h7xx_hal_adc.o(i.ADC_ConfigureBoostMode) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32h7xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) refers to hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32h7xx_hal_adc.o(i.ADC_DMAError) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32h7xx_hal_adc.o(i.ADC_Disable) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.ADC_Enable) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32h7xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32h7xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels) for LL_ADC_SetAnalogWDMonitChannels
    stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32h7xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32h7xx_hal_msp.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback) for HAL_ADCEx_EndOfSamplingCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback) for HAL_ADCEx_LevelOutOfWindow2Callback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback) for HAL_ADCEx_LevelOutOfWindow3Callback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback) for HAL_ADCEx_InjectedQueueOverflowCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32h7xx_hal_msp.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32h7xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32h7xx_hal_adc.o(i.ADC_ConfigureBoostMode) for ADC_ConfigureBoostMode
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32h7xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_SetOffset) for LL_ADC_SetOffset
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_FactorLoad) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) for HAL_ADCEx_LinearCalibration_SetValue
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_GetValue) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_GetValue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_SetCalibrationLinearFactor) for LL_ADC_SetCalibrationLinearFactor
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(.data) for uwTickPrio
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(.data) for uwTickPrio
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetOscConfig) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL1ClockFreq) for HAL_RCCEx_GetPLL1ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq) for HAL_RCCEx_GetPLL2ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq) for HAL_RCCEx_GetPLL3ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) for HAL_RCCEx_GetD3PCLK1Freq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config) for RCCEx_PLL2_Config
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config) for RCCEx_PLL3_Config
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) for FLASH_CRC_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) for FLASH_OB_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC) refers to stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) for FLASH_OB_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC) refers to stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) for FLASH_CRC_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32h7xx_hal_hsem.o(i.HAL_HSEM_IRQHandler) refers to stm32h7xx_hal_hsem.o(i.HAL_HSEM_FreeCallback) for HAL_HSEM_FreeCallback
    stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32h7xx_hal_dma.o(.constdata) for .constdata
    stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32h7xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32h7xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_IRQHandler) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init) refers to stm32h7xx_hal_mdma.o(i.MDMA_Init) for MDMA_Init
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) for HAL_MDMA_Abort
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start) refers to stm32h7xx_hal_mdma.o(i.MDMA_SetConfig) for MDMA_SetConfig
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT) refers to stm32h7xx_hal_mdma.o(i.MDMA_SetConfig) for MDMA_SetConfig
    stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_AVDCallback) for HAL_PWREx_AVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP1_Callback) for HAL_PWREx_WKUP1_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP2_Callback) for HAL_PWREx_WKUP2_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP3_Callback) for HAL_PWREx_WKUP3_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP4_Callback) for HAL_PWREx_WKUP4_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP5_Callback) for HAL_PWREx_WKUP5_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP6_Callback) for HAL_PWREx_WKUP6_Callback
    stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32h7xx_hal.o(i.HAL_DeInit) refers to stm32h7xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32h7xx_hal.o(i.HAL_Delay) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal.o(i.HAL_Delay) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTickFreq) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTickPrio) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_IncTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32h7xx_hal.o(i.HAL_Init) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal.o(i.HAL_Init) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_InitTick) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal.o(i.HAL_SetTickFreq) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal.o(i.HAL_SetTickFreq) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32h7xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_DMAError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_i2c.o(i.I2C_DMAError) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dac.o(i.DAC_DMAConvCpltCh1) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32h7xx_hal_dac.o(i.DAC_DMAErrorCh1) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32h7xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32h7xx_hal_dac.o(i.HAL_DAC_ConfigChannel) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dac.o(i.HAL_DAC_DeInit) refers to stm32h7xx_hal_msp.o(i.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32h7xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32h7xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32h7xx_hal_dac.o(i.HAL_DAC_Init) refers to stm32h7xx_hal_msp.o(i.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32h7xx_hal_dac.o(i.HAL_DAC_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) refers to stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32h7xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) refers to stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32h7xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) refers to stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_SelfCalibrate) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) for HAL_MDMA_Abort
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort_IT) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort_IT) for HAL_MDMA_Abort_IT
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort_IT) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_AbortCpltCallback) for HAL_QSPI_AbortCpltCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort_IT) refers to stm32h7xx_hal_qspi.o(i.QSPI_DMAAbortCplt) for QSPI_DMAAbortCplt
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling) refers to stm32h7xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling_IT) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling_IT) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling_IT) refers to stm32h7xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command) refers to stm32h7xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command_IT) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command_IT) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command_IT) refers to stm32h7xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_DeInit) refers to stm32h7xx_hal_msp.o(i.HAL_QSPI_MspDeInit) for HAL_QSPI_MspDeInit
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_FifoThresholdCallback) for HAL_QSPI_FifoThresholdCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_TxCpltCallback) for HAL_QSPI_TxCpltCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_RxCpltCallback) for HAL_QSPI_RxCpltCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_CmdCpltCallback) for HAL_QSPI_CmdCpltCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_AbortCpltCallback) for HAL_QSPI_AbortCpltCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_ErrorCallback) for HAL_QSPI_ErrorCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_StatusMatchCallback) for HAL_QSPI_StatusMatchCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort_IT) for HAL_MDMA_Abort_IT
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_TimeOutCallback) for HAL_QSPI_TimeOutCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.QSPI_DMAAbortCplt) for QSPI_DMAAbortCplt
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Init) refers to stm32h7xx_hal_msp.o(i.HAL_QSPI_MspInit) for HAL_QSPI_MspInit
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Init) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_MemoryMapped) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_MemoryMapped) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_MemoryMapped) refers to stm32h7xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT) for HAL_MDMA_Start_IT
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA) refers to stm32h7xx_hal_qspi.o(i.QSPI_DMARxCplt) for QSPI_DMARxCplt
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA) refers to stm32h7xx_hal_qspi.o(i.QSPI_DMAError) for QSPI_DMAError
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT) for HAL_MDMA_Start_IT
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA) refers to stm32h7xx_hal_qspi.o(i.QSPI_DMATxCplt) for QSPI_DMATxCplt
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA) refers to stm32h7xx_hal_qspi.o(i.QSPI_DMAError) for QSPI_DMAError
    stm32h7xx_hal_qspi.o(i.QSPI_DMAAbortCplt) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_ErrorCallback) for HAL_QSPI_ErrorCallback
    stm32h7xx_hal_qspi.o(i.QSPI_DMAError) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort_IT) for HAL_QSPI_Abort_IT
    stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_ll_delayblock.o(i.DelayBlock_Enable) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_DeInit) refers to stm32h7xx_hal_msp.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback) for HAL_UARTEx_TxFifoEmptyCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback) for HAL_UARTEx_RxFifoFullCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN) for UART_TxISR_8BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN) for UART_TxISR_16BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32h7xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError) refers to hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt) refers to hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) refers to hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) refers to hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) for HAL_RCCEx_GetD3PCLK1Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq) for HAL_RCCEx_GetPLL2ClockFreq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq) for HAL_RCCEx_GetPLL3ClockFreq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_uart.o(.constdata) for .constdata
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) for UART_RxISR_8BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) for UART_RxISR_16BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) refers to stm32h7xx_hal_uart_ex.o(.constdata) for .constdata
    system_stm32h7xx.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx.o(.constdata) for .constdata
    system_stm32h7xx.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx.o(.data) for .data
    app_main.o(i.app_adc_callback) refers to app_melp.o(i.app_melp_process_input) for app_melp_process_input
    app_main.o(i.app_adc_callback) refers to app_main.o(.bss) for .bss
    app_main.o(i.app_button_callback) refers to app_melp.o(i.app_melp_start_recording) for app_melp_start_recording
    app_main.o(i.app_button_callback) refers to hal_adc.o(i.hal_adc_start_continuous) for hal_adc_start_continuous
    app_main.o(i.app_button_callback) refers to hal_adc.o(i.hal_adc_stop_continuous) for hal_adc_stop_continuous
    app_main.o(i.app_button_callback) refers to app_melp.o(i.app_melp_stop_recording) for app_melp_stop_recording
    app_main.o(i.app_button_callback) refers to hal_gpio.o(i.hal_gpio_set_led) for hal_gpio_set_led
    app_main.o(i.app_button_callback) refers to app_main.o(.data) for .data
    app_main.o(i.app_button_callback) refers to app_main.o(.bss) for .bss
    app_main.o(i.app_button_callback) refers to app_main.o(i.app_adc_callback) for app_adc_callback
    app_main.o(i.app_get_state) refers to app_main.o(.data) for .data
    app_main.o(i.app_init) refers to hal_gpio.o(i.hal_gpio_init) for hal_gpio_init
    app_main.o(i.app_init) refers to hal_adc.o(i.hal_adc_init) for hal_adc_init
    app_main.o(i.app_init) refers to hal_dac.o(i.hal_dac_init) for hal_dac_init
    app_main.o(i.app_init) refers to hal_uart.o(i.hal_uart_init) for hal_uart_init
    app_main.o(i.app_init) refers to hal_gpio.o(i.hal_gpio_set_button_callback) for hal_gpio_set_button_callback
    app_main.o(i.app_init) refers to app_melp.o(i.app_melp_init) for app_melp_init
    app_main.o(i.app_init) refers to hal_gpio.o(i.hal_gpio_set_led) for hal_gpio_set_led
    app_main.o(i.app_init) refers to app_main.o(.data) for .data
    app_main.o(i.app_init) refers to app_main.o(i.app_button_callback) for app_button_callback
    app_main.o(i.app_init) refers to app_main.o(.constdata) for .constdata
    app_main.o(i.app_init) refers to app_main.o(.bss) for .bss
    app_main.o(i.app_melp_error_callback) refers to app_main.o(.data) for .data
    app_main.o(i.app_melp_frame_decoded_callback) refers to hal_dac.o(i.hal_dac_write_single) for hal_dac_write_single
    app_main.o(i.app_melp_frame_encoded_callback) refers to hal_uart.o(i.hal_uart_send_async) for hal_uart_send_async
    app_main.o(i.app_melp_frame_encoded_callback) refers to app_main.o(i.app_uart_tx_callback) for app_uart_tx_callback
    app_main.o(i.app_run) refers to hal_gpio.o(i.hal_gpio_button_poll) for hal_gpio_button_poll
    app_main.o(i.app_run) refers to hal_gpio.o(i.hal_gpio_set_led) for hal_gpio_set_led
    app_main.o(i.app_run) refers to hal_gpio.o(i.hal_gpio_toggle_led) for hal_gpio_toggle_led
    app_main.o(i.app_run) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    app_main.o(i.app_run) refers to app_main.o(.data) for .data
    app_main.o(i.app_systick_handler) refers to app_main.o(.data) for .data
    app_main.o(.constdata) refers to app_main.o(i.app_melp_frame_encoded_callback) for app_melp_frame_encoded_callback
    app_main.o(.constdata) refers to app_main.o(i.app_melp_frame_decoded_callback) for app_melp_frame_decoded_callback
    app_main.o(.constdata) refers to app_main.o(i.app_melp_state_changed_callback) for app_melp_state_changed_callback
    app_main.o(.constdata) refers to app_main.o(i.app_melp_error_callback) for app_melp_error_callback
    app_melp.o(i.app_melp_change_state) refers to app_melp.o(.data) for .data
    app_melp.o(i.app_melp_change_state) refers to app_melp.o(.bss) for .bss
    app_melp.o(i.app_melp_decode_frame) refers to melp_decoder.o(i.melp_decoder_decode_frame) for melp_decoder_decode_frame
    app_melp.o(i.app_melp_decode_frame) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    app_melp.o(i.app_melp_decode_frame) refers to app_melp.o(.data) for .data
    app_melp.o(i.app_melp_decode_frame) refers to app_melp.o(.bss) for .bss
    app_melp.o(i.app_melp_deinit) refers to melp_encoder.o(i.melp_encoder_deinit) for melp_encoder_deinit
    app_melp.o(i.app_melp_deinit) refers to melp_decoder.o(i.melp_decoder_deinit) for melp_decoder_deinit
    app_melp.o(i.app_melp_deinit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_melp.o(i.app_melp_deinit) refers to app_melp.o(.data) for .data
    app_melp.o(i.app_melp_encode_frame) refers to melp_encoder.o(i.melp_encoder_encode_frame) for melp_encoder_encode_frame
    app_melp.o(i.app_melp_encode_frame) refers to melp_encoder.o(i.melp_encoder_pack_frame) for melp_encoder_pack_frame
    app_melp.o(i.app_melp_encode_frame) refers to app_melp.o(.data) for .data
    app_melp.o(i.app_melp_encode_frame) refers to app_melp.o(.bss) for .bss
    app_melp.o(i.app_melp_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_melp.o(i.app_melp_init) refers to melp_encoder.o(i.melp_encoder_init) for melp_encoder_init
    app_melp.o(i.app_melp_init) refers to melp_decoder.o(i.melp_decoder_init) for melp_decoder_init
    app_melp.o(i.app_melp_init) refers to melp_encoder.o(i.melp_encoder_deinit) for melp_encoder_deinit
    app_melp.o(i.app_melp_init) refers to app_melp.o(.bss) for .bss
    app_melp.o(i.app_melp_init) refers to app_melp.o(.data) for .data
    app_melp.o(i.app_melp_process_input) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    app_melp.o(i.app_melp_process_input) refers to app_melp.o(i.app_melp_change_state) for app_melp_change_state
    app_melp.o(i.app_melp_process_input) refers to app_melp.o(i.app_melp_encode_frame) for app_melp_encode_frame
    app_melp.o(i.app_melp_process_input) refers to rt_memmove_v6.o(.text) for __aeabi_memmove
    app_melp.o(i.app_melp_process_received_frame) refers to app_melp.o(i.app_melp_change_state) for app_melp_change_state
    app_melp.o(i.app_melp_process_received_frame) refers to app_melp.o(i.app_melp_decode_frame) for app_melp_decode_frame
    app_melp.o(i.app_melp_start_playback) refers to app_melp.o(i.app_melp_change_state) for app_melp_change_state
    app_melp.o(i.app_melp_start_recording) refers to app_melp.o(i.app_melp_change_state) for app_melp_change_state
    app_melp.o(i.app_melp_stop_playback) refers to app_melp.o(i.app_melp_change_state) for app_melp_change_state
    app_melp.o(i.app_melp_stop_recording) refers to app_melp.o(i.app_melp_change_state) for app_melp_change_state
    app_state_machine.o(i.app_sm_deinit) refers to app_state_machine.o(i.app_sm_handle_state_exit) for app_sm_handle_state_exit
    app_state_machine.o(i.app_sm_deinit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_state_machine.o(i.app_sm_deinit) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_force_state) refers to app_state_machine.o(i.app_sm_transition_to) for app_sm_transition_to
    app_state_machine.o(i.app_sm_get_error_name) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_get_event_name) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_get_state_name) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_handle_state_entry) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_handle_state_entry) refers to app_state_machine.o(.bss) for .bss
    app_state_machine.o(i.app_sm_handle_state_exit) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_handle_state_exit) refers to app_state_machine.o(.bss) for .bss
    app_state_machine.o(i.app_sm_handle_timeout) refers to app_state_machine.o(i.app_sm_report_error) for app_sm_report_error
    app_state_machine.o(i.app_sm_handle_timeout) refers to app_state_machine.o(i.app_sm_process_event) for app_sm_process_event
    app_state_machine.o(i.app_sm_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_state_machine.o(i.app_sm_init) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    app_state_machine.o(i.app_sm_init) refers to app_state_machine.o(i.app_sm_handle_state_entry) for app_sm_handle_state_entry
    app_state_machine.o(i.app_sm_init) refers to app_state_machine.o(.bss) for .bss
    app_state_machine.o(i.app_sm_init) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_process_event) refers to app_state_machine.o(i.app_sm_transition_to) for app_sm_transition_to
    app_state_machine.o(i.app_sm_report_error) refers to app_state_machine.o(i.app_sm_process_event) for app_sm_process_event
    app_state_machine.o(i.app_sm_report_error) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_report_error) refers to app_state_machine.o(.bss) for .bss
    app_state_machine.o(i.app_sm_reset_statistics) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_state_machine.o(i.app_sm_transition_to) refers to app_state_machine.o(i.app_sm_is_valid_transition) for app_sm_is_valid_transition
    app_state_machine.o(i.app_sm_transition_to) refers to app_state_machine.o(i.app_sm_handle_state_exit) for app_sm_handle_state_exit
    app_state_machine.o(i.app_sm_transition_to) refers to app_state_machine.o(i.app_sm_handle_state_entry) for app_sm_handle_state_entry
    app_state_machine.o(i.app_sm_transition_to) refers to app_state_machine.o(i.app_sm_report_error) for app_sm_report_error
    app_state_machine.o(i.app_sm_transition_to) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_transition_to) refers to app_state_machine.o(.bss) for .bss
    app_state_machine.o(i.app_sm_update) refers to app_state_machine.o(i.app_sm_handle_timeout) for app_sm_handle_timeout
    app_state_machine.o(i.app_sm_update) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_update) refers to app_state_machine.o(.bss) for .bss
    app_state_machine.o(.data) refers to app_state_machine.o(.conststring) for .conststring
    hal_adc.o(i.HAL_ADC_ConvCpltCallback) refers to main.o(.bss) for hadc1
    hal_adc.o(i.HAL_ADC_ConvCpltCallback) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.HAL_ADC_ConvCpltCallback) refers to hal_adc.o(.bss) for .bss
    hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.bss) for hadc1
    hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) refers to hal_adc.o(.bss) for .bss
    hal_adc.o(i.hal_adc_deinit) refers to hal_adc.o(i.hal_adc_stop_continuous) for hal_adc_stop_continuous
    hal_adc.o(i.hal_adc_deinit) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit) for HAL_ADC_DeInit
    hal_adc.o(i.hal_adc_deinit) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.hal_adc_deinit) refers to main.o(.bss) for hadc1
    hal_adc.o(i.hal_adc_init) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) for HAL_ADCEx_Calibration_Start
    hal_adc.o(i.hal_adc_init) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.hal_adc_init) refers to main.o(.bss) for hadc1
    hal_adc.o(i.hal_adc_is_ready) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_GetState) for HAL_ADC_GetState
    hal_adc.o(i.hal_adc_is_ready) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.hal_adc_is_ready) refers to main.o(.bss) for hadc1
    hal_adc.o(i.hal_adc_read_single) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    hal_adc.o(i.hal_adc_read_single) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_PollForConversion) for HAL_ADC_PollForConversion
    hal_adc.o(i.hal_adc_read_single) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    hal_adc.o(i.hal_adc_read_single) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Stop) for HAL_ADC_Stop
    hal_adc.o(i.hal_adc_read_single) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.hal_adc_read_single) refers to main.o(.bss) for hadc1
    hal_adc.o(i.hal_adc_start_continuous) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    hal_adc.o(i.hal_adc_start_continuous) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.hal_adc_start_continuous) refers to hal_adc.o(.bss) for .bss
    hal_adc.o(i.hal_adc_start_continuous) refers to main.o(.bss) for hadc1
    hal_adc.o(i.hal_adc_stop_continuous) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    hal_adc.o(i.hal_adc_stop_continuous) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.hal_adc_stop_continuous) refers to main.o(.bss) for hadc1
    hal_dac.o(i.HAL_DAC_ConvCpltCallback) refers to hal_dac.o(i.hal_dac_dma_callback) for hal_dac_dma_callback
    hal_dac.o(i.HAL_DAC_ConvHalfCpltCallback) refers to hal_dac.o(i.hal_dac_dma_callback) for hal_dac_dma_callback
    hal_dac.o(i.hal_dac_deinit) refers to hal_dac.o(i.hal_dac_stop_continuous) for hal_dac_stop_continuous
    hal_dac.o(i.hal_dac_deinit) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_Stop) for HAL_DAC_Stop
    hal_dac.o(i.hal_dac_deinit) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_DeInit) for HAL_DAC_DeInit
    hal_dac.o(i.hal_dac_deinit) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_deinit) refers to main.o(.bss) for hdac1
    hal_dac.o(i.hal_dac_dma_callback) refers to main.o(.bss) for hdac1
    hal_dac.o(i.hal_dac_dma_callback) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_init) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_Start) for HAL_DAC_Start
    hal_dac.o(i.hal_dac_init) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_init) refers to main.o(.bss) for hdac1
    hal_dac.o(i.hal_dac_is_ready) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_GetState) for HAL_DAC_GetState
    hal_dac.o(i.hal_dac_is_ready) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_is_ready) refers to main.o(.bss) for hdac1
    hal_dac.o(i.hal_dac_mute) refers to hal_dac.o(i.hal_dac_write_single) for hal_dac_write_single
    hal_dac.o(i.hal_dac_start_continuous) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) for HAL_DAC_Start_DMA
    hal_dac.o(i.hal_dac_start_continuous) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_start_continuous) refers to main.o(.bss) for hdac1
    hal_dac.o(i.hal_dac_stop_continuous) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_Stop_DMA) for HAL_DAC_Stop_DMA
    hal_dac.o(i.hal_dac_stop_continuous) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_stop_continuous) refers to main.o(.bss) for hdac1
    hal_dac.o(i.hal_dac_write_single) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    hal_dac.o(i.hal_dac_write_single) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_write_single) refers to main.o(.bss) for hdac1
    hal_gpio.o(i.HAL_GPIO_EXTI_Callback) refers to hal_gpio.o(i.hal_gpio_read_button) for hal_gpio_read_button
    hal_gpio.o(i.HAL_GPIO_EXTI_Callback) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_button_poll) refers to hal_gpio.o(i.hal_gpio_read_button) for hal_gpio_read_button
    hal_gpio.o(i.hal_gpio_button_poll) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_deinit) refers to hal_gpio.o(i.hal_gpio_disable_button_interrupt) for hal_gpio_disable_button_interrupt
    hal_gpio.o(i.hal_gpio_deinit) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_disable_button_interrupt) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    hal_gpio.o(i.hal_gpio_disable_button_interrupt) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    hal_gpio.o(i.hal_gpio_disable_button_interrupt) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    hal_gpio.o(i.hal_gpio_disable_button_interrupt) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_enable_button_interrupt) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    hal_gpio.o(i.hal_gpio_enable_button_interrupt) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    hal_gpio.o(i.hal_gpio_enable_button_interrupt) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    hal_gpio.o(i.hal_gpio_enable_button_interrupt) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    hal_gpio.o(i.hal_gpio_enable_button_interrupt) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_get_led_state) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    hal_gpio.o(i.hal_gpio_get_led_state) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_init) refers to hal_gpio.o(i.hal_gpio_read_button) for hal_gpio_read_button
    hal_gpio.o(i.hal_gpio_init) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_read_button) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    hal_gpio.o(i.hal_gpio_read_button) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_set_button_callback) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_set_led) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    hal_gpio.o(i.hal_gpio_set_led) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_toggle_led) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    hal_gpio.o(i.hal_gpio_toggle_led) refers to hal_gpio.o(.data) for .data
    hal_uart.o(i.HAL_UARTEx_RxEventCallback) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    hal_uart.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.bss) for huart1
    hal_uart.o(i.HAL_UARTEx_RxEventCallback) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.HAL_UARTEx_RxEventCallback) refers to hal_uart.o(.bss) for .bss
    hal_uart.o(i.HAL_UART_ErrorCallback) refers to main.o(.bss) for huart1
    hal_uart.o(i.HAL_UART_ErrorCallback) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.HAL_UART_RxCpltCallback) refers to hal_uart.o(i.hal_uart_rx_dma_callback) for hal_uart_rx_dma_callback
    hal_uart.o(i.HAL_UART_TxCpltCallback) refers to main.o(.bss) for huart1
    hal_uart.o(i.HAL_UART_TxCpltCallback) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_deinit) refers to hal_uart.o(i.hal_uart_stop_receive) for hal_uart_stop_receive
    hal_uart.o(i.hal_uart_deinit) refers to stm32h7xx_hal_uart.o(i.HAL_UART_DeInit) for HAL_UART_DeInit
    hal_uart.o(i.hal_uart_deinit) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_deinit) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_init) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_is_receiving) refers to stm32h7xx_hal_uart.o(i.HAL_UART_GetState) for HAL_UART_GetState
    hal_uart.o(i.hal_uart_is_receiving) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_is_receiving) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_is_tx_ready) refers to stm32h7xx_hal_uart.o(i.HAL_UART_GetState) for HAL_UART_GetState
    hal_uart.o(i.hal_uart_is_tx_ready) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_is_tx_ready) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_rx_dma_callback) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_rx_dma_callback) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_rx_dma_callback) refers to hal_uart.o(.bss) for .bss
    hal_uart.o(i.hal_uart_send_async) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    hal_uart.o(i.hal_uart_send_async) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_send_async) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_send_sync) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    hal_uart.o(i.hal_uart_send_sync) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_send_sync) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_set_error_callback) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_start_receive) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    hal_uart.o(i.hal_uart_start_receive) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_start_receive) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_stop_receive) refers to stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    hal_uart.o(i.hal_uart_stop_receive) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_stop_receive) refers to main.o(.bss) for huart1
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_decoder_unpack_frame) for melp_decoder_unpack_frame
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_dequantize_parameters) for melp_dequantize_parameters
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_lsf_to_lpc) for melp_lsf_to_lpc
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_generate_excitation) for melp_generate_excitation
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_lpc_synthesis) for melp_lpc_synthesis
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_adaptive_spectral_enhancement) for melp_adaptive_spectral_enhancement
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_post_filter) for melp_post_filter
    melp_decoder.o(i.melp_decoder_decode_frame) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    melp_decoder.o(i.melp_decoder_deinit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    melp_decoder.o(i.melp_decoder_frame_erasure) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    melp_decoder.o(i.melp_decoder_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    melp_decoder.o(i.melp_decoder_init) refers to melp_decoder.o(.data) for .data
    melp_decoder.o(i.melp_generate_excitation) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    melp_decoder.o(i.melp_generate_excitation) refers to melp_decoder.o(.data) for .data
    melp_decoder.o(i.melp_lsf_to_lpc) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_fast) refers to melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) for melp_decoder_decode_frame_optimized
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_decoder.o(i.melp_decoder_unpack_frame) for melp_decoder_unpack_frame
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_decoder.o(i.melp_dequantize_parameters) for melp_dequantize_parameters
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_decoder.o(i.melp_lsf_to_lpc) for melp_lsf_to_lpc
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_decoder_optimized.o(i.melp_generate_excitation_optimized) for melp_generate_excitation_optimized
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_lpc_synthesis) for melp_dsp_lpc_synthesis
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_vector_scale) for melp_dsp_vector_scale
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_post_filter) for melp_dsp_post_filter
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_decoder_optimized.o(.bss) for .bss
    melp_decoder_optimized.o(i.melp_decoder_deinit_fast) refers to melp_decoder_optimized.o(i.melp_decoder_deinit_optimized) for melp_decoder_deinit_optimized
    melp_decoder_optimized.o(i.melp_decoder_deinit_optimized) refers to melp_dsp.o(i.melp_dsp_deinit) for melp_dsp_deinit
    melp_decoder_optimized.o(i.melp_decoder_deinit_optimized) refers to melp_decoder.o(i.melp_decoder_deinit) for melp_decoder_deinit
    melp_decoder_optimized.o(i.melp_decoder_deinit_optimized) refers to melp_decoder_optimized.o(.data) for .data
    melp_decoder_optimized.o(i.melp_decoder_deinit_optimized) refers to melp_decoder_optimized.o(.bss) for .bss
    melp_decoder_optimized.o(i.melp_decoder_init_fast) refers to melp_decoder_optimized.o(i.melp_decoder_init_optimized) for melp_decoder_init_optimized
    melp_decoder_optimized.o(i.melp_decoder_init_optimized) refers to melp_decoder.o(i.melp_decoder_init) for melp_decoder_init
    melp_decoder_optimized.o(i.melp_decoder_init_optimized) refers to melp_dsp.o(i.melp_dsp_init) for melp_dsp_init
    melp_decoder_optimized.o(i.melp_decoder_init_optimized) refers to melp_decoder_optimized.o(.data) for .data
    melp_decoder_optimized.o(i.melp_decoder_init_optimized) refers to melp_decoder_optimized.o(.bss) for .bss
    melp_decoder_optimized.o(i.melp_generate_excitation_optimized) refers to melp_decoder_optimized.o(i.melp_generate_pulse_train_optimized) for melp_generate_pulse_train_optimized
    melp_decoder_optimized.o(i.melp_generate_excitation_optimized) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    melp_decoder_optimized.o(i.melp_generate_excitation_optimized) refers to melp_dsp.o(i.melp_dsp_vector_scale) for melp_dsp_vector_scale
    melp_decoder_optimized.o(i.melp_generate_excitation_optimized) refers to melp_decoder_optimized.o(.data) for .data
    melp_decoder_optimized.o(i.melp_generate_pulse_train_optimized) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    melp_decoder_optimized.o(i.melp_generate_pulse_train_optimized) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    melp_dsp.o(i.melp_dsp_deinit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    melp_dsp.o(i.melp_dsp_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    melp_dsp.o(i.melp_dsp_spectral_analysis) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    melp_dsp.o(i.melp_dsp_spectral_analysis) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    melp_dsp.o(i.melp_dsp_spectral_analysis) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    melp_dsp.o(i.melp_dsp_spectral_analysis) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    melp_encoder.o(i.melp_encoder_deinit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    melp_encoder.o(i.melp_encoder_encode_frame) refers to melp_encoder.o(i.melp_lpc_analysis) for melp_lpc_analysis
    melp_encoder.o(i.melp_encoder_encode_frame) refers to melp_encoder.o(i.melp_lpc_to_lsf) for melp_lpc_to_lsf
    melp_encoder.o(i.melp_encoder_encode_frame) refers to melp_encoder.o(i.melp_pitch_estimation) for melp_pitch_estimation
    melp_encoder.o(i.melp_encoder_encode_frame) refers to melp_encoder.o(i.melp_voicing_analysis) for melp_voicing_analysis
    melp_encoder.o(i.melp_encoder_encode_frame) refers to melp_encoder.o(i.melp_quantize_parameters) for melp_quantize_parameters
    melp_encoder.o(i.melp_encoder_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    melp_encoder.o(i.melp_encoder_init) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    melp_encoder.o(i.melp_encoder_init) refers to melp_encoder.o(.data) for .data
    melp_encoder.o(i.melp_encoder_init) refers to melp_encoder.o(.bss) for .bss
    melp_encoder.o(i.melp_lpc_analysis) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    melp_encoder.o(i.melp_lpc_analysis) refers to melp_encoder.o(i.melp_levinson_durbin) for melp_levinson_durbin
    melp_encoder.o(i.melp_lpc_analysis) refers to melp_encoder.o(.bss) for .bss
    melp_encoder_optimized.o(i.melp_encoder_deinit_fast) refers to melp_encoder_optimized.o(i.melp_encoder_deinit_optimized) for melp_encoder_deinit_optimized
    melp_encoder_optimized.o(i.melp_encoder_deinit_optimized) refers to melp_dsp.o(i.melp_dsp_deinit) for melp_dsp_deinit
    melp_encoder_optimized.o(i.melp_encoder_deinit_optimized) refers to melp_encoder.o(i.melp_encoder_deinit) for melp_encoder_deinit
    melp_encoder_optimized.o(i.melp_encoder_deinit_optimized) refers to melp_encoder_optimized.o(.bss) for .bss
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_fast) refers to melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) for melp_encoder_encode_frame_optimized
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_preemphasis) for melp_dsp_preemphasis
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_windowing) for melp_dsp_windowing
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_autocorrelation) for melp_dsp_autocorrelation
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_levinson_durbin) for melp_dsp_levinson_durbin
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_encoder.o(i.melp_lpc_to_lsf) for melp_lpc_to_lsf
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_pitch_estimation) for melp_dsp_pitch_estimation
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_encoder_optimized.o(i.melp_voicing_analysis_optimized) for melp_voicing_analysis_optimized
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_encoder.o(i.melp_quantize_parameters) for melp_quantize_parameters
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_encoder_optimized.o(.bss) for .bss
    melp_encoder_optimized.o(i.melp_encoder_init_fast) refers to melp_encoder_optimized.o(i.melp_encoder_init_optimized) for melp_encoder_init_optimized
    melp_encoder_optimized.o(i.melp_encoder_init_optimized) refers to melp_encoder.o(i.melp_encoder_init) for melp_encoder_init
    melp_encoder_optimized.o(i.melp_encoder_init_optimized) refers to melp_dsp.o(i.melp_dsp_init) for melp_dsp_init
    melp_encoder_optimized.o(i.melp_encoder_init_optimized) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    melp_encoder_optimized.o(i.melp_encoder_init_optimized) refers to melp_encoder_optimized.o(.bss) for .bss
    melp_encoder_optimized.o(i.melp_encoder_init_optimized) refers to melp_encoder_optimized.o(.data) for .data
    melp_encoder_optimized.o(i.melp_voicing_analysis_optimized) refers to melp_dsp.o(i.melp_dsp_dot_product) for melp_dsp_dot_product
    melp_encoder_optimized.o(i.melp_voicing_analysis_optimized) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    melp_encoder_optimized.o(i.melp_voicing_analysis_optimized) refers to melp_dsp.o(i.melp_dsp_spectral_analysis) for melp_dsp_spectral_analysis
    melp_encoder_optimized.o(i.melp_voicing_analysis_optimized) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    melp_encoder_optimized.o(i.melp_voicing_analysis_optimized) refers to melp_encoder_optimized.o(.bss) for .bss
    comm_protocol.o(i.comm_protocol_deinit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    comm_protocol.o(i.comm_protocol_deinit) refers to comm_protocol.o(.data) for .data
    comm_protocol.o(i.comm_protocol_get_stats) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    comm_protocol.o(i.comm_protocol_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    comm_protocol.o(i.comm_protocol_init) refers to comm_protocol.o(.bss) for .bss
    comm_protocol.o(i.comm_protocol_init) refers to comm_protocol.o(.data) for .data
    comm_protocol.o(i.comm_protocol_process_received_data) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    comm_protocol.o(i.comm_protocol_process_received_data) refers to rt_memmove_v6.o(.text) for __aeabi_memmove
    comm_protocol.o(i.comm_protocol_process_received_data) refers to comm_protocol.o(i.comm_protocol_validate_frame) for comm_protocol_validate_frame
    comm_protocol.o(i.comm_protocol_process_received_data) refers to comm_protocol.o(i.comm_protocol_send_ack) for comm_protocol_send_ack
    comm_protocol.o(i.comm_protocol_process_received_data) refers to comm_protocol.o(i.comm_protocol_send_nack) for comm_protocol_send_nack
    comm_protocol.o(i.comm_protocol_process_received_data) refers to comm_protocol.o(.bss) for .bss
    comm_protocol.o(i.comm_protocol_process_received_data) refers to comm_protocol.o(.data) for .data
    comm_protocol.o(i.comm_protocol_reset_stats) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    comm_protocol.o(i.comm_protocol_send_ack) refers to comm_protocol.o(i.comm_protocol_send_frame) for comm_protocol_send_frame
    comm_protocol.o(i.comm_protocol_send_data) refers to comm_protocol.o(i.comm_protocol_send_frame) for comm_protocol_send_frame
    comm_protocol.o(i.comm_protocol_send_frame) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    comm_protocol.o(i.comm_protocol_send_frame) refers to comm_protocol.o(i.comm_protocol_calculate_crc16) for comm_protocol_calculate_crc16
    comm_protocol.o(i.comm_protocol_send_frame) refers to comm_protocol.o(.data) for .data
    comm_protocol.o(i.comm_protocol_send_frame) refers to comm_protocol.o(.bss) for .bss
    comm_protocol.o(i.comm_protocol_send_heartbeat) refers to comm_protocol.o(i.comm_protocol_send_frame) for comm_protocol_send_frame
    comm_protocol.o(i.comm_protocol_send_nack) refers to comm_protocol.o(i.comm_protocol_send_frame) for comm_protocol_send_frame
    comm_protocol.o(i.comm_protocol_update) refers to comm_protocol.o(.data) for .data
    comm_protocol.o(i.comm_protocol_update) refers to comm_protocol.o(.bss) for .bss
    comm_protocol.o(i.comm_protocol_validate_frame) refers to comm_protocol.o(i.comm_protocol_calculate_crc16) for comm_protocol_calculate_crc16
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memmove_v6.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    rt_memmove_v6.o(.text) refers to rt_memmove_w.o(.text) for __memmove_aligned
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    atan2f.o(i.__hardfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to _rserrno.o(.text) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    cosf.o(i.__hardfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to _rserrno.o(.text) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to _rserrno.o(.text) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    fmodf.o(i.__hardfp_fmodf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf.o(i.__hardfp_fmodf) refers to frem_clz.o(x$fpl$frem) for _frem
    fmodf.o(i.__hardfp_fmodf) refers to _rserrno.o(.text) for __set_errno
    fmodf.o(i.__hardfp_fmodf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    fmodf.o(i.__softfp_fmodf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf.o(i.__softfp_fmodf) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    fmodf.o(i.fmodf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf.o(i.fmodf) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    fmodf_x.o(i.____hardfp_fmodf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf_x.o(i.____hardfp_fmodf$lsc) refers to frem_clz.o(x$fpl$frem) for _frem
    fmodf_x.o(i.____hardfp_fmodf$lsc) refers to _rserrno.o(.text) for __set_errno
    fmodf_x.o(i.____softfp_fmodf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf_x.o(i.____softfp_fmodf$lsc) refers to fmodf_x.o(i.____hardfp_fmodf$lsc) for ____hardfp_fmodf$lsc
    fmodf_x.o(i.__fmodf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf_x.o(i.__fmodf$lsc) refers to fmodf_x.o(i.____hardfp_fmodf$lsc) for ____hardfp_fmodf$lsc
    powf.o(i.__hardfp_powf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf.o(i.__hardfp_powf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    powf.o(i.__hardfp_powf) refers to _rserrno.o(.text) for __set_errno
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_overflow) for __mathlib_flt_overflow
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    powf.o(i.__hardfp_powf) refers to powf.o(.constdata) for .constdata
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    powf.o(i.__softfp_powf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf.o(i.__softfp_powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(i.powf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf.o(i.powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers to _rserrno.o(.text) for __set_errno
    powf_x.o(i.____hardfp_powf$lsc) refers to powf_x.o(.constdata) for .constdata
    powf_x.o(i.____hardfp_powf$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf_x.o(i.____softfp_powf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.____softfp_powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(i.__powf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.__powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    rt_memmove_w.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    frem_clz.o(x$fpl$frem) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frem_clz.o(x$fpl$frem) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32h750xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_it.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_it.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_it.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_msp.o(i.HAL_ADC_MspDeInit), (40 bytes).
    Removing stm32h7xx_hal_msp.o(i.HAL_DAC_MspDeInit), (40 bytes).
    Removing stm32h7xx_hal_msp.o(i.HAL_QSPI_MspDeInit), (72 bytes).
    Removing stm32h7xx_hal_msp.o(i.HAL_QSPI_MspInit), (240 bytes).
    Removing stm32h7xx_hal_msp.o(i.HAL_UART_MspDeInit), (44 bytes).
    Removing stm32h7xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (752 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit), (564 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler), (656 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_PollForConversion), (292 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_PollForEvent), (198 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Start), (264 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Start_IT), (392 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Stop), (62 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32h7xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels), (44 bytes).
    Removing stm32h7xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_GetValue), (22 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue), (122 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue), (48 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator), (32 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue), (48 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EnterADCDeepPowerDownMode), (36 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (1484 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (46 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (276 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (252 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (312 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (84 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (94 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_FactorLoad), (84 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_GetValue), (164 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue), (248 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (40 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (280 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (240 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA), (260 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop), (92 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA), (138 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT), (102 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel), (12 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing), (8 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.LL_ADC_SetCalibrationLinearFactor), (48 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.LL_ADC_SetOffset), (22 bytes).
    Removing stm32h7xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit), (496 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (16 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (16 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (92 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (308 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (148 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (108 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (40 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (136 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (116 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (28 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (52 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq), (36 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq), (68 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (452 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_KerWakeUpStopCLKConfig), (20 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_WWDGxSysResetConfig), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_WakeUpStopCLKConfig), (20 bytes).
    Removing stm32h7xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation), (112 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation), (80 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation), (120 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (204 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Lock), (28 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (44 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (28 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Program), (148 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT), (128 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32h7xx_hal_flash.o(.bss), (28 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (36 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (132 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC), (188 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (196 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (144 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Lock_Bank1), (20 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (164 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (272 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Unlock_Bank1), (44 bytes).
    Removing stm32h7xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit), (368 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32h7xx_hal_hsem.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_hsem.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_hsem.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_ActivateNotification), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_DeactivateNotification), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_FastTake), (32 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_FreeCallback), (2 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_GetClearKey), (12 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_IsSemTaken), (20 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_Release), (20 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_ReleaseAll), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_SetClearKey), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_Take), (40 bytes).
    Removing stm32h7xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (180 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask), (156 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask), (116 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam), (84 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT), (644 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit), (488 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler), (1768 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Init), (964 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (1118 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (90 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Start), (368 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (104 bytes).
    Removing stm32h7xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (160 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (144 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxRequestGenerator), (176 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxSync), (216 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_DisableMuxRequestGenerator), (26 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_EnableMuxRequestGenerator), (26 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MUX_IRQHandler), (100 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (640 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (1128 bytes).
    Removing stm32h7xx_hal_mdma.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort), (116 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort_IT), (38 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_ConfigPostRequestMask), (88 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_DeInit), (86 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GenerateSWRequest), (52 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GetError), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GetState), (6 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_IRQHandler), (376 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init), (94 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_AddNode), (208 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_CreateNode), (268 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_DisableCircularMode), (78 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_EnableCircularMode), (78 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_RemoveNode), (188 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer), (246 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_RegisterCallback), (88 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start), (102 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT), (154 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_UnRegisterCallback), (104 bytes).
    Removing stm32h7xx_hal_mdma.o(i.MDMA_Init), (176 bytes).
    Removing stm32h7xx_hal_mdma.o(i.MDMA_SetConfig), (128 bytes).
    Removing stm32h7xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (136 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DeInit), (2 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (24 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (28 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (48 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (80 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (34 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_AVDCallback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearPendingEvent), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearWakeupFlag), (28 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigAVD), (132 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigD3Domain), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlStopModeVoltageScaling), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (204 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableAVD), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableMonitoring), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBVoltageDetector), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableWakeUpPin), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableAVD), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableMonitoring), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBVoltageDetector), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableWakeUpPin), (136 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTANDBYMode), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOPMode), (100 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetStopModeVoltageRange), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetSupplyConfig), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetTemperatureLevel), (32 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetVBATLevel), (32 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetWakeupFlag), (12 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler), (96 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler), (124 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP1_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP2_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP3_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP4_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP5_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP6_Callback), (2 bytes).
    Removing stm32h7xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32h7xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DeInit), (120 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableCompensationCell), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D1_ClearFlag), (22 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D1_EventInputConfig), (66 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D3_EventInputConfig), (70 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_EdgeConfig), (58 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_GenerateSWInterrupt), (24 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableCompensationCell), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetFMCMemorySwappingConfig), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32h7xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_AnalogSwitchConfig), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CM7BootAddConfig), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CompensationCodeConfig), (24 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CompensationCodeSelect), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableBOOST), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableIOSpeedOptimize), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_ETHInterfaceSelect), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableBOOST), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableIOSpeedOptimize), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SetFMCMemorySwappingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32h7xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_DeInit), (52 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (54 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (104 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (16 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (40 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Init), (192 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (296 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (128 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (292 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (300 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (132 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (324 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (160 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (400 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (224 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (332 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (172 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (356 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (168 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (344 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (176 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (312 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (232 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (96 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (368 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (208 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (364 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (208 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (384 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (128 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (26 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAAbort), (20 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAError), (332 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (82 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (82 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (164 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (30 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ), (96 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ), (136 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR), (34 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt), (148 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITError), (288 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt), (100 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt), (244 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt), (80 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt), (592 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt), (114 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT), (304 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA), (316 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT), (328 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead), (100 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (100 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA), (524 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_TransferConfig), (48 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback), (42 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout), (124 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (168 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (88 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout), (92 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter), (88 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter), (84 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableFastModePlus), (40 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableWakeUp), (80 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus), (40 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableWakeUp), (80 bytes).
    Removing stm32h7xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (200 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (28 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (236 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetPending), (32 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (48 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (18 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (268 bytes).
    Removing stm32h7xx_hal_dac.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dac.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dac.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dac.o(i.DAC_DMAConvCpltCh1), (16 bytes).
    Removing stm32h7xx_hal_dac.o(i.DAC_DMAErrorCh1), (24 bytes).
    Removing stm32h7xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1), (10 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1), (2 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1), (2 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1), (2 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_DeInit), (30 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1), (2 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_GetError), (4 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_GetState), (4 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_GetValue), (12 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_IRQHandler), (102 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_MspInit), (2 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA), (224 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_Stop), (32 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_Stop_DMA), (90 bytes).
    Removing stm32h7xx_hal_dac_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dac_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dac_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2), (16 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.DAC_DMAErrorCh2), (24 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2), (10 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2), (2 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2), (2 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2), (2 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualGetValue), (12 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualSetValue), (30 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart), (96 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA), (220 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStop), (34 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStop_DMA), (88 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2), (2 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_GetTrimOffset), (18 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_NoiseWaveGenerate), (62 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_SelfCalibrate), (320 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_SetUserTrimming), (70 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_TriangleWaveGenerate), (62 bytes).
    Removing stm32h7xx_hal_qspi.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_qspi.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_qspi.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort), (142 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_AbortCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort_IT), (128 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling), (172 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling_IT), (162 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_CmdCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command), (134 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command_IT), (128 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_DeInit), (36 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_FifoThresholdCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_GetError), (4 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_GetFifoThreshold), (12 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_GetState), (6 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler), (484 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Init), (172 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_MemoryMapped), (140 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_MspInit), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive), (190 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA), (292 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive_IT), (120 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_RxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_SetFifoThreshold), (66 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_SetFlashID), (58 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_SetTimeout), (4 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_StatusMatchCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_TimeOutCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit), (182 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA), (268 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit_IT), (110 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_TxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.QSPI_Config), (408 bytes).
    Removing stm32h7xx_hal_qspi.o(i.QSPI_DMAAbortCplt), (58 bytes).
    Removing stm32h7xx_hal_qspi.o(i.QSPI_DMAError), (30 bytes).
    Removing stm32h7xx_hal_qspi.o(i.QSPI_DMARxCplt), (18 bytes).
    Removing stm32h7xx_hal_qspi.o(i.QSPI_DMATxCplt), (18 bytes).
    Removing stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout), (72 bytes).
    Removing stm32h7xx_ll_delayblock.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_ll_delayblock.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_ll_delayblock.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_ll_delayblock.o(i.DelayBlock_Configure), (24 bytes).
    Removing stm32h7xx_ll_delayblock.o(i.DelayBlock_Disable), (8 bytes).
    Removing stm32h7xx_ll_delayblock.o(i.DelayBlock_Enable), (150 bytes).
    Removing stm32h7xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init), (116 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_LIN_Init), (140 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_LIN_SendBreak), (50 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (52 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (52 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init), (138 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Abort), (256 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive), (172 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (188 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit), (140 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (152 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT), (292 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAPause), (116 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAResume), (108 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop), (146 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DeInit), (70 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DisableReceiverTimeout), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_EnableReceiverTimeout), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_GetState), (14 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler), (924 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Receive), (232 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA), (72 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT), (72 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_ReceiverTimeout_Config), (24 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Transmit), (176 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT), (152 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError), (16 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt), (130 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback), (68 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt), (32 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (40 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (40 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT), (200 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN), (416 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT), (200 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN), (416 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA), (168 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT), (272 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT), (82 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN), (106 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT), (78 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN), (102 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (48 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (140 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (46 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode), (72 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (46 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle), (304 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback), (2 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (140 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback), (2 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback), (2 bytes).
    Removing system_stm32h7xx.o(.rev16_text), (4 bytes).
    Removing system_stm32h7xx.o(.revsh_text), (4 bytes).
    Removing system_stm32h7xx.o(.rrx_text), (6 bytes).
    Removing system_stm32h7xx.o(i.SystemCoreClockUpdate), (320 bytes).
    Removing app_main.o(.rev16_text), (4 bytes).
    Removing app_main.o(.revsh_text), (4 bytes).
    Removing app_main.o(.rrx_text), (6 bytes).
    Removing app_main.o(i.app_get_state), (12 bytes).
    Removing app_main.o(i.app_systick_handler), (20 bytes).
    Removing app_melp.o(i.app_melp_decode_frame), (108 bytes).
    Removing app_melp.o(i.app_melp_deinit), (56 bytes).
    Removing app_melp.o(i.app_melp_get_state), (20 bytes).
    Removing app_melp.o(i.app_melp_get_statistics), (46 bytes).
    Removing app_melp.o(i.app_melp_process_received_frame), (116 bytes).
    Removing app_melp.o(i.app_melp_reset_statistics), (42 bytes).
    Removing app_melp.o(i.app_melp_start_playback), (40 bytes).
    Removing app_melp.o(i.app_melp_stop_playback), (34 bytes).
    Removing app_state_machine.o(i.app_sm_deinit), (48 bytes).
    Removing app_state_machine.o(i.app_sm_force_state), (22 bytes).
    Removing app_state_machine.o(i.app_sm_get_error_name), (28 bytes).
    Removing app_state_machine.o(i.app_sm_get_event_name), (28 bytes).
    Removing app_state_machine.o(i.app_sm_get_state), (16 bytes).
    Removing app_state_machine.o(i.app_sm_get_state_name), (28 bytes).
    Removing app_state_machine.o(i.app_sm_get_statistics), (52 bytes).
    Removing app_state_machine.o(i.app_sm_handle_state_entry), (92 bytes).
    Removing app_state_machine.o(i.app_sm_handle_state_exit), (36 bytes).
    Removing app_state_machine.o(i.app_sm_handle_timeout), (70 bytes).
    Removing app_state_machine.o(i.app_sm_init), (80 bytes).
    Removing app_state_machine.o(i.app_sm_is_valid_transition), (100 bytes).
    Removing app_state_machine.o(i.app_sm_process_event), (170 bytes).
    Removing app_state_machine.o(i.app_sm_report_error), (80 bytes).
    Removing app_state_machine.o(i.app_sm_reset_statistics), (58 bytes).
    Removing app_state_machine.o(i.app_sm_set_debug_mode), (18 bytes).
    Removing app_state_machine.o(i.app_sm_transition_to), (108 bytes).
    Removing app_state_machine.o(i.app_sm_update), (84 bytes).
    Removing app_state_machine.o(.bss), (20 bytes).
    Removing app_state_machine.o(.conststring), (493 bytes).
    Removing app_state_machine.o(.data), (144 bytes).
    Removing hal_adc.o(.rev16_text), (4 bytes).
    Removing hal_adc.o(.revsh_text), (4 bytes).
    Removing hal_adc.o(.rrx_text), (6 bytes).
    Removing hal_adc.o(i.hal_adc_deinit), (44 bytes).
    Removing hal_adc.o(i.hal_adc_get_vref_mv), (6 bytes).
    Removing hal_adc.o(i.hal_adc_is_ready), (36 bytes).
    Removing hal_adc.o(i.hal_adc_read_single), (68 bytes).
    Removing hal_adc.o(i.hal_adc_to_voltage_mv), (18 bytes).
    Removing hal_dac.o(.rev16_text), (4 bytes).
    Removing hal_dac.o(.revsh_text), (4 bytes).
    Removing hal_dac.o(.rrx_text), (6 bytes).
    Removing hal_dac.o(i.HAL_DAC_ConvCpltCallback), (4 bytes).
    Removing hal_dac.o(i.HAL_DAC_ConvHalfCpltCallback), (4 bytes).
    Removing hal_dac.o(i.hal_dac_deinit), (56 bytes).
    Removing hal_dac.o(i.hal_dac_dma_callback), (28 bytes).
    Removing hal_dac.o(i.hal_dac_is_ready), (32 bytes).
    Removing hal_dac.o(i.hal_dac_mute), (6 bytes).
    Removing hal_dac.o(i.hal_dac_start_continuous), (52 bytes).
    Removing hal_dac.o(i.hal_dac_stop_continuous), (40 bytes).
    Removing hal_dac.o(i.hal_dac_voltage_to_value), (26 bytes).
    Removing hal_gpio.o(.rev16_text), (4 bytes).
    Removing hal_gpio.o(.revsh_text), (4 bytes).
    Removing hal_gpio.o(.rrx_text), (6 bytes).
    Removing hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (40 bytes).
    Removing hal_gpio.o(i.hal_gpio_deinit), (28 bytes).
    Removing hal_gpio.o(i.hal_gpio_disable_button_interrupt), (60 bytes).
    Removing hal_gpio.o(i.hal_gpio_enable_button_interrupt), (72 bytes).
    Removing hal_gpio.o(i.hal_gpio_get_led_state), (40 bytes).
    Removing hal_uart.o(.rev16_text), (4 bytes).
    Removing hal_uart.o(.revsh_text), (4 bytes).
    Removing hal_uart.o(.rrx_text), (6 bytes).
    Removing hal_uart.o(i.HAL_UARTEx_RxEventCallback), (52 bytes).
    Removing hal_uart.o(i.HAL_UART_RxCpltCallback), (4 bytes).
    Removing hal_uart.o(i.hal_uart_deinit), (48 bytes).
    Removing hal_uart.o(i.hal_uart_get_baud_rate), (6 bytes).
    Removing hal_uart.o(i.hal_uart_is_receiving), (36 bytes).
    Removing hal_uart.o(i.hal_uart_is_tx_ready), (36 bytes).
    Removing hal_uart.o(i.hal_uart_rx_dma_callback), (176 bytes).
    Removing hal_uart.o(i.hal_uart_send_sync), (44 bytes).
    Removing hal_uart.o(i.hal_uart_set_error_callback), (12 bytes).
    Removing hal_uart.o(i.hal_uart_start_receive), (48 bytes).
    Removing hal_uart.o(i.hal_uart_stop_receive), (36 bytes).
    Removing hal_uart.o(.bss), (512 bytes).
    Removing melp_decoder.o(i.melp_adaptive_spectral_enhancement), (56 bytes).
    Removing melp_decoder.o(i.melp_decoder_decode_frame), (300 bytes).
    Removing melp_decoder.o(i.melp_decoder_deinit), (18 bytes).
    Removing melp_decoder.o(i.melp_decoder_frame_erasure), (160 bytes).
    Removing melp_decoder.o(i.melp_decoder_get_version), (24 bytes).
    Removing melp_decoder.o(i.melp_decoder_unpack_frame), (40 bytes).
    Removing melp_decoder.o(i.melp_dequantize_parameters), (212 bytes).
    Removing melp_decoder.o(i.melp_generate_excitation), (320 bytes).
    Removing melp_decoder.o(i.melp_lpc_synthesis), (140 bytes).
    Removing melp_decoder.o(i.melp_lsf_to_lpc), (76 bytes).
    Removing melp_decoder.o(i.melp_post_filter), (84 bytes).
    Removing melp_decoder.o(i.melp_pulse_dispersion), (50 bytes).
    Removing melp_decoder_optimized.o(i.melp_decoder_decode_frame_fast), (4 bytes).
    Removing melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized), (380 bytes).
    Removing melp_decoder_optimized.o(i.melp_decoder_deinit_fast), (4 bytes).
    Removing melp_decoder_optimized.o(i.melp_decoder_deinit_optimized), (44 bytes).
    Removing melp_decoder_optimized.o(i.melp_decoder_init_fast), (4 bytes).
    Removing melp_decoder_optimized.o(i.melp_decoder_init_optimized), (48 bytes).
    Removing melp_decoder_optimized.o(i.melp_generate_excitation_optimized), (268 bytes).
    Removing melp_decoder_optimized.o(i.melp_generate_pulse_train_optimized), (164 bytes).
    Removing melp_decoder_optimized.o(.bss), (2900 bytes).
    Removing melp_decoder_optimized.o(.data), (8 bytes).
    Removing melp_dsp.o(i.melp_dsp_autocorrelation), (96 bytes).
    Removing melp_dsp.o(i.melp_dsp_deinit), (18 bytes).
    Removing melp_dsp.o(i.melp_dsp_dot_product), (44 bytes).
    Removing melp_dsp.o(i.melp_dsp_float_to_q15), (100 bytes).
    Removing melp_dsp.o(i.melp_dsp_init), (26 bytes).
    Removing melp_dsp.o(i.melp_dsp_levinson_durbin), (248 bytes).
    Removing melp_dsp.o(i.melp_dsp_lpc_synthesis), (148 bytes).
    Removing melp_dsp.o(i.melp_dsp_pitch_estimation), (128 bytes).
    Removing melp_dsp.o(i.melp_dsp_post_filter), (80 bytes).
    Removing melp_dsp.o(i.melp_dsp_preemphasis), (80 bytes).
    Removing melp_dsp.o(i.melp_dsp_q15_to_float), (48 bytes).
    Removing melp_dsp.o(i.melp_dsp_spectral_analysis), (216 bytes).
    Removing melp_dsp.o(i.melp_dsp_vector_scale), (36 bytes).
    Removing melp_dsp.o(i.melp_dsp_windowing), (54 bytes).
    Removing melp_encoder.o(i.melp_encoder_get_version), (24 bytes).
    Removing melp_encoder_optimized.o(i.melp_encoder_deinit_fast), (4 bytes).
    Removing melp_encoder_optimized.o(i.melp_encoder_deinit_optimized), (32 bytes).
    Removing melp_encoder_optimized.o(i.melp_encoder_encode_frame_fast), (4 bytes).
    Removing melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized), (324 bytes).
    Removing melp_encoder_optimized.o(i.melp_encoder_init_fast), (4 bytes).
    Removing melp_encoder_optimized.o(i.melp_encoder_init_optimized), (140 bytes).
    Removing melp_encoder_optimized.o(i.melp_voicing_analysis_optimized), (408 bytes).
    Removing melp_encoder_optimized.o(.bss), (3620 bytes).
    Removing melp_encoder_optimized.o(.data), (1 bytes).
    Removing comm_protocol.o(i.comm_protocol_calculate_crc16), (60 bytes).
    Removing comm_protocol.o(i.comm_protocol_deinit), (28 bytes).
    Removing comm_protocol.o(i.comm_protocol_get_stats), (34 bytes).
    Removing comm_protocol.o(i.comm_protocol_init), (60 bytes).
    Removing comm_protocol.o(i.comm_protocol_process_received_data), (508 bytes).
    Removing comm_protocol.o(i.comm_protocol_reset_stats), (26 bytes).
    Removing comm_protocol.o(i.comm_protocol_send_ack), (30 bytes).
    Removing comm_protocol.o(i.comm_protocol_send_data), (64 bytes).
    Removing comm_protocol.o(i.comm_protocol_send_frame), (172 bytes).
    Removing comm_protocol.o(i.comm_protocol_send_heartbeat), (22 bytes).
    Removing comm_protocol.o(i.comm_protocol_send_nack), (30 bytes).
    Removing comm_protocol.o(i.comm_protocol_update), (92 bytes).
    Removing comm_protocol.o(i.comm_protocol_validate_frame), (46 bytes).
    Removing comm_protocol.o(.bss), (16 bytes).
    Removing comm_protocol.o(.data), (1 bytes).

752 unused section(s) (total 73139 bytes) removed from the image.
