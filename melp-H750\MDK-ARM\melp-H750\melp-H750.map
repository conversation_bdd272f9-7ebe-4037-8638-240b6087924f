Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32h750xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h750xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h750xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h750xx.o(RESET) refers to startup_stm32h750xx.o(STACK) for __initial_sp
    startup_stm32h750xx.o(RESET) refers to startup_stm32h750xx.o(.text) for Reset_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32h750xx.o(RESET) refers to stm32h7xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32h750xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h750xx.o(.text) refers to system_stm32h7xx.o(i.ExitRun0Mode) for ExitRun0Mode
    startup_stm32h750xx.o(.text) refers to system_stm32h7xx.o(i.SystemInit) for SystemInit
    startup_stm32h750xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32h750xx.o(.text) refers to startup_stm32h750xx.o(HEAP) for Heap_Mem
    startup_stm32h750xx.o(.text) refers to startup_stm32h750xx.o(STACK) for Stack_Mem
    main.o(i.Error_Handler) refers to main.o(i.debug_printf) for debug_printf
    main.o(i.Error_Handler) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    main.o(i.Error_Handler) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.MX_ADC1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.MX_ADC1_Init) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    main.o(i.MX_ADC1_Init) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) for HAL_ADCEx_MultiModeConfigChannel
    main.o(i.MX_ADC1_Init) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    main.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.MX_ADC1_Init) refers to main.o(.bss) for .bss
    main.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.MX_GPIO_Init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.MX_GPIO_Init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    main.o(i.MX_USART1_Init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    main.o(i.MX_USART1_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    main.o(i.MX_USART1_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    main.o(i.MX_USART1_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    main.o(i.MX_USART1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.MX_USART1_Init) refers to main.o(.bss) for .bss
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply) for HAL_PWREx_ConfigSupply
    main.o(i.SystemClock_Config) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.debug_init) refers to main.o(i.debug_printf) for debug_printf
    main.o(i.debug_init) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    main.o(i.debug_print_system_info) refers to main.o(i.debug_printf) for debug_printf
    main.o(i.debug_print_system_info) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    main.o(i.debug_print_system_info) refers to stm32h7xx_hal.o(i.HAL_GetHalVersion) for HAL_GetHalVersion
    main.o(i.debug_print_system_info) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    main.o(i.debug_print_system_info) refers to stm32h7xx_hal.o(i.HAL_GetDEVID) for HAL_GetDEVID
    main.o(i.debug_print_system_info) refers to stm32h7xx_hal.o(i.HAL_GetUIDw2) for HAL_GetUIDw2
    main.o(i.debug_print_system_info) refers to stm32h7xx_hal.o(i.HAL_GetUIDw1) for HAL_GetUIDw1
    main.o(i.debug_print_system_info) refers to stm32h7xx_hal.o(i.HAL_GetUIDw0) for HAL_GetUIDw0
    main.o(i.debug_printf) refers to vsnprintf.o(.text) for vsnprintf
    main.o(i.debug_printf) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    main.o(i.debug_printf) refers to main.o(.bss) for .bss
    main.o(i.debug_test_button) refers to main.o(i.debug_printf) for debug_printf
    main.o(i.debug_test_button) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.debug_test_button) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    main.o(i.debug_test_button) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.debug_test_button) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.debug_test_led) refers to main.o(i.debug_printf) for debug_printf
    main.o(i.debug_test_led) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.debug_test_led) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.debug_test_led) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    main.o(i.debug_test_uart) refers to main.o(i.debug_printf) for debug_printf
    main.o(i.debug_test_uart) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main.o(i.debug_test_uart) refers to strlen.o(.text) for strlen
    main.o(i.debug_test_uart) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    main.o(i.debug_test_uart) refers to main.o(.bss) for .bss
    main.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable) for HAL_MPU_Disable
    main.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion) for HAL_MPU_ConfigRegion
    main.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable) for HAL_MPU_Enable
    main.o(i.main) refers to stm32h7xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to main.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to main.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.main) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_Init) for HAL_DAC_Init
    main.o(i.main) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    main.o(i.main) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.main) refers to main.o(i.MX_USART1_Init) for MX_USART1_Init
    main.o(i.main) refers to main.o(i.debug_init) for debug_init
    main.o(i.main) refers to main.o(i.debug_printf) for debug_printf
    main.o(i.main) refers to main.o(i.debug_print_system_info) for debug_print_system_info
    main.o(i.main) refers to main.o(i.debug_test_led) for debug_test_led
    main.o(i.main) refers to main.o(i.debug_test_uart) for debug_test_uart
    main.o(i.main) refers to main.o(i.debug_test_button) for debug_test_button
    main.o(i.main) refers to app_main.o(i.app_init) for app_init
    main.o(i.main) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    main.o(i.main) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.main) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32h7xx_it.o(i.SysTick_Handler) refers to stm32h7xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32h7xx_hal_msp.o(i.HAL_ADC_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32h7xx_hal_msp.o(i.HAL_ADC_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32h7xx_hal_msp.o(i.HAL_ADC_MspInit) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    stm32h7xx_hal_msp.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    stm32h7xx_hal_msp.o(i.HAL_ADC_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_msp.o(i.HAL_DAC_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32h7xx_hal_msp.o(i.HAL_DAC_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32h7xx_hal_msp.o(i.HAL_DAC_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_msp.o(i.HAL_QSPI_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32h7xx_hal_msp.o(i.HAL_QSPI_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32h7xx_hal_msp.o(i.HAL_QSPI_MspInit) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    stm32h7xx_hal_msp.o(i.HAL_QSPI_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    stm32h7xx_hal_msp.o(i.HAL_QSPI_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_msp.o(i.HAL_UART_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_adc.o(i.ADC_ConfigureBoostMode) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_adc.o(i.ADC_ConfigureBoostMode) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32h7xx_hal_adc.o(i.ADC_ConfigureBoostMode) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32h7xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) refers to hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32h7xx_hal_adc.o(i.ADC_DMAError) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32h7xx_hal_adc.o(i.ADC_Disable) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.ADC_Enable) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32h7xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32h7xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels) for LL_ADC_SetAnalogWDMonitChannels
    stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32h7xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32h7xx_hal_msp.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback) for HAL_ADCEx_EndOfSamplingCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback) for HAL_ADCEx_LevelOutOfWindow2Callback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback) for HAL_ADCEx_LevelOutOfWindow3Callback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback) for HAL_ADCEx_InjectedQueueOverflowCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32h7xx_hal_msp.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32h7xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32h7xx_hal_adc.o(i.ADC_ConfigureBoostMode) for ADC_ConfigureBoostMode
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32h7xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_SetOffset) for LL_ADC_SetOffset
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_FactorLoad) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) for HAL_ADCEx_LinearCalibration_SetValue
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_GetValue) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_GetValue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_SetCalibrationLinearFactor) for LL_ADC_SetCalibrationLinearFactor
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(.data) for uwTickPrio
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(.data) for uwTickPrio
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetOscConfig) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL1ClockFreq) for HAL_RCCEx_GetPLL1ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq) for HAL_RCCEx_GetPLL2ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq) for HAL_RCCEx_GetPLL3ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) for HAL_RCCEx_GetD3PCLK1Freq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config) for RCCEx_PLL2_Config
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config) for RCCEx_PLL3_Config
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) for FLASH_CRC_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) for FLASH_OB_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC) refers to stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) for FLASH_OB_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC) refers to stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) for FLASH_CRC_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32h7xx_hal_hsem.o(i.HAL_HSEM_IRQHandler) refers to stm32h7xx_hal_hsem.o(i.HAL_HSEM_FreeCallback) for HAL_HSEM_FreeCallback
    stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32h7xx_hal_dma.o(.constdata) for .constdata
    stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32h7xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32h7xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_IRQHandler) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init) refers to stm32h7xx_hal_mdma.o(i.MDMA_Init) for MDMA_Init
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) for HAL_MDMA_Abort
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start) refers to stm32h7xx_hal_mdma.o(i.MDMA_SetConfig) for MDMA_SetConfig
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT) refers to stm32h7xx_hal_mdma.o(i.MDMA_SetConfig) for MDMA_SetConfig
    stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_AVDCallback) for HAL_PWREx_AVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP1_Callback) for HAL_PWREx_WKUP1_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP2_Callback) for HAL_PWREx_WKUP2_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP3_Callback) for HAL_PWREx_WKUP3_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP4_Callback) for HAL_PWREx_WKUP4_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP5_Callback) for HAL_PWREx_WKUP5_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP6_Callback) for HAL_PWREx_WKUP6_Callback
    stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32h7xx_hal.o(i.HAL_DeInit) refers to stm32h7xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32h7xx_hal.o(i.HAL_Delay) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal.o(i.HAL_Delay) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTickFreq) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTickPrio) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_IncTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32h7xx_hal.o(i.HAL_Init) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal.o(i.HAL_Init) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_InitTick) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal.o(i.HAL_SetTickFreq) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal.o(i.HAL_SetTickFreq) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32h7xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_DMAError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_i2c.o(i.I2C_DMAError) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dac.o(i.DAC_DMAConvCpltCh1) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32h7xx_hal_dac.o(i.DAC_DMAErrorCh1) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32h7xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32h7xx_hal_dac.o(i.HAL_DAC_ConfigChannel) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dac.o(i.HAL_DAC_DeInit) refers to stm32h7xx_hal_msp.o(i.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32h7xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32h7xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32h7xx_hal_dac.o(i.HAL_DAC_Init) refers to stm32h7xx_hal_msp.o(i.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32h7xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32h7xx_hal_dac.o(i.HAL_DAC_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) refers to stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32h7xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) refers to stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32h7xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) refers to stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA) refers to stm32h7xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_SelfCalibrate) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) for HAL_MDMA_Abort
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort_IT) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort_IT) for HAL_MDMA_Abort_IT
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort_IT) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_AbortCpltCallback) for HAL_QSPI_AbortCpltCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort_IT) refers to stm32h7xx_hal_qspi.o(i.QSPI_DMAAbortCplt) for QSPI_DMAAbortCplt
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling) refers to stm32h7xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling_IT) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling_IT) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling_IT) refers to stm32h7xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command) refers to stm32h7xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command_IT) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command_IT) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command_IT) refers to stm32h7xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_DeInit) refers to stm32h7xx_hal_msp.o(i.HAL_QSPI_MspDeInit) for HAL_QSPI_MspDeInit
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_FifoThresholdCallback) for HAL_QSPI_FifoThresholdCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_TxCpltCallback) for HAL_QSPI_TxCpltCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_RxCpltCallback) for HAL_QSPI_RxCpltCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_CmdCpltCallback) for HAL_QSPI_CmdCpltCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_AbortCpltCallback) for HAL_QSPI_AbortCpltCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_ErrorCallback) for HAL_QSPI_ErrorCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_StatusMatchCallback) for HAL_QSPI_StatusMatchCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort_IT) for HAL_MDMA_Abort_IT
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_TimeOutCallback) for HAL_QSPI_TimeOutCallback
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32h7xx_hal_qspi.o(i.QSPI_DMAAbortCplt) for QSPI_DMAAbortCplt
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Init) refers to stm32h7xx_hal_msp.o(i.HAL_QSPI_MspInit) for HAL_QSPI_MspInit
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Init) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_MemoryMapped) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_MemoryMapped) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_MemoryMapped) refers to stm32h7xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT) for HAL_MDMA_Start_IT
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA) refers to stm32h7xx_hal_qspi.o(i.QSPI_DMARxCplt) for QSPI_DMARxCplt
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA) refers to stm32h7xx_hal_qspi.o(i.QSPI_DMAError) for QSPI_DMAError
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit) refers to stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT) for HAL_MDMA_Start_IT
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA) refers to stm32h7xx_hal_qspi.o(i.QSPI_DMATxCplt) for QSPI_DMATxCplt
    stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA) refers to stm32h7xx_hal_qspi.o(i.QSPI_DMAError) for QSPI_DMAError
    stm32h7xx_hal_qspi.o(i.QSPI_DMAAbortCplt) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_ErrorCallback) for HAL_QSPI_ErrorCallback
    stm32h7xx_hal_qspi.o(i.QSPI_DMAError) refers to stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort_IT) for HAL_QSPI_Abort_IT
    stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_ll_delayblock.o(i.DelayBlock_Enable) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_DeInit) refers to stm32h7xx_hal_msp.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback) for HAL_UARTEx_TxFifoEmptyCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback) for HAL_UARTEx_RxFifoFullCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN) for UART_TxISR_8BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN) for UART_TxISR_16BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32h7xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError) refers to hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt) refers to hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) refers to hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) refers to hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) for HAL_RCCEx_GetD3PCLK1Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq) for HAL_RCCEx_GetPLL2ClockFreq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq) for HAL_RCCEx_GetPLL3ClockFreq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_uart.o(.constdata) for .constdata
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) for UART_RxISR_8BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) for UART_RxISR_16BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_msp.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) refers to stm32h7xx_hal_uart_ex.o(.constdata) for .constdata
    system_stm32h7xx.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx.o(.constdata) for .constdata
    system_stm32h7xx.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx.o(.data) for .data
    app_main.o(i.app_adc_callback) refers to app_melp.o(i.app_melp_process_input) for app_melp_process_input
    app_main.o(i.app_adc_callback) refers to app_main.o(.bss) for .bss
    app_main.o(i.app_button_callback) refers to app_melp.o(i.app_melp_start_recording) for app_melp_start_recording
    app_main.o(i.app_button_callback) refers to hal_adc.o(i.hal_adc_start_continuous) for hal_adc_start_continuous
    app_main.o(i.app_button_callback) refers to hal_adc.o(i.hal_adc_stop_continuous) for hal_adc_stop_continuous
    app_main.o(i.app_button_callback) refers to app_melp.o(i.app_melp_stop_recording) for app_melp_stop_recording
    app_main.o(i.app_button_callback) refers to hal_gpio.o(i.hal_gpio_set_led) for hal_gpio_set_led
    app_main.o(i.app_button_callback) refers to app_main.o(.data) for .data
    app_main.o(i.app_button_callback) refers to app_main.o(.bss) for .bss
    app_main.o(i.app_button_callback) refers to app_main.o(i.app_adc_callback) for app_adc_callback
    app_main.o(i.app_get_state) refers to app_main.o(.data) for .data
    app_main.o(i.app_init) refers to hal_gpio.o(i.hal_gpio_init) for hal_gpio_init
    app_main.o(i.app_init) refers to hal_adc.o(i.hal_adc_init) for hal_adc_init
    app_main.o(i.app_init) refers to hal_dac.o(i.hal_dac_init) for hal_dac_init
    app_main.o(i.app_init) refers to hal_uart.o(i.hal_uart_init) for hal_uart_init
    app_main.o(i.app_init) refers to hal_gpio.o(i.hal_gpio_set_button_callback) for hal_gpio_set_button_callback
    app_main.o(i.app_init) refers to app_melp.o(i.app_melp_init) for app_melp_init
    app_main.o(i.app_init) refers to hal_gpio.o(i.hal_gpio_set_led) for hal_gpio_set_led
    app_main.o(i.app_init) refers to app_main.o(.data) for .data
    app_main.o(i.app_init) refers to app_main.o(i.app_button_callback) for app_button_callback
    app_main.o(i.app_init) refers to app_main.o(.constdata) for .constdata
    app_main.o(i.app_init) refers to app_main.o(.bss) for .bss
    app_main.o(i.app_melp_error_callback) refers to app_main.o(.data) for .data
    app_main.o(i.app_melp_frame_decoded_callback) refers to hal_dac.o(i.hal_dac_write_single) for hal_dac_write_single
    app_main.o(i.app_melp_frame_encoded_callback) refers to hal_uart.o(i.hal_uart_send_async) for hal_uart_send_async
    app_main.o(i.app_melp_frame_encoded_callback) refers to app_main.o(i.app_uart_tx_callback) for app_uart_tx_callback
    app_main.o(i.app_run) refers to hal_gpio.o(i.hal_gpio_button_poll) for hal_gpio_button_poll
    app_main.o(i.app_run) refers to hal_gpio.o(i.hal_gpio_set_led) for hal_gpio_set_led
    app_main.o(i.app_run) refers to hal_gpio.o(i.hal_gpio_toggle_led) for hal_gpio_toggle_led
    app_main.o(i.app_run) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    app_main.o(i.app_run) refers to app_main.o(.data) for .data
    app_main.o(i.app_systick_handler) refers to app_main.o(.data) for .data
    app_main.o(.constdata) refers to app_main.o(i.app_melp_frame_encoded_callback) for app_melp_frame_encoded_callback
    app_main.o(.constdata) refers to app_main.o(i.app_melp_frame_decoded_callback) for app_melp_frame_decoded_callback
    app_main.o(.constdata) refers to app_main.o(i.app_melp_state_changed_callback) for app_melp_state_changed_callback
    app_main.o(.constdata) refers to app_main.o(i.app_melp_error_callback) for app_melp_error_callback
    app_melp.o(i.app_melp_change_state) refers to app_melp.o(.data) for .data
    app_melp.o(i.app_melp_change_state) refers to app_melp.o(.bss) for .bss
    app_melp.o(i.app_melp_decode_frame) refers to melp_decoder.o(i.melp_decoder_decode_frame) for melp_decoder_decode_frame
    app_melp.o(i.app_melp_decode_frame) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    app_melp.o(i.app_melp_decode_frame) refers to app_melp.o(.data) for .data
    app_melp.o(i.app_melp_decode_frame) refers to app_melp.o(.bss) for .bss
    app_melp.o(i.app_melp_deinit) refers to melp_encoder.o(i.melp_encoder_deinit) for melp_encoder_deinit
    app_melp.o(i.app_melp_deinit) refers to melp_decoder.o(i.melp_decoder_deinit) for melp_decoder_deinit
    app_melp.o(i.app_melp_deinit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_melp.o(i.app_melp_deinit) refers to app_melp.o(.data) for .data
    app_melp.o(i.app_melp_encode_frame) refers to melp_encoder.o(i.melp_encoder_encode_frame) for melp_encoder_encode_frame
    app_melp.o(i.app_melp_encode_frame) refers to melp_encoder.o(i.melp_encoder_pack_frame) for melp_encoder_pack_frame
    app_melp.o(i.app_melp_encode_frame) refers to app_melp.o(.data) for .data
    app_melp.o(i.app_melp_encode_frame) refers to app_melp.o(.bss) for .bss
    app_melp.o(i.app_melp_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_melp.o(i.app_melp_init) refers to melp_encoder.o(i.melp_encoder_init) for melp_encoder_init
    app_melp.o(i.app_melp_init) refers to melp_decoder.o(i.melp_decoder_init) for melp_decoder_init
    app_melp.o(i.app_melp_init) refers to melp_encoder.o(i.melp_encoder_deinit) for melp_encoder_deinit
    app_melp.o(i.app_melp_init) refers to app_melp.o(.bss) for .bss
    app_melp.o(i.app_melp_init) refers to app_melp.o(.data) for .data
    app_melp.o(i.app_melp_process_input) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    app_melp.o(i.app_melp_process_input) refers to app_melp.o(i.app_melp_change_state) for app_melp_change_state
    app_melp.o(i.app_melp_process_input) refers to app_melp.o(i.app_melp_encode_frame) for app_melp_encode_frame
    app_melp.o(i.app_melp_process_input) refers to rt_memmove_v6.o(.text) for __aeabi_memmove
    app_melp.o(i.app_melp_process_received_frame) refers to app_melp.o(i.app_melp_change_state) for app_melp_change_state
    app_melp.o(i.app_melp_process_received_frame) refers to app_melp.o(i.app_melp_decode_frame) for app_melp_decode_frame
    app_melp.o(i.app_melp_start_playback) refers to app_melp.o(i.app_melp_change_state) for app_melp_change_state
    app_melp.o(i.app_melp_start_recording) refers to app_melp.o(i.app_melp_change_state) for app_melp_change_state
    app_melp.o(i.app_melp_stop_playback) refers to app_melp.o(i.app_melp_change_state) for app_melp_change_state
    app_melp.o(i.app_melp_stop_recording) refers to app_melp.o(i.app_melp_change_state) for app_melp_change_state
    app_state_machine.o(i.app_sm_deinit) refers to app_state_machine.o(i.app_sm_handle_state_exit) for app_sm_handle_state_exit
    app_state_machine.o(i.app_sm_deinit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_state_machine.o(i.app_sm_deinit) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_force_state) refers to app_state_machine.o(i.app_sm_transition_to) for app_sm_transition_to
    app_state_machine.o(i.app_sm_get_error_name) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_get_event_name) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_get_state_name) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_handle_state_entry) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_handle_state_entry) refers to app_state_machine.o(.bss) for .bss
    app_state_machine.o(i.app_sm_handle_state_exit) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_handle_state_exit) refers to app_state_machine.o(.bss) for .bss
    app_state_machine.o(i.app_sm_handle_timeout) refers to app_state_machine.o(i.app_sm_report_error) for app_sm_report_error
    app_state_machine.o(i.app_sm_handle_timeout) refers to app_state_machine.o(i.app_sm_process_event) for app_sm_process_event
    app_state_machine.o(i.app_sm_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_state_machine.o(i.app_sm_init) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    app_state_machine.o(i.app_sm_init) refers to app_state_machine.o(i.app_sm_handle_state_entry) for app_sm_handle_state_entry
    app_state_machine.o(i.app_sm_init) refers to app_state_machine.o(.bss) for .bss
    app_state_machine.o(i.app_sm_init) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_process_event) refers to app_state_machine.o(i.app_sm_transition_to) for app_sm_transition_to
    app_state_machine.o(i.app_sm_report_error) refers to app_state_machine.o(i.app_sm_process_event) for app_sm_process_event
    app_state_machine.o(i.app_sm_report_error) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_report_error) refers to app_state_machine.o(.bss) for .bss
    app_state_machine.o(i.app_sm_reset_statistics) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_state_machine.o(i.app_sm_transition_to) refers to app_state_machine.o(i.app_sm_is_valid_transition) for app_sm_is_valid_transition
    app_state_machine.o(i.app_sm_transition_to) refers to app_state_machine.o(i.app_sm_handle_state_exit) for app_sm_handle_state_exit
    app_state_machine.o(i.app_sm_transition_to) refers to app_state_machine.o(i.app_sm_handle_state_entry) for app_sm_handle_state_entry
    app_state_machine.o(i.app_sm_transition_to) refers to app_state_machine.o(i.app_sm_report_error) for app_sm_report_error
    app_state_machine.o(i.app_sm_transition_to) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_transition_to) refers to app_state_machine.o(.bss) for .bss
    app_state_machine.o(i.app_sm_update) refers to app_state_machine.o(i.app_sm_handle_timeout) for app_sm_handle_timeout
    app_state_machine.o(i.app_sm_update) refers to app_state_machine.o(.data) for .data
    app_state_machine.o(i.app_sm_update) refers to app_state_machine.o(.bss) for .bss
    app_state_machine.o(.data) refers to app_state_machine.o(.conststring) for .conststring
    hal_adc.o(i.HAL_ADC_ConvCpltCallback) refers to main.o(.bss) for hadc1
    hal_adc.o(i.HAL_ADC_ConvCpltCallback) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.HAL_ADC_ConvCpltCallback) refers to hal_adc.o(.bss) for .bss
    hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.bss) for hadc1
    hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) refers to hal_adc.o(.bss) for .bss
    hal_adc.o(i.hal_adc_deinit) refers to hal_adc.o(i.hal_adc_stop_continuous) for hal_adc_stop_continuous
    hal_adc.o(i.hal_adc_deinit) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit) for HAL_ADC_DeInit
    hal_adc.o(i.hal_adc_deinit) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.hal_adc_deinit) refers to main.o(.bss) for hadc1
    hal_adc.o(i.hal_adc_init) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) for HAL_ADCEx_Calibration_Start
    hal_adc.o(i.hal_adc_init) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.hal_adc_init) refers to main.o(.bss) for hadc1
    hal_adc.o(i.hal_adc_is_ready) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_GetState) for HAL_ADC_GetState
    hal_adc.o(i.hal_adc_is_ready) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.hal_adc_is_ready) refers to main.o(.bss) for hadc1
    hal_adc.o(i.hal_adc_read_single) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    hal_adc.o(i.hal_adc_read_single) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_PollForConversion) for HAL_ADC_PollForConversion
    hal_adc.o(i.hal_adc_read_single) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    hal_adc.o(i.hal_adc_read_single) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Stop) for HAL_ADC_Stop
    hal_adc.o(i.hal_adc_read_single) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.hal_adc_read_single) refers to main.o(.bss) for hadc1
    hal_adc.o(i.hal_adc_start_continuous) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    hal_adc.o(i.hal_adc_start_continuous) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.hal_adc_start_continuous) refers to hal_adc.o(.bss) for .bss
    hal_adc.o(i.hal_adc_start_continuous) refers to main.o(.bss) for hadc1
    hal_adc.o(i.hal_adc_stop_continuous) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    hal_adc.o(i.hal_adc_stop_continuous) refers to hal_adc.o(.data) for .data
    hal_adc.o(i.hal_adc_stop_continuous) refers to main.o(.bss) for hadc1
    hal_dac.o(i.HAL_DAC_ConvCpltCallback) refers to hal_dac.o(i.hal_dac_dma_callback) for hal_dac_dma_callback
    hal_dac.o(i.HAL_DAC_ConvHalfCpltCallback) refers to hal_dac.o(i.hal_dac_dma_callback) for hal_dac_dma_callback
    hal_dac.o(i.hal_dac_deinit) refers to hal_dac.o(i.hal_dac_stop_continuous) for hal_dac_stop_continuous
    hal_dac.o(i.hal_dac_deinit) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_Stop) for HAL_DAC_Stop
    hal_dac.o(i.hal_dac_deinit) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_DeInit) for HAL_DAC_DeInit
    hal_dac.o(i.hal_dac_deinit) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_deinit) refers to main.o(.bss) for hdac1
    hal_dac.o(i.hal_dac_dma_callback) refers to main.o(.bss) for hdac1
    hal_dac.o(i.hal_dac_dma_callback) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_init) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_Start) for HAL_DAC_Start
    hal_dac.o(i.hal_dac_init) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_init) refers to main.o(.bss) for hdac1
    hal_dac.o(i.hal_dac_is_ready) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_GetState) for HAL_DAC_GetState
    hal_dac.o(i.hal_dac_is_ready) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_is_ready) refers to main.o(.bss) for hdac1
    hal_dac.o(i.hal_dac_mute) refers to hal_dac.o(i.hal_dac_write_single) for hal_dac_write_single
    hal_dac.o(i.hal_dac_start_continuous) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA) for HAL_DAC_Start_DMA
    hal_dac.o(i.hal_dac_start_continuous) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_start_continuous) refers to main.o(.bss) for hdac1
    hal_dac.o(i.hal_dac_stop_continuous) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_Stop_DMA) for HAL_DAC_Stop_DMA
    hal_dac.o(i.hal_dac_stop_continuous) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_stop_continuous) refers to main.o(.bss) for hdac1
    hal_dac.o(i.hal_dac_write_single) refers to stm32h7xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    hal_dac.o(i.hal_dac_write_single) refers to hal_dac.o(.data) for .data
    hal_dac.o(i.hal_dac_write_single) refers to main.o(.bss) for hdac1
    hal_gpio.o(i.HAL_GPIO_EXTI_Callback) refers to hal_gpio.o(i.hal_gpio_read_button) for hal_gpio_read_button
    hal_gpio.o(i.HAL_GPIO_EXTI_Callback) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_button_poll) refers to hal_gpio.o(i.hal_gpio_read_button) for hal_gpio_read_button
    hal_gpio.o(i.hal_gpio_button_poll) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_deinit) refers to hal_gpio.o(i.hal_gpio_disable_button_interrupt) for hal_gpio_disable_button_interrupt
    hal_gpio.o(i.hal_gpio_deinit) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_disable_button_interrupt) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    hal_gpio.o(i.hal_gpio_disable_button_interrupt) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    hal_gpio.o(i.hal_gpio_disable_button_interrupt) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    hal_gpio.o(i.hal_gpio_disable_button_interrupt) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_enable_button_interrupt) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    hal_gpio.o(i.hal_gpio_enable_button_interrupt) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    hal_gpio.o(i.hal_gpio_enable_button_interrupt) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    hal_gpio.o(i.hal_gpio_enable_button_interrupt) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    hal_gpio.o(i.hal_gpio_enable_button_interrupt) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_get_led_state) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    hal_gpio.o(i.hal_gpio_get_led_state) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_init) refers to hal_gpio.o(i.hal_gpio_read_button) for hal_gpio_read_button
    hal_gpio.o(i.hal_gpio_init) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_read_button) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    hal_gpio.o(i.hal_gpio_read_button) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_set_button_callback) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_set_led) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    hal_gpio.o(i.hal_gpio_set_led) refers to hal_gpio.o(.data) for .data
    hal_gpio.o(i.hal_gpio_toggle_led) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    hal_gpio.o(i.hal_gpio_toggle_led) refers to hal_gpio.o(.data) for .data
    hal_uart.o(i.HAL_UARTEx_RxEventCallback) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    hal_uart.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.bss) for huart1
    hal_uart.o(i.HAL_UARTEx_RxEventCallback) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.HAL_UARTEx_RxEventCallback) refers to hal_uart.o(.bss) for .bss
    hal_uart.o(i.HAL_UART_ErrorCallback) refers to main.o(.bss) for huart1
    hal_uart.o(i.HAL_UART_ErrorCallback) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.HAL_UART_RxCpltCallback) refers to hal_uart.o(i.hal_uart_rx_dma_callback) for hal_uart_rx_dma_callback
    hal_uart.o(i.HAL_UART_TxCpltCallback) refers to main.o(.bss) for huart1
    hal_uart.o(i.HAL_UART_TxCpltCallback) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_deinit) refers to hal_uart.o(i.hal_uart_stop_receive) for hal_uart_stop_receive
    hal_uart.o(i.hal_uart_deinit) refers to stm32h7xx_hal_uart.o(i.HAL_UART_DeInit) for HAL_UART_DeInit
    hal_uart.o(i.hal_uart_deinit) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_deinit) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_init) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_is_receiving) refers to stm32h7xx_hal_uart.o(i.HAL_UART_GetState) for HAL_UART_GetState
    hal_uart.o(i.hal_uart_is_receiving) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_is_receiving) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_is_tx_ready) refers to stm32h7xx_hal_uart.o(i.HAL_UART_GetState) for HAL_UART_GetState
    hal_uart.o(i.hal_uart_is_tx_ready) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_is_tx_ready) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_rx_dma_callback) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_rx_dma_callback) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_rx_dma_callback) refers to hal_uart.o(.bss) for .bss
    hal_uart.o(i.hal_uart_send_async) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    hal_uart.o(i.hal_uart_send_async) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_send_async) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_send_sync) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    hal_uart.o(i.hal_uart_send_sync) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_send_sync) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_set_error_callback) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_start_receive) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    hal_uart.o(i.hal_uart_start_receive) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_start_receive) refers to main.o(.bss) for huart1
    hal_uart.o(i.hal_uart_stop_receive) refers to stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    hal_uart.o(i.hal_uart_stop_receive) refers to hal_uart.o(.data) for .data
    hal_uart.o(i.hal_uart_stop_receive) refers to main.o(.bss) for huart1
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_decoder_unpack_frame) for melp_decoder_unpack_frame
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_dequantize_parameters) for melp_dequantize_parameters
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_lsf_to_lpc) for melp_lsf_to_lpc
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_generate_excitation) for melp_generate_excitation
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_lpc_synthesis) for melp_lpc_synthesis
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_adaptive_spectral_enhancement) for melp_adaptive_spectral_enhancement
    melp_decoder.o(i.melp_decoder_decode_frame) refers to melp_decoder.o(i.melp_post_filter) for melp_post_filter
    melp_decoder.o(i.melp_decoder_decode_frame) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    melp_decoder.o(i.melp_decoder_deinit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    melp_decoder.o(i.melp_decoder_frame_erasure) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    melp_decoder.o(i.melp_decoder_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    melp_decoder.o(i.melp_decoder_init) refers to melp_decoder.o(.data) for .data
    melp_decoder.o(i.melp_generate_excitation) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    melp_decoder.o(i.melp_generate_excitation) refers to melp_decoder.o(.data) for .data
    melp_decoder.o(i.melp_lsf_to_lpc) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_fast) refers to melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) for melp_decoder_decode_frame_optimized
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_decoder.o(i.melp_decoder_unpack_frame) for melp_decoder_unpack_frame
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_decoder.o(i.melp_dequantize_parameters) for melp_dequantize_parameters
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_decoder.o(i.melp_lsf_to_lpc) for melp_lsf_to_lpc
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_decoder_optimized.o(i.melp_generate_excitation_optimized) for melp_generate_excitation_optimized
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_lpc_synthesis) for melp_dsp_lpc_synthesis
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_vector_scale) for melp_dsp_vector_scale
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_post_filter) for melp_dsp_post_filter
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized) refers to melp_decoder_optimized.o(.bss) for .bss
    melp_decoder_optimized.o(i.melp_decoder_deinit_fast) refers to melp_decoder_optimized.o(i.melp_decoder_deinit_optimized) for melp_decoder_deinit_optimized
    melp_decoder_optimized.o(i.melp_decoder_deinit_optimized) refers to melp_dsp.o(i.melp_dsp_deinit) for melp_dsp_deinit
    melp_decoder_optimized.o(i.melp_decoder_deinit_optimized) refers to melp_decoder.o(i.melp_decoder_deinit) for melp_decoder_deinit
    melp_decoder_optimized.o(i.melp_decoder_deinit_optimized) refers to melp_decoder_optimized.o(.data) for .data
    melp_decoder_optimized.o(i.melp_decoder_deinit_optimized) refers to melp_decoder_optimized.o(.bss) for .bss
    melp_decoder_optimized.o(i.melp_decoder_init_fast) refers to melp_decoder_optimized.o(i.melp_decoder_init_optimized) for melp_decoder_init_optimized
    melp_decoder_optimized.o(i.melp_decoder_init_optimized) refers to melp_decoder.o(i.melp_decoder_init) for melp_decoder_init
    melp_decoder_optimized.o(i.melp_decoder_init_optimized) refers to melp_dsp.o(i.melp_dsp_init) for melp_dsp_init
    melp_decoder_optimized.o(i.melp_decoder_init_optimized) refers to melp_decoder_optimized.o(.data) for .data
    melp_decoder_optimized.o(i.melp_decoder_init_optimized) refers to melp_decoder_optimized.o(.bss) for .bss
    melp_decoder_optimized.o(i.melp_generate_excitation_optimized) refers to melp_decoder_optimized.o(i.melp_generate_pulse_train_optimized) for melp_generate_pulse_train_optimized
    melp_decoder_optimized.o(i.melp_generate_excitation_optimized) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    melp_decoder_optimized.o(i.melp_generate_excitation_optimized) refers to melp_dsp.o(i.melp_dsp_vector_scale) for melp_dsp_vector_scale
    melp_decoder_optimized.o(i.melp_generate_excitation_optimized) refers to melp_decoder_optimized.o(.data) for .data
    melp_decoder_optimized.o(i.melp_generate_pulse_train_optimized) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    melp_decoder_optimized.o(i.melp_generate_pulse_train_optimized) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    melp_dsp.o(i.melp_dsp_deinit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    melp_dsp.o(i.melp_dsp_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    melp_dsp.o(i.melp_dsp_spectral_analysis) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    melp_dsp.o(i.melp_dsp_spectral_analysis) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    melp_dsp.o(i.melp_dsp_spectral_analysis) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    melp_dsp.o(i.melp_dsp_spectral_analysis) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    melp_encoder.o(i.melp_encoder_deinit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    melp_encoder.o(i.melp_encoder_encode_frame) refers to melp_encoder.o(i.melp_lpc_analysis) for melp_lpc_analysis
    melp_encoder.o(i.melp_encoder_encode_frame) refers to melp_encoder.o(i.melp_lpc_to_lsf) for melp_lpc_to_lsf
    melp_encoder.o(i.melp_encoder_encode_frame) refers to melp_encoder.o(i.melp_pitch_estimation) for melp_pitch_estimation
    melp_encoder.o(i.melp_encoder_encode_frame) refers to melp_encoder.o(i.melp_voicing_analysis) for melp_voicing_analysis
    melp_encoder.o(i.melp_encoder_encode_frame) refers to melp_encoder.o(i.melp_quantize_parameters) for melp_quantize_parameters
    melp_encoder.o(i.melp_encoder_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    melp_encoder.o(i.melp_encoder_init) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    melp_encoder.o(i.melp_encoder_init) refers to melp_encoder.o(.data) for .data
    melp_encoder.o(i.melp_encoder_init) refers to melp_encoder.o(.bss) for .bss
    melp_encoder.o(i.melp_lpc_analysis) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    melp_encoder.o(i.melp_lpc_analysis) refers to melp_encoder.o(i.melp_levinson_durbin) for melp_levinson_durbin
    melp_encoder.o(i.melp_lpc_analysis) refers to melp_encoder.o(.bss) for .bss
    melp_encoder_optimized.o(i.melp_encoder_deinit_fast) refers to melp_encoder_optimized.o(i.melp_encoder_deinit_optimized) for melp_encoder_deinit_optimized
    melp_encoder_optimized.o(i.melp_encoder_deinit_optimized) refers to melp_dsp.o(i.melp_dsp_deinit) for melp_dsp_deinit
    melp_encoder_optimized.o(i.melp_encoder_deinit_optimized) refers to melp_encoder.o(i.melp_encoder_deinit) for melp_encoder_deinit
    melp_encoder_optimized.o(i.melp_encoder_deinit_optimized) refers to melp_encoder_optimized.o(.bss) for .bss
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_fast) refers to melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) for melp_encoder_encode_frame_optimized
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_preemphasis) for melp_dsp_preemphasis
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_windowing) for melp_dsp_windowing
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_autocorrelation) for melp_dsp_autocorrelation
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_levinson_durbin) for melp_dsp_levinson_durbin
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_encoder.o(i.melp_lpc_to_lsf) for melp_lpc_to_lsf
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_dsp.o(i.melp_dsp_pitch_estimation) for melp_dsp_pitch_estimation
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_encoder_optimized.o(i.melp_voicing_analysis_optimized) for melp_voicing_analysis_optimized
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_encoder.o(i.melp_quantize_parameters) for melp_quantize_parameters
    melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized) refers to melp_encoder_optimized.o(.bss) for .bss
    melp_encoder_optimized.o(i.melp_encoder_init_fast) refers to melp_encoder_optimized.o(i.melp_encoder_init_optimized) for melp_encoder_init_optimized
    melp_encoder_optimized.o(i.melp_encoder_init_optimized) refers to melp_encoder.o(i.melp_encoder_init) for melp_encoder_init
    melp_encoder_optimized.o(i.melp_encoder_init_optimized) refers to melp_dsp.o(i.melp_dsp_init) for melp_dsp_init
    melp_encoder_optimized.o(i.melp_encoder_init_optimized) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    melp_encoder_optimized.o(i.melp_encoder_init_optimized) refers to melp_encoder_optimized.o(.bss) for .bss
    melp_encoder_optimized.o(i.melp_encoder_init_optimized) refers to melp_encoder_optimized.o(.data) for .data
    melp_encoder_optimized.o(i.melp_voicing_analysis_optimized) refers to melp_dsp.o(i.melp_dsp_dot_product) for melp_dsp_dot_product
    melp_encoder_optimized.o(i.melp_voicing_analysis_optimized) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    melp_encoder_optimized.o(i.melp_voicing_analysis_optimized) refers to melp_dsp.o(i.melp_dsp_spectral_analysis) for melp_dsp_spectral_analysis
    melp_encoder_optimized.o(i.melp_voicing_analysis_optimized) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    melp_encoder_optimized.o(i.melp_voicing_analysis_optimized) refers to melp_encoder_optimized.o(.bss) for .bss
    comm_protocol.o(i.comm_protocol_deinit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    comm_protocol.o(i.comm_protocol_deinit) refers to comm_protocol.o(.data) for .data
    comm_protocol.o(i.comm_protocol_get_stats) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    comm_protocol.o(i.comm_protocol_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    comm_protocol.o(i.comm_protocol_init) refers to comm_protocol.o(.bss) for .bss
    comm_protocol.o(i.comm_protocol_init) refers to comm_protocol.o(.data) for .data
    comm_protocol.o(i.comm_protocol_process_received_data) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    comm_protocol.o(i.comm_protocol_process_received_data) refers to rt_memmove_v6.o(.text) for __aeabi_memmove
    comm_protocol.o(i.comm_protocol_process_received_data) refers to comm_protocol.o(i.comm_protocol_validate_frame) for comm_protocol_validate_frame
    comm_protocol.o(i.comm_protocol_process_received_data) refers to comm_protocol.o(i.comm_protocol_send_ack) for comm_protocol_send_ack
    comm_protocol.o(i.comm_protocol_process_received_data) refers to comm_protocol.o(i.comm_protocol_send_nack) for comm_protocol_send_nack
    comm_protocol.o(i.comm_protocol_process_received_data) refers to comm_protocol.o(.bss) for .bss
    comm_protocol.o(i.comm_protocol_process_received_data) refers to comm_protocol.o(.data) for .data
    comm_protocol.o(i.comm_protocol_reset_stats) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    comm_protocol.o(i.comm_protocol_send_ack) refers to comm_protocol.o(i.comm_protocol_send_frame) for comm_protocol_send_frame
    comm_protocol.o(i.comm_protocol_send_data) refers to comm_protocol.o(i.comm_protocol_send_frame) for comm_protocol_send_frame
    comm_protocol.o(i.comm_protocol_send_frame) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    comm_protocol.o(i.comm_protocol_send_frame) refers to comm_protocol.o(i.comm_protocol_calculate_crc16) for comm_protocol_calculate_crc16
    comm_protocol.o(i.comm_protocol_send_frame) refers to comm_protocol.o(.data) for .data
    comm_protocol.o(i.comm_protocol_send_frame) refers to comm_protocol.o(.bss) for .bss
    comm_protocol.o(i.comm_protocol_send_heartbeat) refers to comm_protocol.o(i.comm_protocol_send_frame) for comm_protocol_send_frame
    comm_protocol.o(i.comm_protocol_send_nack) refers to comm_protocol.o(i.comm_protocol_send_frame) for comm_protocol_send_frame
    comm_protocol.o(i.comm_protocol_update) refers to comm_protocol.o(.data) for .data
    comm_protocol.o(i.comm_protocol_update) refers to comm_protocol.o(.bss) for .bss
    comm_protocol.o(i.comm_protocol_validate_frame) refers to comm_protocol.o(i.comm_protocol_calculate_crc16) for comm_protocol_calculate_crc16
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memmove_v6.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    rt_memmove_v6.o(.text) refers to rt_memmove_w.o(.text) for __memmove_aligned
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    atan2f.o(i.__hardfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to _rserrno.o(.text) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    cosf.o(i.__hardfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to _rserrno.o(.text) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to _rserrno.o(.text) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    fmodf.o(i.__hardfp_fmodf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf.o(i.__hardfp_fmodf) refers to frem_clz.o(x$fpl$frem) for _frem
    fmodf.o(i.__hardfp_fmodf) refers to _rserrno.o(.text) for __set_errno
    fmodf.o(i.__hardfp_fmodf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    fmodf.o(i.__softfp_fmodf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf.o(i.__softfp_fmodf) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    fmodf.o(i.fmodf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf.o(i.fmodf) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    fmodf_x.o(i.____hardfp_fmodf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf_x.o(i.____hardfp_fmodf$lsc) refers to frem_clz.o(x$fpl$frem) for _frem
    fmodf_x.o(i.____hardfp_fmodf$lsc) refers to _rserrno.o(.text) for __set_errno
    fmodf_x.o(i.____softfp_fmodf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf_x.o(i.____softfp_fmodf$lsc) refers to fmodf_x.o(i.____hardfp_fmodf$lsc) for ____hardfp_fmodf$lsc
    fmodf_x.o(i.__fmodf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf_x.o(i.__fmodf$lsc) refers to fmodf_x.o(i.____hardfp_fmodf$lsc) for ____hardfp_fmodf$lsc
    powf.o(i.__hardfp_powf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf.o(i.__hardfp_powf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    powf.o(i.__hardfp_powf) refers to _rserrno.o(.text) for __set_errno
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_overflow) for __mathlib_flt_overflow
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    powf.o(i.__hardfp_powf) refers to powf.o(.constdata) for .constdata
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    powf.o(i.__softfp_powf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf.o(i.__softfp_powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(i.powf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf.o(i.powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers to _rserrno.o(.text) for __set_errno
    powf_x.o(i.____hardfp_powf$lsc) refers to powf_x.o(.constdata) for .constdata
    powf_x.o(i.____hardfp_powf$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf_x.o(i.____softfp_powf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.____softfp_powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(i.__powf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.__powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    rt_memmove_w.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    frem_clz.o(x$fpl$frem) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frem_clz.o(x$fpl$frem) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32h750xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m_pel.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m_pel.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_it.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_it.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_it.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_msp.o(i.HAL_ADC_MspDeInit), (40 bytes).
    Removing stm32h7xx_hal_msp.o(i.HAL_DAC_MspDeInit), (40 bytes).
    Removing stm32h7xx_hal_msp.o(i.HAL_QSPI_MspDeInit), (72 bytes).
    Removing stm32h7xx_hal_msp.o(i.HAL_QSPI_MspInit), (240 bytes).
    Removing stm32h7xx_hal_msp.o(i.HAL_UART_MspDeInit), (44 bytes).
    Removing stm32h7xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (752 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit), (564 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler), (656 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_PollForConversion), (292 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_PollForEvent), (198 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Start), (264 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Start_IT), (392 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Stop), (62 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32h7xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels), (44 bytes).
    Removing stm32h7xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_GetValue), (22 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue), (122 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue), (48 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator), (32 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue), (48 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EnterADCDeepPowerDownMode), (36 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (1484 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (46 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (276 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (252 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (312 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (84 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (94 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_FactorLoad), (84 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_GetValue), (164 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue), (248 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (40 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (280 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (240 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA), (260 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop), (92 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA), (138 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT), (102 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel), (12 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing), (8 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.LL_ADC_SetCalibrationLinearFactor), (48 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.LL_ADC_SetOffset), (22 bytes).
    Removing stm32h7xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit), (496 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (16 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (16 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (92 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (308 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (148 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (108 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (40 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (136 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (116 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (28 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (52 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq), (36 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq), (68 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (452 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_KerWakeUpStopCLKConfig), (20 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_WWDGxSysResetConfig), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_WakeUpStopCLKConfig), (20 bytes).
    Removing stm32h7xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation), (112 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation), (80 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation), (120 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (204 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Lock), (28 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (44 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (28 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Program), (148 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT), (128 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32h7xx_hal_flash.o(.bss), (28 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (36 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (132 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC), (188 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (196 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (144 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Lock_Bank1), (20 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (164 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (272 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Unlock_Bank1), (44 bytes).
    Removing stm32h7xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit), (368 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32h7xx_hal_hsem.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_hsem.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_hsem.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_ActivateNotification), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_DeactivateNotification), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_FastTake), (32 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_FreeCallback), (2 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_GetClearKey), (12 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_IsSemTaken), (20 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_Release), (20 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_ReleaseAll), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_SetClearKey), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_Take), (40 bytes).
    Removing stm32h7xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (180 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask), (156 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask), (116 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam), (84 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT), (644 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit), (488 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler), (1768 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Init), (964 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (1118 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (90 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Start), (368 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (104 bytes).
    Removing stm32h7xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (160 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (144 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxRequestGenerator), (176 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxSync), (216 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_DisableMuxRequestGenerator), (26 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_EnableMuxRequestGenerator), (26 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MUX_IRQHandler), (100 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (640 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (1128 bytes).
    Removing stm32h7xx_hal_mdma.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort), (116 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort_IT), (38 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_ConfigPostRequestMask), (88 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_DeInit), (86 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GenerateSWRequest), (52 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GetError), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GetState), (6 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_IRQHandler), (376 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init), (94 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_AddNode), (208 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_CreateNode), (268 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_DisableCircularMode), (78 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_EnableCircularMode), (78 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_RemoveNode), (188 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer), (246 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_RegisterCallback), (88 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start), (102 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT), (154 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_UnRegisterCallback), (104 bytes).
    Removing stm32h7xx_hal_mdma.o(i.MDMA_Init), (176 bytes).
    Removing stm32h7xx_hal_mdma.o(i.MDMA_SetConfig), (128 bytes).
    Removing stm32h7xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (136 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DeInit), (2 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (24 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (28 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (48 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (80 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (34 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_AVDCallback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearPendingEvent), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearWakeupFlag), (28 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigAVD), (132 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigD3Domain), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlStopModeVoltageScaling), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (204 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableAVD), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableMonitoring), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBVoltageDetector), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableWakeUpPin), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableAVD), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableMonitoring), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBVoltageDetector), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableWakeUpPin), (136 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTANDBYMode), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOPMode), (100 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetStopModeVoltageRange), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetSupplyConfig), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetTemperatureLevel), (32 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetVBATLevel), (32 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetWakeupFlag), (12 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler), (96 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler), (124 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP1_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP2_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP3_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP4_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP5_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP6_Callback), (2 bytes).
    Removing stm32h7xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32h7xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DeInit), (120 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableCompensationCell), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D1_ClearFlag), (22 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D1_EventInputConfig), (66 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D3_EventInputConfig), (70 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_EdgeConfig), (58 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_GenerateSWInterrupt), (24 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableCompensationCell), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetFMCMemorySwappingConfig), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32h7xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_AnalogSwitchConfig), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CM7BootAddConfig), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CompensationCodeConfig), (24 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CompensationCodeSelect), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableBOOST), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableIOSpeedOptimize), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_ETHInterfaceSelect), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableBOOST), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableIOSpeedOptimize), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SetFMCMemorySwappingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32h7xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_DeInit), (52 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (54 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (104 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (16 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (40 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Init), (192 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (296 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (128 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (292 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (300 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (132 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (324 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (160 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (400 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (224 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (332 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (172 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (356 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (168 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (344 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (176 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (312 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (232 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (96 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (368 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (208 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (364 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (208 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (384 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (128 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (26 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAAbort), (20 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAError), (332 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (82 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (82 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (164 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (30 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ), (96 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ), (136 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR), (34 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt), (148 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITError), (288 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt), (100 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt), (244 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt), (80 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt), (592 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt), (114 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT), (304 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA), (316 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT), (328 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead), (100 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (100 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA), (524 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_TransferConfig), (48 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback), (42 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout), (124 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (168 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (88 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout), (92 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter), (88 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter), (84 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableFastModePlus), (40 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableWakeUp), (80 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus), (40 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableWakeUp), (80 bytes).
    Removing stm32h7xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (200 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (28 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (236 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetPending), (32 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (48 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (18 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (268 bytes).
    Removing stm32h7xx_hal_dac.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dac.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dac.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dac.o(i.DAC_DMAConvCpltCh1), (16 bytes).
    Removing stm32h7xx_hal_dac.o(i.DAC_DMAErrorCh1), (24 bytes).
    Removing stm32h7xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1), (10 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1), (2 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1), (2 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1), (2 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_DeInit), (30 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1), (2 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_GetError), (4 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_GetState), (4 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_GetValue), (12 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_IRQHandler), (102 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_MspInit), (2 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_Start_DMA), (224 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_Stop), (32 bytes).
    Removing stm32h7xx_hal_dac.o(i.HAL_DAC_Stop_DMA), (90 bytes).
    Removing stm32h7xx_hal_dac_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dac_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dac_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2), (16 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.DAC_DMAErrorCh2), (24 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2), (10 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2), (2 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2), (2 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2), (2 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualGetValue), (12 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualSetValue), (30 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart), (96 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStart_DMA), (220 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStop), (34 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_DualStop_DMA), (88 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2), (2 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_GetTrimOffset), (18 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_NoiseWaveGenerate), (62 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_SelfCalibrate), (320 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_SetUserTrimming), (70 bytes).
    Removing stm32h7xx_hal_dac_ex.o(i.HAL_DACEx_TriangleWaveGenerate), (62 bytes).
    Removing stm32h7xx_hal_qspi.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_qspi.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_qspi.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort), (142 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_AbortCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Abort_IT), (128 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling), (172 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_AutoPolling_IT), (162 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_CmdCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command), (134 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Command_IT), (128 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_DeInit), (36 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_FifoThresholdCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_GetError), (4 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_GetFifoThreshold), (12 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_GetState), (6 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_IRQHandler), (484 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Init), (172 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_MemoryMapped), (140 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_MspInit), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive), (190 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA), (292 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Receive_IT), (120 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_RxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_SetFifoThreshold), (66 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_SetFlashID), (58 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_SetTimeout), (4 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_StatusMatchCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_TimeOutCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit), (182 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA), (268 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_Transmit_IT), (110 bytes).
    Removing stm32h7xx_hal_qspi.o(i.HAL_QSPI_TxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_qspi.o(i.QSPI_Config), (408 bytes).
    Removing stm32h7xx_hal_qspi.o(i.QSPI_DMAAbortCplt), (58 bytes).
    Removing stm32h7xx_hal_qspi.o(i.QSPI_DMAError), (30 bytes).
    Removing stm32h7xx_hal_qspi.o(i.QSPI_DMARxCplt), (18 bytes).
    Removing stm32h7xx_hal_qspi.o(i.QSPI_DMATxCplt), (18 bytes).
    Removing stm32h7xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout), (72 bytes).
    Removing stm32h7xx_ll_delayblock.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_ll_delayblock.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_ll_delayblock.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_ll_delayblock.o(i.DelayBlock_Configure), (24 bytes).
    Removing stm32h7xx_ll_delayblock.o(i.DelayBlock_Disable), (8 bytes).
    Removing stm32h7xx_ll_delayblock.o(i.DelayBlock_Enable), (150 bytes).
    Removing stm32h7xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init), (116 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_LIN_Init), (140 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_LIN_SendBreak), (50 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (52 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (52 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init), (138 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Abort), (256 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive), (172 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (188 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit), (140 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (152 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT), (292 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAPause), (116 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAResume), (108 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop), (146 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DeInit), (70 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DisableReceiverTimeout), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_EnableReceiverTimeout), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_GetState), (14 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler), (924 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Receive), (232 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA), (72 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT), (72 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_ReceiverTimeout_Config), (24 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT), (152 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError), (16 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt), (130 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback), (68 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt), (32 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (40 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (40 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT), (200 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN), (416 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT), (200 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN), (416 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA), (168 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT), (272 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT), (82 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN), (106 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT), (78 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN), (102 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (48 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (140 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (46 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode), (72 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (46 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle), (304 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback), (2 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (140 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback), (2 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback), (2 bytes).
    Removing system_stm32h7xx.o(.rev16_text), (4 bytes).
    Removing system_stm32h7xx.o(.revsh_text), (4 bytes).
    Removing system_stm32h7xx.o(.rrx_text), (6 bytes).
    Removing system_stm32h7xx.o(i.SystemCoreClockUpdate), (320 bytes).
    Removing app_main.o(.rev16_text), (4 bytes).
    Removing app_main.o(.revsh_text), (4 bytes).
    Removing app_main.o(.rrx_text), (6 bytes).
    Removing app_main.o(i.app_get_state), (12 bytes).
    Removing app_main.o(i.app_run), (96 bytes).
    Removing app_main.o(i.app_systick_handler), (20 bytes).
    Removing app_melp.o(i.app_melp_decode_frame), (108 bytes).
    Removing app_melp.o(i.app_melp_deinit), (56 bytes).
    Removing app_melp.o(i.app_melp_get_state), (20 bytes).
    Removing app_melp.o(i.app_melp_get_statistics), (46 bytes).
    Removing app_melp.o(i.app_melp_process_received_frame), (116 bytes).
    Removing app_melp.o(i.app_melp_reset_statistics), (42 bytes).
    Removing app_melp.o(i.app_melp_start_playback), (40 bytes).
    Removing app_melp.o(i.app_melp_stop_playback), (34 bytes).
    Removing app_state_machine.o(i.app_sm_deinit), (48 bytes).
    Removing app_state_machine.o(i.app_sm_force_state), (22 bytes).
    Removing app_state_machine.o(i.app_sm_get_error_name), (28 bytes).
    Removing app_state_machine.o(i.app_sm_get_event_name), (28 bytes).
    Removing app_state_machine.o(i.app_sm_get_state), (16 bytes).
    Removing app_state_machine.o(i.app_sm_get_state_name), (28 bytes).
    Removing app_state_machine.o(i.app_sm_get_statistics), (52 bytes).
    Removing app_state_machine.o(i.app_sm_handle_state_entry), (92 bytes).
    Removing app_state_machine.o(i.app_sm_handle_state_exit), (36 bytes).
    Removing app_state_machine.o(i.app_sm_handle_timeout), (70 bytes).
    Removing app_state_machine.o(i.app_sm_init), (80 bytes).
    Removing app_state_machine.o(i.app_sm_is_valid_transition), (100 bytes).
    Removing app_state_machine.o(i.app_sm_process_event), (170 bytes).
    Removing app_state_machine.o(i.app_sm_report_error), (80 bytes).
    Removing app_state_machine.o(i.app_sm_reset_statistics), (58 bytes).
    Removing app_state_machine.o(i.app_sm_set_debug_mode), (18 bytes).
    Removing app_state_machine.o(i.app_sm_transition_to), (108 bytes).
    Removing app_state_machine.o(i.app_sm_update), (84 bytes).
    Removing app_state_machine.o(.bss), (20 bytes).
    Removing app_state_machine.o(.conststring), (493 bytes).
    Removing app_state_machine.o(.data), (144 bytes).
    Removing hal_adc.o(.rev16_text), (4 bytes).
    Removing hal_adc.o(.revsh_text), (4 bytes).
    Removing hal_adc.o(.rrx_text), (6 bytes).
    Removing hal_adc.o(i.hal_adc_deinit), (44 bytes).
    Removing hal_adc.o(i.hal_adc_get_vref_mv), (6 bytes).
    Removing hal_adc.o(i.hal_adc_is_ready), (36 bytes).
    Removing hal_adc.o(i.hal_adc_read_single), (68 bytes).
    Removing hal_adc.o(i.hal_adc_to_voltage_mv), (18 bytes).
    Removing hal_dac.o(.rev16_text), (4 bytes).
    Removing hal_dac.o(.revsh_text), (4 bytes).
    Removing hal_dac.o(.rrx_text), (6 bytes).
    Removing hal_dac.o(i.HAL_DAC_ConvCpltCallback), (4 bytes).
    Removing hal_dac.o(i.HAL_DAC_ConvHalfCpltCallback), (4 bytes).
    Removing hal_dac.o(i.hal_dac_deinit), (56 bytes).
    Removing hal_dac.o(i.hal_dac_dma_callback), (28 bytes).
    Removing hal_dac.o(i.hal_dac_is_ready), (32 bytes).
    Removing hal_dac.o(i.hal_dac_mute), (6 bytes).
    Removing hal_dac.o(i.hal_dac_start_continuous), (52 bytes).
    Removing hal_dac.o(i.hal_dac_stop_continuous), (40 bytes).
    Removing hal_dac.o(i.hal_dac_voltage_to_value), (26 bytes).
    Removing hal_gpio.o(.rev16_text), (4 bytes).
    Removing hal_gpio.o(.revsh_text), (4 bytes).
    Removing hal_gpio.o(.rrx_text), (6 bytes).
    Removing hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (40 bytes).
    Removing hal_gpio.o(i.hal_gpio_button_poll), (36 bytes).
    Removing hal_gpio.o(i.hal_gpio_deinit), (28 bytes).
    Removing hal_gpio.o(i.hal_gpio_disable_button_interrupt), (60 bytes).
    Removing hal_gpio.o(i.hal_gpio_enable_button_interrupt), (72 bytes).
    Removing hal_gpio.o(i.hal_gpio_get_led_state), (40 bytes).
    Removing hal_gpio.o(i.hal_gpio_toggle_led), (32 bytes).
    Removing hal_uart.o(.rev16_text), (4 bytes).
    Removing hal_uart.o(.revsh_text), (4 bytes).
    Removing hal_uart.o(.rrx_text), (6 bytes).
    Removing hal_uart.o(i.HAL_UARTEx_RxEventCallback), (52 bytes).
    Removing hal_uart.o(i.HAL_UART_RxCpltCallback), (4 bytes).
    Removing hal_uart.o(i.hal_uart_deinit), (48 bytes).
    Removing hal_uart.o(i.hal_uart_get_baud_rate), (6 bytes).
    Removing hal_uart.o(i.hal_uart_is_receiving), (36 bytes).
    Removing hal_uart.o(i.hal_uart_is_tx_ready), (36 bytes).
    Removing hal_uart.o(i.hal_uart_rx_dma_callback), (176 bytes).
    Removing hal_uart.o(i.hal_uart_send_sync), (44 bytes).
    Removing hal_uart.o(i.hal_uart_set_error_callback), (12 bytes).
    Removing hal_uart.o(i.hal_uart_start_receive), (48 bytes).
    Removing hal_uart.o(i.hal_uart_stop_receive), (36 bytes).
    Removing hal_uart.o(.bss), (512 bytes).
    Removing melp_decoder.o(i.melp_adaptive_spectral_enhancement), (56 bytes).
    Removing melp_decoder.o(i.melp_decoder_decode_frame), (300 bytes).
    Removing melp_decoder.o(i.melp_decoder_deinit), (18 bytes).
    Removing melp_decoder.o(i.melp_decoder_frame_erasure), (160 bytes).
    Removing melp_decoder.o(i.melp_decoder_get_version), (24 bytes).
    Removing melp_decoder.o(i.melp_decoder_unpack_frame), (40 bytes).
    Removing melp_decoder.o(i.melp_dequantize_parameters), (212 bytes).
    Removing melp_decoder.o(i.melp_generate_excitation), (320 bytes).
    Removing melp_decoder.o(i.melp_lpc_synthesis), (140 bytes).
    Removing melp_decoder.o(i.melp_lsf_to_lpc), (76 bytes).
    Removing melp_decoder.o(i.melp_post_filter), (84 bytes).
    Removing melp_decoder.o(i.melp_pulse_dispersion), (50 bytes).
    Removing melp_decoder_optimized.o(i.melp_decoder_decode_frame_fast), (4 bytes).
    Removing melp_decoder_optimized.o(i.melp_decoder_decode_frame_optimized), (380 bytes).
    Removing melp_decoder_optimized.o(i.melp_decoder_deinit_fast), (4 bytes).
    Removing melp_decoder_optimized.o(i.melp_decoder_deinit_optimized), (44 bytes).
    Removing melp_decoder_optimized.o(i.melp_decoder_init_fast), (4 bytes).
    Removing melp_decoder_optimized.o(i.melp_decoder_init_optimized), (48 bytes).
    Removing melp_decoder_optimized.o(i.melp_generate_excitation_optimized), (268 bytes).
    Removing melp_decoder_optimized.o(i.melp_generate_pulse_train_optimized), (164 bytes).
    Removing melp_decoder_optimized.o(.bss), (2900 bytes).
    Removing melp_decoder_optimized.o(.data), (8 bytes).
    Removing melp_dsp.o(i.melp_dsp_autocorrelation), (96 bytes).
    Removing melp_dsp.o(i.melp_dsp_deinit), (18 bytes).
    Removing melp_dsp.o(i.melp_dsp_dot_product), (44 bytes).
    Removing melp_dsp.o(i.melp_dsp_float_to_q15), (100 bytes).
    Removing melp_dsp.o(i.melp_dsp_init), (26 bytes).
    Removing melp_dsp.o(i.melp_dsp_levinson_durbin), (248 bytes).
    Removing melp_dsp.o(i.melp_dsp_lpc_synthesis), (148 bytes).
    Removing melp_dsp.o(i.melp_dsp_pitch_estimation), (128 bytes).
    Removing melp_dsp.o(i.melp_dsp_post_filter), (80 bytes).
    Removing melp_dsp.o(i.melp_dsp_preemphasis), (80 bytes).
    Removing melp_dsp.o(i.melp_dsp_q15_to_float), (48 bytes).
    Removing melp_dsp.o(i.melp_dsp_spectral_analysis), (216 bytes).
    Removing melp_dsp.o(i.melp_dsp_vector_scale), (36 bytes).
    Removing melp_dsp.o(i.melp_dsp_windowing), (54 bytes).
    Removing melp_encoder.o(i.melp_encoder_get_version), (24 bytes).
    Removing melp_encoder_optimized.o(i.melp_encoder_deinit_fast), (4 bytes).
    Removing melp_encoder_optimized.o(i.melp_encoder_deinit_optimized), (32 bytes).
    Removing melp_encoder_optimized.o(i.melp_encoder_encode_frame_fast), (4 bytes).
    Removing melp_encoder_optimized.o(i.melp_encoder_encode_frame_optimized), (324 bytes).
    Removing melp_encoder_optimized.o(i.melp_encoder_init_fast), (4 bytes).
    Removing melp_encoder_optimized.o(i.melp_encoder_init_optimized), (140 bytes).
    Removing melp_encoder_optimized.o(i.melp_voicing_analysis_optimized), (408 bytes).
    Removing melp_encoder_optimized.o(.bss), (3620 bytes).
    Removing melp_encoder_optimized.o(.data), (1 bytes).
    Removing comm_protocol.o(i.comm_protocol_calculate_crc16), (60 bytes).
    Removing comm_protocol.o(i.comm_protocol_deinit), (28 bytes).
    Removing comm_protocol.o(i.comm_protocol_get_stats), (34 bytes).
    Removing comm_protocol.o(i.comm_protocol_init), (60 bytes).
    Removing comm_protocol.o(i.comm_protocol_process_received_data), (508 bytes).
    Removing comm_protocol.o(i.comm_protocol_reset_stats), (26 bytes).
    Removing comm_protocol.o(i.comm_protocol_send_ack), (30 bytes).
    Removing comm_protocol.o(i.comm_protocol_send_data), (64 bytes).
    Removing comm_protocol.o(i.comm_protocol_send_frame), (172 bytes).
    Removing comm_protocol.o(i.comm_protocol_send_heartbeat), (22 bytes).
    Removing comm_protocol.o(i.comm_protocol_send_nack), (30 bytes).
    Removing comm_protocol.o(i.comm_protocol_update), (92 bytes).
    Removing comm_protocol.o(i.comm_protocol_validate_frame), (46 bytes).
    Removing comm_protocol.o(.bss), (16 bytes).
    Removing comm_protocol.o(.data), (1 bytes).

749 unused section(s) (total 73067 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32h7xx_hal_msp.c          0x00000000   Number         0  stm32h7xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32h7xx_it.c               0x00000000   Number         0  stm32h7xx_it.o ABSOLUTE
    ../Core/Src/system_stm32h7xx.c           0x00000000   Number         0  system_stm32h7xx.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c 0x00000000   Number         0  stm32h7xx_hal.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c 0x00000000   Number         0  stm32h7xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c 0x00000000   Number         0  stm32h7xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c 0x00000000   Number         0  stm32h7xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dac.c 0x00000000   Number         0  stm32h7xx_hal_dac.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dac_ex.c 0x00000000   Number         0  stm32h7xx_hal_dac_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c 0x00000000   Number         0  stm32h7xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c 0x00000000   Number         0  stm32h7xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c 0x00000000   Number         0  stm32h7xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c 0x00000000   Number         0  stm32h7xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c 0x00000000   Number         0  stm32h7xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c 0x00000000   Number         0  stm32h7xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c 0x00000000   Number         0  stm32h7xx_hal_hsem.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c 0x00000000   Number         0  stm32h7xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c 0x00000000   Number         0  stm32h7xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c 0x00000000   Number         0  stm32h7xx_hal_mdma.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c 0x00000000   Number         0  stm32h7xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c 0x00000000   Number         0  stm32h7xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_qspi.c 0x00000000   Number         0  stm32h7xx_hal_qspi.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c 0x00000000   Number         0  stm32h7xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c 0x00000000   Number         0  stm32h7xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c 0x00000000   Number         0  stm32h7xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c 0x00000000   Number         0  stm32h7xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart.c 0x00000000   Number         0  stm32h7xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart_ex.c 0x00000000   Number         0  stm32h7xx_hal_uart_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_delayblock.c 0x00000000   Number         0  stm32h7xx_ll_delayblock.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memmove_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m_pel.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memmove_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/frem.s                          0x00000000   Number         0  frem_clz.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf_x.o ABSOLUTE
    ../mathlib/fmodf.c                       0x00000000   Number         0  fmodf.o ABSOLUTE
    ../mathlib/fmodf.c                       0x00000000   Number         0  fmodf_x.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf_x.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ..\Algorithm\src\melp_decoder.c          0x00000000   Number         0  melp_decoder.o ABSOLUTE
    ..\Algorithm\src\melp_decoder_optimized.c 0x00000000   Number         0  melp_decoder_optimized.o ABSOLUTE
    ..\Algorithm\src\melp_dsp.c              0x00000000   Number         0  melp_dsp.o ABSOLUTE
    ..\Algorithm\src\melp_encoder.c          0x00000000   Number         0  melp_encoder.o ABSOLUTE
    ..\Algorithm\src\melp_encoder_optimized.c 0x00000000   Number         0  melp_encoder_optimized.o ABSOLUTE
    ..\App\src\app_main.c                    0x00000000   Number         0  app_main.o ABSOLUTE
    ..\App\src\app_melp.c                    0x00000000   Number         0  app_melp.o ABSOLUTE
    ..\App\src\app_state_machine.c           0x00000000   Number         0  app_state_machine.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32h7xx_hal_msp.c          0x00000000   Number         0  stm32h7xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32h7xx_it.c               0x00000000   Number         0  stm32h7xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32h7xx.c           0x00000000   Number         0  system_stm32h7xx.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal.c 0x00000000   Number         0  stm32h7xx_hal.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_adc.c 0x00000000   Number         0  stm32h7xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_adc_ex.c 0x00000000   Number         0  stm32h7xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_cortex.c 0x00000000   Number         0  stm32h7xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dac.c 0x00000000   Number         0  stm32h7xx_hal_dac.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dac_ex.c 0x00000000   Number         0  stm32h7xx_hal_dac_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma.c 0x00000000   Number         0  stm32h7xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma_ex.c 0x00000000   Number         0  stm32h7xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_exti.c 0x00000000   Number         0  stm32h7xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_flash.c 0x00000000   Number         0  stm32h7xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_flash_ex.c 0x00000000   Number         0  stm32h7xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_gpio.c 0x00000000   Number         0  stm32h7xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_hsem.c 0x00000000   Number         0  stm32h7xx_hal_hsem.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2c.c 0x00000000   Number         0  stm32h7xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2c_ex.c 0x00000000   Number         0  stm32h7xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_mdma.c 0x00000000   Number         0  stm32h7xx_hal_mdma.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr.c 0x00000000   Number         0  stm32h7xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr_ex.c 0x00000000   Number         0  stm32h7xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_qspi.c 0x00000000   Number         0  stm32h7xx_hal_qspi.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc.c 0x00000000   Number         0  stm32h7xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc_ex.c 0x00000000   Number         0  stm32h7xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_tim.c 0x00000000   Number         0  stm32h7xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_tim_ex.c 0x00000000   Number         0  stm32h7xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_uart.c 0x00000000   Number         0  stm32h7xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_uart_ex.c 0x00000000   Number         0  stm32h7xx_hal_uart_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_delayblock.c 0x00000000   Number         0  stm32h7xx_ll_delayblock.o ABSOLUTE
    ..\HAL\Src\hal_adc.c                     0x00000000   Number         0  hal_adc.o ABSOLUTE
    ..\HAL\Src\hal_dac.c                     0x00000000   Number         0  hal_dac.o ABSOLUTE
    ..\HAL\Src\hal_gpio.c                    0x00000000   Number         0  hal_gpio.o ABSOLUTE
    ..\HAL\Src\hal_uart.c                    0x00000000   Number         0  hal_uart.o ABSOLUTE
    ..\Middleware\src\comm_protocol.c        0x00000000   Number         0  comm_protocol.o ABSOLUTE
    ..\\App\\src\\app_main.c                 0x00000000   Number         0  app_main.o ABSOLUTE
    ..\\HAL\\Src\\hal_adc.c                  0x00000000   Number         0  hal_adc.o ABSOLUTE
    ..\\HAL\\Src\\hal_dac.c                  0x00000000   Number         0  hal_dac.o ABSOLUTE
    ..\\HAL\\Src\\hal_gpio.c                 0x00000000   Number         0  hal_gpio.o ABSOLUTE
    ..\\HAL\\Src\\hal_uart.c                 0x00000000   Number         0  hal_uart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32h750xx.s                    0x00000000   Number         0  startup_stm32h750xx.o ABSOLUTE
    RESET                                    0x08000000   Section      664  startup_stm32h750xx.o(RESET)
    !!!main                                  0x08000298   Section        8  __main.o(!!!main)
    !!!scatter                               0x080002a0   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080002d4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080002f0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800030c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x0800030c   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000312   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000318   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800031e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000324   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800032a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000330   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800033a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000340   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000346   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800034c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000352   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000358   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800035e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000364   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800036a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x08000370   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x08000376   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000380   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000386   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x0800038c   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x08000392   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x08000398   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800039c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800039e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080003a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080003a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080003a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080003a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080003a2   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080003a8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080003a8   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080003b4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080003b4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080003b4   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080003be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080003be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080003be   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080003be   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080003be   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080003be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080003be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080003be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080003be   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080003be   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080003be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080003be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080003be   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080003c0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080003c2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080003c2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080003c2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080003c2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080003c2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080003c2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080003c2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080003c2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080003c4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080003c4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080003c4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080003ca   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080003ca   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080003ce   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080003ce   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080003d6   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080003d8   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080003d8   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080003dc   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080003e4   Section       72  startup_stm32h750xx.o(.text)
    $v0                                      0x080003e4   Number         0  startup_stm32h750xx.o(.text)
    .text                                    0x0800042c   Section      238  lludivv7m.o(.text)
    .text                                    0x0800051c   Section        0  vsnprintf.o(.text)
    .text                                    0x08000550   Section        0  strlen.o(.text)
    .text                                    0x0800058e   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08000618   Section      132  rt_memmove_v6.o(.text)
    .text                                    0x0800069c   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000700   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800074e   Section        0  heapauxi.o(.text)
    .text                                    0x08000754   Section        0  _rserrno.o(.text)
    .text                                    0x0800076a   Section        0  _printf_pad.o(.text)
    .text                                    0x080007b8   Section        0  _printf_truncate.o(.text)
    .text                                    0x080007dc   Section        0  _printf_str.o(.text)
    .text                                    0x08000830   Section        0  _printf_dec.o(.text)
    .text                                    0x080008a8   Section        0  _printf_charcount.o(.text)
    .text                                    0x080008d0   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x080008d1   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000900   Section        0  _sputc.o(.text)
    .text                                    0x0800090a   Section        0  _snputc.o(.text)
    .text                                    0x0800091c   Section        0  _printf_wctomb.o(.text)
    .text                                    0x080009d8   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000a54   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08000a55   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000ac4   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000ac5   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000b58   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000ce0   Section      122  rt_memmove_w.o(.text)
    .text                                    0x08000d5c   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000d64   Section      138  lludiv10.o(.text)
    .text                                    0x08000dee   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000ea0   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000ea3   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x080012c0   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x080015bc   Section        0  _printf_char.o(.text)
    .text                                    0x080015e8   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001614   Section        0  _wcrtomb.o(.text)
    .text                                    0x08001654   Section        8  libspace.o(.text)
    .text                                    0x0800165c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080016a8   Section       16  rt_ctype_table.o(.text)
    .text                                    0x080016b8   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080016c0   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001740   Section        0  bigflt0.o(.text)
    .text                                    0x08001824   Section        0  exit.o(.text)
    .text                                    0x08001838   Section      104  strcmpv7m_pel.o(.text)
    .text                                    0x080018a0   Section        0  sys_exit.o(.text)
    .text                                    0x080018ac   Section        2  use_no_semi.o(.text)
    .text                                    0x080018ae   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x080018ae   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080018ec   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001932   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08001992   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08001cca   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001da6   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001dd0   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001dfa   Section      580  btod.o(CL$$btod_mult_common)
    i.ADC_ConfigureBoostMode                 0x08002040   Section        0  stm32h7xx_hal_adc.o(i.ADC_ConfigureBoostMode)
    i.ADC_ConversionStop                     0x08002148   Section        0  stm32h7xx_hal_adc.o(i.ADC_ConversionStop)
    i.ADC_DMAConvCplt                        0x0800222c   Section        0  stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x08002294   Section        0  stm32h7xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x080022ae   Section        0  stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_Disable                            0x080022b8   Section        0  stm32h7xx_hal_adc.o(i.ADC_Disable)
    i.ADC_Enable                             0x08002328   Section        0  stm32h7xx_hal_adc.o(i.ADC_Enable)
    i.BusFault_Handler                       0x080023d0   Section        0  stm32h7xx_it.o(i.BusFault_Handler)
    i.DMA_SetConfig                          0x080023d4   Section        0  stm32h7xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x080023d5   Thumb Code   518  stm32h7xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x080025f4   Section        0  stm32h7xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x080025f8   Section        0  main.o(i.Error_Handler)
    i.ExitRun0Mode                           0x080026c4   Section        0  system_stm32h7xx.o(i.ExitRun0Mode)
    i.HAL_ADCEx_Calibration_Start            0x080026e0   Section        0  stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start)
    i.HAL_ADCEx_MultiModeConfigChannel       0x0800277c   Section        0  stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel)
    i.HAL_ADC_ConfigChannel                  0x080028a4   Section        0  stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08002cc4   Section        0  hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x08002d00   Section        0  hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x08002d30   Section        0  stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_Init                           0x08002d34   Section        0  stm32h7xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08002f5c   Section        0  stm32h7xx_hal_msp.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x08002ff4   Section        0  stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_ADC_Stop_DMA                       0x080030fc   Section        0  stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    i.HAL_DAC_ConfigChannel                  0x08003178   Section        0  stm32h7xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    i.HAL_DAC_Init                           0x080032c4   Section        0  stm32h7xx_hal_dac.o(i.HAL_DAC_Init)
    i.HAL_DAC_MspInit                        0x080032ec   Section        0  stm32h7xx_hal_msp.o(i.HAL_DAC_MspInit)
    i.HAL_DAC_SetValue                       0x0800334c   Section        0  stm32h7xx_hal_dac.o(i.HAL_DAC_SetValue)
    i.HAL_DAC_Start                          0x08003370   Section        0  stm32h7xx_hal_dac.o(i.HAL_DAC_Start)
    i.HAL_DMA_Abort                          0x080033d0   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Start_IT                       0x08003730   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x080039cc   Section        0  stm32h7xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x080039f0   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08003c1c   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_TogglePin                     0x08003c26   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x08003c36   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetDEVID                           0x08003c40   Section        0  stm32h7xx_hal.o(i.HAL_GetDEVID)
    i.HAL_GetHalVersion                      0x08003c50   Section        0  stm32h7xx_hal.o(i.HAL_GetHalVersion)
    i.HAL_GetREVID                           0x08003c58   Section        0  stm32h7xx_hal.o(i.HAL_GetREVID)
    i.HAL_GetTick                            0x08003c64   Section        0  stm32h7xx_hal.o(i.HAL_GetTick)
    i.HAL_GetUIDw0                           0x08003c70   Section        0  stm32h7xx_hal.o(i.HAL_GetUIDw0)
    i.HAL_GetUIDw1                           0x08003c7c   Section        0  stm32h7xx_hal.o(i.HAL_GetUIDw1)
    i.HAL_GetUIDw2                           0x08003c88   Section        0  stm32h7xx_hal.o(i.HAL_GetUIDw2)
    i.HAL_IncTick                            0x08003c94   Section        0  stm32h7xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08003ca4   Section        0  stm32h7xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08003d00   Section        0  stm32h7xx_hal.o(i.HAL_InitTick)
    i.HAL_MPU_ConfigRegion                   0x08003d40   Section        0  stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion)
    i.HAL_MPU_Disable                        0x08003d9c   Section        0  stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable)
    i.HAL_MPU_Enable                         0x08003db8   Section        0  stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable)
    i.HAL_MspInit                            0x08003ddc   Section        0  stm32h7xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_SetPriority                   0x08003df8   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08003e38   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ConfigSupply                 0x08003e5c   Section        0  stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply)
    i.HAL_RCCEx_GetD3PCLK1Freq               0x08003eb0   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq)
    i.HAL_RCCEx_GetPLL1ClockFreq             0x08003ed4   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL1ClockFreq)
    i.HAL_RCCEx_GetPLL2ClockFreq             0x0800401c   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq)
    i.HAL_RCCEx_GetPLL3ClockFreq             0x08004160   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq)
    i.HAL_RCCEx_GetPeriphCLKFreq             0x080042a4   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq)
    i.HAL_RCCEx_PeriphCLKConfig              0x0800456c   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08004f18   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08005174   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x080051b8   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080051dc   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08005200   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08005338   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x080058ba   Section        0  stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UARTEx_DisableFifoMode             0x080058e0   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    i.HAL_UARTEx_SetRxFifoThreshold          0x08005920   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    i.HAL_UARTEx_SetTxFifoThreshold          0x0800596c   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    i.HAL_UART_ErrorCallback                 0x080059b8   Section        0  hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_Init                          0x080059d8   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08005a44   Section        0  stm32h7xx_hal_msp.o(i.HAL_UART_MspInit)
    i.HAL_UART_Transmit                      0x08005acc   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_Transmit_DMA                  0x08005b7c   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA)
    i.HAL_UART_TxCpltCallback                0x08005c0c   Section        0  hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HAL_UART_TxHalfCpltCallback            0x08005c28   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback)
    i.HardFault_Handler                      0x08005c2a   Section        0  stm32h7xx_it.o(i.HardFault_Handler)
    i.LL_ADC_INJ_IsConversionOngoing         0x08005c2c   Section        0  stm32h7xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing)
    LL_ADC_INJ_IsConversionOngoing           0x08005c2d   Thumb Code     8  stm32h7xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing)
    i.LL_ADC_REG_IsConversionOngoing         0x08005c34   Section        0  stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    LL_ADC_REG_IsConversionOngoing           0x08005c35   Thumb Code     8  stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    i.LL_ADC_REG_IsConversionOngoing         0x08005c3c   Section        0  stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing)
    LL_ADC_REG_IsConversionOngoing           0x08005c3d   Thumb Code     8  stm32h7xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing)
    i.LL_ADC_REG_IsTriggerSourceSWStart      0x08005c44   Section        0  stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart)
    LL_ADC_REG_IsTriggerSourceSWStart        0x08005c45   Thumb Code    16  stm32h7xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart)
    i.MX_ADC1_Init                           0x08005c54   Section        0  main.o(i.MX_ADC1_Init)
    MX_ADC1_Init                             0x08005c55   Thumb Code   124  main.o(i.MX_ADC1_Init)
    i.MX_GPIO_Init                           0x08005cdc   Section        0  main.o(i.MX_GPIO_Init)
    MX_GPIO_Init                             0x08005cdd   Thumb Code   166  main.o(i.MX_GPIO_Init)
    i.MX_USART1_Init                         0x08005d8c   Section        0  main.o(i.MX_USART1_Init)
    MX_USART1_Init                           0x08005d8d   Thumb Code    78  main.o(i.MX_USART1_Init)
    i.MemManage_Handler                      0x08005de4   Section        0  stm32h7xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08005de6   Section        0  stm32h7xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08005de8   Section        0  stm32h7xx_it.o(i.PendSV_Handler)
    i.RCCEx_PLL2_Config                      0x08005dec   Section        0  stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config)
    RCCEx_PLL2_Config                        0x08005ded   Thumb Code   284  stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config)
    i.RCCEx_PLL3_Config                      0x08005f0c   Section        0  stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config)
    RCCEx_PLL3_Config                        0x08005f0d   Thumb Code   284  stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config)
    i.SVC_Handler                            0x0800602c   Section        0  stm32h7xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x0800602e   Section        0  stm32h7xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08006034   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080060d8   Section        0  system_stm32h7xx.o(i.SystemInit)
    i.UARTEx_SetNbDataToProcess              0x080061d0   Section        0  stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    UARTEx_SetNbDataToProcess                0x080061d1   Thumb Code    62  stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    i.UART_AdvFeatureConfig                  0x08006214   Section        0  stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x080062dc   Section        0  stm32h7xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_DMAError                          0x08006386   Section        0  stm32h7xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08006387   Thumb Code    78  stm32h7xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMATransmitCplt                   0x080063d4   Section        0  stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt)
    UART_DMATransmitCplt                     0x080063d5   Thumb Code    64  stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt)
    i.UART_DMATxHalfCplt                     0x08006414   Section        0  stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt)
    UART_DMATxHalfCplt                       0x08006415   Thumb Code    10  stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt)
    i.UART_EndRxTransfer                     0x08006420   Section        0  stm32h7xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08006421   Thumb Code    78  stm32h7xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x08006474   Section        0  stm32h7xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08006475   Thumb Code    46  stm32h7xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_SetConfig                         0x080064a4   Section        0  stm32h7xx_hal_uart.o(i.UART_SetConfig)
    i.UART_WaitOnFlagUntilTimeout            0x08006850   Section        0  stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.UsageFault_Handler                     0x080068e4   Section        0  stm32h7xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x080068e6   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x08006916   Section        0  stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08006917   Thumb Code    34  stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_cosf                          0x08006938   Section        0  cosf.o(i.__hardfp_cosf)
    i.__hardfp_sqrtf                         0x08006a88   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_infnan                   0x08006ac2   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x08006ac8   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_rredf2                       0x08006ad8   Section        0  rredf.o(i.__mathlib_rredf2)
    i._is_digit                              0x08006c2c   Section        0  __printf_wp.o(i._is_digit)
    i.app_adc_callback                       0x08006c3c   Section        0  app_main.o(i.app_adc_callback)
    app_adc_callback                         0x08006c3d   Thumb Code    22  app_main.o(i.app_adc_callback)
    i.app_button_callback                    0x08006c58   Section        0  app_main.o(i.app_button_callback)
    app_button_callback                      0x08006c59   Thumb Code    62  app_main.o(i.app_button_callback)
    i.app_init                               0x08006ca4   Section        0  app_main.o(i.app_init)
    i.app_melp_change_state                  0x08006d0c   Section        0  app_melp.o(i.app_melp_change_state)
    app_melp_change_state                    0x08006d0d   Thumb Code    34  app_melp.o(i.app_melp_change_state)
    i.app_melp_encode_frame                  0x08006d38   Section        0  app_melp.o(i.app_melp_encode_frame)
    app_melp_encode_frame                    0x08006d39   Thumb Code   110  app_melp.o(i.app_melp_encode_frame)
    i.app_melp_error_callback                0x08006db0   Section        0  app_main.o(i.app_melp_error_callback)
    app_melp_error_callback                  0x08006db1   Thumb Code     8  app_main.o(i.app_melp_error_callback)
    i.app_melp_frame_decoded_callback        0x08006dbc   Section        0  app_main.o(i.app_melp_frame_decoded_callback)
    app_melp_frame_decoded_callback          0x08006dbd   Thumb Code    50  app_main.o(i.app_melp_frame_decoded_callback)
    i.app_melp_frame_encoded_callback        0x08006df0   Section        0  app_main.o(i.app_melp_frame_encoded_callback)
    app_melp_frame_encoded_callback          0x08006df1   Thumb Code     6  app_main.o(i.app_melp_frame_encoded_callback)
    i.app_melp_init                          0x08006dfc   Section        0  app_melp.o(i.app_melp_init)
    i.app_melp_process_input                 0x08006e58   Section        0  app_melp.o(i.app_melp_process_input)
    i.app_melp_start_recording               0x08006f00   Section        0  app_melp.o(i.app_melp_start_recording)
    i.app_melp_state_changed_callback        0x08006f28   Section        0  app_main.o(i.app_melp_state_changed_callback)
    app_melp_state_changed_callback          0x08006f29   Thumb Code     2  app_main.o(i.app_melp_state_changed_callback)
    i.app_melp_stop_recording                0x08006f2a   Section        0  app_melp.o(i.app_melp_stop_recording)
    i.app_uart_tx_callback                   0x08006f4c   Section        0  app_main.o(i.app_uart_tx_callback)
    app_uart_tx_callback                     0x08006f4d   Thumb Code     2  app_main.o(i.app_uart_tx_callback)
    i.debug_init                             0x08006f50   Section        0  main.o(i.debug_init)
    i.debug_print_system_info                0x08006fd4   Section        0  main.o(i.debug_print_system_info)
    i.debug_printf                           0x08007148   Section        0  main.o(i.debug_printf)
    i.debug_test_button                      0x08007178   Section        0  main.o(i.debug_test_button)
    i.debug_test_led                         0x080072d0   Section        0  main.o(i.debug_test_led)
    i.debug_test_uart                        0x080073b0   Section        0  main.o(i.debug_test_uart)
    i.hal_adc_init                           0x08007480   Section        0  hal_adc.o(i.hal_adc_init)
    i.hal_adc_start_continuous               0x080074ac   Section        0  hal_adc.o(i.hal_adc_start_continuous)
    i.hal_adc_stop_continuous                0x080074dc   Section        0  hal_adc.o(i.hal_adc_stop_continuous)
    i.hal_dac_init                           0x08007500   Section        0  hal_dac.o(i.hal_dac_init)
    i.hal_dac_write_single                   0x08007528   Section        0  hal_dac.o(i.hal_dac_write_single)
    i.hal_gpio_init                          0x08007550   Section        0  hal_gpio.o(i.hal_gpio_init)
    i.hal_gpio_read_button                   0x0800756c   Section        0  hal_gpio.o(i.hal_gpio_read_button)
    i.hal_gpio_set_button_callback           0x08007590   Section        0  hal_gpio.o(i.hal_gpio_set_button_callback)
    i.hal_gpio_set_led                       0x080075a4   Section        0  hal_gpio.o(i.hal_gpio_set_led)
    i.hal_uart_init                          0x080075d0   Section        0  hal_uart.o(i.hal_uart_init)
    i.hal_uart_send_async                    0x080075e4   Section        0  hal_uart.o(i.hal_uart_send_async)
    i.main                                   0x08007610   Section        0  main.o(i.main)
    i.melp_decoder_init                      0x08007d44   Section        0  melp_decoder.o(i.melp_decoder_init)
    i.melp_encoder_deinit                    0x08007dbc   Section        0  melp_encoder.o(i.melp_encoder_deinit)
    i.melp_encoder_encode_frame              0x08007dd0   Section        0  melp_encoder.o(i.melp_encoder_encode_frame)
    i.melp_encoder_init                      0x08007ed8   Section        0  melp_encoder.o(i.melp_encoder_init)
    i.melp_encoder_pack_frame                0x08007f64   Section        0  melp_encoder.o(i.melp_encoder_pack_frame)
    i.melp_levinson_durbin                   0x08007f9c   Section        0  melp_encoder.o(i.melp_levinson_durbin)
    melp_levinson_durbin                     0x08007f9d   Thumb Code   230  melp_encoder.o(i.melp_levinson_durbin)
    i.melp_lpc_analysis                      0x0800808c   Section        0  melp_encoder.o(i.melp_lpc_analysis)
    i.melp_lpc_to_lsf                        0x08008154   Section        0  melp_encoder.o(i.melp_lpc_to_lsf)
    i.melp_pitch_estimation                  0x08008190   Section        0  melp_encoder.o(i.melp_pitch_estimation)
    i.melp_quantize_parameters               0x080081fc   Section        0  melp_encoder.o(i.melp_quantize_parameters)
    i.melp_voicing_analysis                  0x0800826c   Section        0  melp_encoder.o(i.melp_voicing_analysis)
    locale$$code                             0x080082d4   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08008300   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$fpinit                             0x0800832c   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800832c   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x08008336   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08008336   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x0800833a   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x0800833a   Number         0  printf2.o(x$fpl$printf2)
    .constdata                               0x0800833e   Section       24  stm32h7xx_hal_uart.o(.constdata)
    x$fpl$usenofp                            0x0800833e   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08008356   Section       16  stm32h7xx_hal_uart_ex.o(.constdata)
    numerator                                0x08008356   Data           8  stm32h7xx_hal_uart_ex.o(.constdata)
    denominator                              0x0800835e   Data           8  stm32h7xx_hal_uart_ex.o(.constdata)
    .constdata                               0x08008366   Section       16  system_stm32h7xx.o(.constdata)
    .constdata                               0x08008378   Section       16  app_main.o(.constdata)
    .constdata                               0x08008388   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08008388   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08008390   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x08008390   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x080083a4   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x080083b8   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x080083b8   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x080083cc   Section       32  rredf.o(.constdata)
    twooverpi                                0x080083cc   Data          32  rredf.o(.constdata)
    .constdata                               0x080083ec   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x080083ec   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x080083ff   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x08008414   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08008414   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08008450   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x080084c8   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080084cc   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080084d4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080084e0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080084e2   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080084e3   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x080084e4   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x080084e4   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x080084e8   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x080084f0   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x080085f4   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x24000000   Section       12  stm32h7xx_hal.o(.data)
    .data                                    0x2400000c   Section        8  system_stm32h7xx.o(.data)
    .data                                    0x24000014   Section       12  app_main.o(.data)
    current_state                            0x24000014   Data           1  app_main.o(.data)
    app_initialized                          0x24000015   Data           1  app_main.o(.data)
    systick_counter                          0x24000018   Data           4  app_main.o(.data)
    led_blink_counter                        0x2400001c   Data           4  app_main.o(.data)
    .data                                    0x24000020   Section        1  app_melp.o(.data)
    callbacks_initialized                    0x24000020   Data           1  app_melp.o(.data)
    .data                                    0x24000024   Section        8  hal_adc.o(.data)
    adc_initialized                          0x24000024   Data           1  hal_adc.o(.data)
    adc_callback                             0x24000028   Data           4  hal_adc.o(.data)
    .data                                    0x2400002c   Section        8  hal_dac.o(.data)
    dac_initialized                          0x2400002c   Data           1  hal_dac.o(.data)
    dac_callback                             0x24000030   Data           4  hal_dac.o(.data)
    .data                                    0x24000034   Section        8  hal_gpio.o(.data)
    gpio_initialized                         0x24000034   Data           1  hal_gpio.o(.data)
    last_button_state                        0x24000035   Data           1  hal_gpio.o(.data)
    button_callback                          0x24000038   Data           4  hal_gpio.o(.data)
    .data                                    0x2400003c   Section       16  hal_uart.o(.data)
    uart_initialized                         0x2400003c   Data           1  hal_uart.o(.data)
    uart_tx_callback                         0x24000040   Data           4  hal_uart.o(.data)
    uart_rx_callback                         0x24000044   Data           4  hal_uart.o(.data)
    uart_error_callback                      0x24000048   Data           4  hal_uart.o(.data)
    .data                                    0x2400004c   Section        8  melp_decoder.o(.data)
    filters_initialized                      0x2400004c   Data           1  melp_decoder.o(.data)
    seed                                     0x24000050   Data           4  melp_decoder.o(.data)
    .data                                    0x24000054   Section        1  melp_encoder.o(.data)
    window_initialized                       0x24000054   Data           1  melp_encoder.o(.data)
    .bss                                     0x24000058   Section      524  main.o(.bss)
    buffer                                   0x24000058   Data         256  main.o(.bss)
    .bss                                     0x24000264   Section     4512  app_main.o(.bss)
    melp_context                             0x24000264   Data        4512  app_main.o(.bss)
    .bss                                     0x24001404   Section       16  app_melp.o(.bss)
    app_callbacks                            0x24001404   Data          16  app_melp.o(.bss)
    .bss                                     0x24001414   Section      720  hal_adc.o(.bss)
    adc_buffer                               0x24001414   Data         720  hal_adc.o(.bss)
    .bss                                     0x240016e4   Section      720  melp_encoder.o(.bss)
    hamming_window                           0x240016e4   Data         720  melp_encoder.o(.bss)
    .bss                                     0x240019b4   Section       96  libspace.o(.bss)
    HEAP                                     0x24001a18   Section      512  startup_stm32h750xx.o(HEAP)
    Heap_Mem                                 0x24001a18   Data         512  startup_stm32h750xx.o(HEAP)
    STACK                                    0x24001c18   Section     1024  startup_stm32h750xx.o(STACK)
    Stack_Mem                                0x24001c18   Data        1024  startup_stm32h750xx.o(STACK)
    __initial_sp                             0x24002018   Data           0  startup_stm32h750xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPv5_D16$PE$PLD8$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000298   Number         0  startup_stm32h750xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32h750xx.o(RESET)
    __Vectors_End                            0x08000298   Data           0  startup_stm32h750xx.o(RESET)
    __main                                   0x08000299   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080002a1   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080002a1   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080002a1   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080002af   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080002d5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080002f1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x0800030d   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x0800030d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000313   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000319   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800031f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000325   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800032b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000331   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800033b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000341   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000347   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800034d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000353   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000359   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800035f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000365   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800036b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x08000371   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x08000377   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000381   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000387   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x0800038d   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x08000393   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x08000399   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800039d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800039f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080003a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080003a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080003a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080003a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080003a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080003a9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080003a9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080003b5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080003b5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080003b5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080003bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080003c1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080003c3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080003c3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080003c3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080003c3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080003c3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080003c3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080003c3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080003c3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080003c5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080003c5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080003c5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080003cb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080003cb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080003cf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080003cf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080003d7   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080003d9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080003d9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080003dd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080003e5   Thumb Code    12  startup_stm32h750xx.o(.text)
    ADC3_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    ADC_IRQHandler                           0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    BDMA_Channel0_IRQHandler                 0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    BDMA_Channel1_IRQHandler                 0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    BDMA_Channel2_IRQHandler                 0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    BDMA_Channel3_IRQHandler                 0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    BDMA_Channel4_IRQHandler                 0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    BDMA_Channel5_IRQHandler                 0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    BDMA_Channel6_IRQHandler                 0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    BDMA_Channel7_IRQHandler                 0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    CEC_IRQHandler                           0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    COMP1_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    CRS_IRQHandler                           0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    CRYP_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DCMI_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DFSDM1_FLT0_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DFSDM1_FLT1_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DFSDM1_FLT2_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DFSDM1_FLT3_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA2D_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMAMUX1_OVR_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    DMAMUX2_OVR_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    ECC_IRQHandler                           0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    ETH_IRQHandler                           0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    EXTI0_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    EXTI1_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    EXTI2_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    EXTI3_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    EXTI4_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    FDCAN1_IT0_IRQHandler                    0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    FDCAN1_IT1_IRQHandler                    0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    FDCAN2_IT0_IRQHandler                    0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    FDCAN2_IT1_IRQHandler                    0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    FDCAN_CAL_IRQHandler                     0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    FLASH_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    FMC_IRQHandler                           0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    FPU_IRQHandler                           0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    HASH_RNG_IRQHandler                      0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    HRTIM1_FLT_IRQHandler                    0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    HRTIM1_Master_IRQHandler                 0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    HRTIM1_TIMA_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    HRTIM1_TIMB_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    HRTIM1_TIMC_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    HRTIM1_TIMD_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    HRTIM1_TIME_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    HSEM1_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    I2C4_ER_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    I2C4_EV_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    JPEG_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    LPTIM1_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    LPTIM2_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    LPTIM3_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    LPTIM4_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    LPTIM5_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    LPUART1_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    LTDC_ER_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    LTDC_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    MDIOS_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    MDIOS_WKUP_IRQHandler                    0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    MDMA_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    OTG_FS_EP1_IN_IRQHandler                 0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    OTG_FS_EP1_OUT_IRQHandler                0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    OTG_FS_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    OTG_HS_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    PVD_AVD_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    QUADSPI_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    RCC_IRQHandler                           0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SAI1_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SAI2_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SAI3_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SAI4_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SDMMC1_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SDMMC2_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SPDIF_RX_IRQHandler                      0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SPI1_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SPI2_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SPI3_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SPI4_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SPI5_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SPI6_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    SWPMI1_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM15_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM16_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM17_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM1_BRK_IRQHandler                      0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM1_UP_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM2_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM3_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM4_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM5_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM7_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    UART4_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    UART5_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    UART7_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    UART8_IRQHandler                         0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    USART1_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    USART2_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    USART3_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    USART6_IRQHandler                        0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    WAKEUP_PIN_IRQHandler                    0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    WWDG_IRQHandler                          0x08000403   Thumb Code     0  startup_stm32h750xx.o(.text)
    __user_initial_stackheap                 0x08000405   Thumb Code     0  startup_stm32h750xx.o(.text)
    __aeabi_uldivmod                         0x0800042d   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x0800042d   Thumb Code   238  lludivv7m.o(.text)
    vsnprintf                                0x0800051d   Thumb Code    48  vsnprintf.o(.text)
    strlen                                   0x08000551   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy                           0x0800058f   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x0800058f   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080005f5   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memmove                          0x08000619   Thumb Code     0  rt_memmove_v6.o(.text)
    __rt_memmove                             0x08000619   Thumb Code   132  rt_memmove_v6.o(.text)
    __memmove_lastfew                        0x08000679   Thumb Code     0  rt_memmove_v6.o(.text)
    __aeabi_memcpy4                          0x0800069d   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x0800069d   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x0800069d   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080006e5   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x08000701   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000701   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000701   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000705   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x0800074f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000751   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000753   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x08000755   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x0800075f   Thumb Code    12  _rserrno.o(.text)
    _printf_pre_padding                      0x0800076b   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000797   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x080007b9   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x080007cb   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x080007dd   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000831   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x080008a9   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x080008db   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000901   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x0800090b   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x0800091d   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x080009d9   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000a55   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000a97   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000aaf   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000ac5   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000b1b   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000b37   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000b43   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x08000b59   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __aeabi_memmove4                         0x08000ce1   Thumb Code     0  rt_memmove_w.o(.text)
    __aeabi_memmove8                         0x08000ce1   Thumb Code     0  rt_memmove_w.o(.text)
    __rt_memmove_w                           0x08000ce1   Thumb Code   122  rt_memmove_w.o(.text)
    __memmove_aligned                        0x08000d07   Thumb Code     0  rt_memmove_w.o(.text)
    __memmove_lastfew_aligned                0x08000d3f   Thumb Code     0  rt_memmove_w.o(.text)
    __aeabi_errno_addr                       0x08000d5d   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000d5d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000d5d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08000d65   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x08000def   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000ea1   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08001053   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x080012c1   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x080015bd   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080015d1   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x080015e1   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x080015e9   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x080015fd   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x0800160d   Thumb Code     8  _printf_wchar.o(.text)
    _wcrtomb                                 0x08001615   Thumb Code    64  _wcrtomb.o(.text)
    __user_libspace                          0x08001655   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001655   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001655   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0800165d   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x080016a9   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x080016b9   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x080016c1   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08001741   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x08001825   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08001839   Thumb Code   104  strcmpv7m_pel.o(.text)
    _sys_exit                                0x080018a1   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x080018ad   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080018ad   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080018af   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x080018af   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080018ed   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001933   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08001993   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08001ccb   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001da7   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001dd1   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001dfb   Thumb Code   580  btod.o(CL$$btod_mult_common)
    ADC_ConfigureBoostMode                   0x08002041   Thumb Code   236  stm32h7xx_hal_adc.o(i.ADC_ConfigureBoostMode)
    ADC_ConversionStop                       0x08002149   Thumb Code   220  stm32h7xx_hal_adc.o(i.ADC_ConversionStop)
    ADC_DMAConvCplt                          0x0800222d   Thumb Code   104  stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAError                             0x08002295   Thumb Code    26  stm32h7xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAHalfConvCplt                      0x080022af   Thumb Code    10  stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_Disable                              0x080022b9   Thumb Code   106  stm32h7xx_hal_adc.o(i.ADC_Disable)
    ADC_Enable                               0x08002329   Thumb Code   148  stm32h7xx_hal_adc.o(i.ADC_Enable)
    BusFault_Handler                         0x080023d1   Thumb Code     2  stm32h7xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x080025f5   Thumb Code     2  stm32h7xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x080025f9   Thumb Code    82  main.o(i.Error_Handler)
    ExitRun0Mode                             0x080026c5   Thumb Code    22  system_stm32h7xx.o(i.ExitRun0Mode)
    HAL_ADCEx_Calibration_Start              0x080026e1   Thumb Code   146  stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start)
    HAL_ADCEx_MultiModeConfigChannel         0x0800277d   Thumb Code   274  stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel)
    HAL_ADC_ConfigChannel                    0x080028a5   Thumb Code   946  stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08002cc5   Thumb Code    46  hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x08002d01   Thumb Code    36  hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x08002d31   Thumb Code     2  stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_Init                             0x08002d35   Thumb Code   498  stm32h7xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08002f5d   Thumb Code   140  stm32h7xx_hal_msp.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x08002ff5   Thumb Code   230  stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_ADC_Stop_DMA                         0x080030fd   Thumb Code   122  stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    HAL_DAC_ConfigChannel                    0x08003179   Thumb Code   328  stm32h7xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    HAL_DAC_Init                             0x080032c5   Thumb Code    40  stm32h7xx_hal_dac.o(i.HAL_DAC_Init)
    HAL_DAC_MspInit                          0x080032ed   Thumb Code    82  stm32h7xx_hal_msp.o(i.HAL_DAC_MspInit)
    HAL_DAC_SetValue                         0x0800334d   Thumb Code    36  stm32h7xx_hal_dac.o(i.HAL_DAC_SetValue)
    HAL_DAC_Start                            0x08003371   Thumb Code    96  stm32h7xx_hal_dac.o(i.HAL_DAC_Start)
    HAL_DMA_Abort                            0x080033d1   Thumb Code   836  stm32h7xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Start_IT                         0x08003731   Thumb Code   628  stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x080039cd   Thumb Code    32  stm32h7xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x080039f1   Thumb Code   506  stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08003c1d   Thumb Code    10  stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_TogglePin                       0x08003c27   Thumb Code    16  stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08003c37   Thumb Code    10  stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetDEVID                             0x08003c41   Thumb Code    10  stm32h7xx_hal.o(i.HAL_GetDEVID)
    HAL_GetHalVersion                        0x08003c51   Thumb Code     4  stm32h7xx_hal.o(i.HAL_GetHalVersion)
    HAL_GetREVID                             0x08003c59   Thumb Code     8  stm32h7xx_hal.o(i.HAL_GetREVID)
    HAL_GetTick                              0x08003c65   Thumb Code     6  stm32h7xx_hal.o(i.HAL_GetTick)
    HAL_GetUIDw0                             0x08003c71   Thumb Code     6  stm32h7xx_hal.o(i.HAL_GetUIDw0)
    HAL_GetUIDw1                             0x08003c7d   Thumb Code     6  stm32h7xx_hal.o(i.HAL_GetUIDw1)
    HAL_GetUIDw2                             0x08003c89   Thumb Code     6  stm32h7xx_hal.o(i.HAL_GetUIDw2)
    HAL_IncTick                              0x08003c95   Thumb Code    12  stm32h7xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08003ca5   Thumb Code    74  stm32h7xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08003d01   Thumb Code    56  stm32h7xx_hal.o(i.HAL_InitTick)
    HAL_MPU_ConfigRegion                     0x08003d41   Thumb Code    86  stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion)
    HAL_MPU_Disable                          0x08003d9d   Thumb Code    24  stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable)
    HAL_MPU_Enable                           0x08003db9   Thumb Code    30  stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable)
    HAL_MspInit                              0x08003ddd   Thumb Code    22  stm32h7xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_SetPriority                     0x08003df9   Thumb Code    60  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08003e39   Thumb Code    28  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ConfigSupply                   0x08003e5d   Thumb Code    78  stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply)
    HAL_RCCEx_GetD3PCLK1Freq                 0x08003eb1   Thumb Code    26  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq)
    HAL_RCCEx_GetPLL1ClockFreq               0x08003ed5   Thumb Code   298  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL1ClockFreq)
    HAL_RCCEx_GetPLL2ClockFreq               0x0800401d   Thumb Code   296  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq)
    HAL_RCCEx_GetPLL3ClockFreq               0x08004161   Thumb Code   296  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq)
    HAL_RCCEx_GetPeriphCLKFreq               0x080042a5   Thumb Code   692  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq)
    HAL_RCCEx_PeriphCLKConfig                0x0800456d   Thumb Code  2472  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08004f19   Thumb Code   580  stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08005175   Thumb Code    52  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x080051b9   Thumb Code    26  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080051dd   Thumb Code    26  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08005201   Thumb Code   278  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08005339   Thumb Code  1410  stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x080058bb   Thumb Code    38  stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UARTEx_DisableFifoMode               0x080058e1   Thumb Code    64  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    HAL_UARTEx_SetRxFifoThreshold            0x08005921   Thumb Code    76  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    HAL_UARTEx_SetTxFifoThreshold            0x0800596d   Thumb Code    76  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    HAL_UART_ErrorCallback                   0x080059b9   Thumb Code    22  hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_Init                            0x080059d9   Thumb Code   106  stm32h7xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08005a45   Thumb Code   122  stm32h7xx_hal_msp.o(i.HAL_UART_MspInit)
    HAL_UART_Transmit                        0x08005acd   Thumb Code   176  stm32h7xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_Transmit_DMA                    0x08005b7d   Thumb Code   132  stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA)
    HAL_UART_TxCpltCallback                  0x08005c0d   Thumb Code    18  hal_uart.o(i.HAL_UART_TxCpltCallback)
    HAL_UART_TxHalfCpltCallback              0x08005c29   Thumb Code     2  stm32h7xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback)
    HardFault_Handler                        0x08005c2b   Thumb Code     2  stm32h7xx_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x08005de5   Thumb Code     2  stm32h7xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08005de7   Thumb Code     2  stm32h7xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08005de9   Thumb Code     2  stm32h7xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x0800602d   Thumb Code     2  stm32h7xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x0800602f   Thumb Code     4  stm32h7xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08006035   Thumb Code   154  main.o(i.SystemClock_Config)
    SystemInit                               0x080060d9   Thumb Code   206  system_stm32h7xx.o(i.SystemInit)
    UART_AdvFeatureConfig                    0x08006215   Thumb Code   200  stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x080062dd   Thumb Code   170  stm32h7xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x080064a5   Thumb Code   876  stm32h7xx_hal_uart.o(i.UART_SetConfig)
    UART_WaitOnFlagUntilTimeout              0x08006851   Thumb Code   148  stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UsageFault_Handler                       0x080068e5   Thumb Code     2  stm32h7xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x080068e7   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_cosf                            0x08006939   Thumb Code   276  cosf.o(i.__hardfp_cosf)
    __hardfp_sqrtf                           0x08006a89   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_infnan                     0x08006ac3   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x08006ac9   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_rredf2                         0x08006ad9   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    _is_digit                                0x08006c2d   Thumb Code    14  __printf_wp.o(i._is_digit)
    app_init                                 0x08006ca5   Thumb Code    88  app_main.o(i.app_init)
    app_melp_init                            0x08006dfd   Thumb Code    82  app_melp.o(i.app_melp_init)
    app_melp_process_input                   0x08006e59   Thumb Code   168  app_melp.o(i.app_melp_process_input)
    app_melp_start_recording                 0x08006f01   Thumb Code    40  app_melp.o(i.app_melp_start_recording)
    app_melp_stop_recording                  0x08006f2b   Thumb Code    34  app_melp.o(i.app_melp_stop_recording)
    debug_init                               0x08006f51   Thumb Code    30  main.o(i.debug_init)
    debug_print_system_info                  0x08006fd5   Thumb Code   112  main.o(i.debug_print_system_info)
    debug_printf                             0x08007149   Thumb Code    44  main.o(i.debug_printf)
    debug_test_button                        0x08007179   Thumb Code   132  main.o(i.debug_test_button)
    debug_test_led                           0x080072d1   Thumb Code   108  main.o(i.debug_test_led)
    debug_test_uart                          0x080073b1   Thumb Code    62  main.o(i.debug_test_uart)
    hal_adc_init                             0x08007481   Thumb Code    36  hal_adc.o(i.hal_adc_init)
    hal_adc_start_continuous                 0x080074ad   Thumb Code    36  hal_adc.o(i.hal_adc_start_continuous)
    hal_adc_stop_continuous                  0x080074dd   Thumb Code    28  hal_adc.o(i.hal_adc_stop_continuous)
    hal_dac_init                             0x08007501   Thumb Code    32  hal_dac.o(i.hal_dac_init)
    hal_dac_write_single                     0x08007529   Thumb Code    32  hal_dac.o(i.hal_dac_write_single)
    hal_gpio_init                            0x08007551   Thumb Code    24  hal_gpio.o(i.hal_gpio_init)
    hal_gpio_read_button                     0x0800756d   Thumb Code    28  hal_gpio.o(i.hal_gpio_read_button)
    hal_gpio_set_button_callback             0x08007591   Thumb Code    16  hal_gpio.o(i.hal_gpio_set_button_callback)
    hal_gpio_set_led                         0x080075a5   Thumb Code    36  hal_gpio.o(i.hal_gpio_set_led)
    hal_uart_init                            0x080075d1   Thumb Code    16  hal_uart.o(i.hal_uart_init)
    hal_uart_send_async                      0x080075e5   Thumb Code    36  hal_uart.o(i.hal_uart_send_async)
    main                                     0x08007611   Thumb Code  1648  main.o(i.main)
    melp_decoder_init                        0x08007d45   Thumb Code   104  melp_decoder.o(i.melp_decoder_init)
    melp_encoder_deinit                      0x08007dbd   Thumb Code    18  melp_encoder.o(i.melp_encoder_deinit)
    melp_encoder_encode_frame                0x08007dd1   Thumb Code   252  melp_encoder.o(i.melp_encoder_encode_frame)
    melp_encoder_init                        0x08007ed9   Thumb Code   116  melp_encoder.o(i.melp_encoder_init)
    melp_encoder_pack_frame                  0x08007f65   Thumb Code    56  melp_encoder.o(i.melp_encoder_pack_frame)
    melp_lpc_analysis                        0x0800808d   Thumb Code   190  melp_encoder.o(i.melp_lpc_analysis)
    melp_lpc_to_lsf                          0x08008155   Thumb Code    54  melp_encoder.o(i.melp_lpc_to_lsf)
    melp_pitch_estimation                    0x08008191   Thumb Code   102  melp_encoder.o(i.melp_pitch_estimation)
    melp_quantize_parameters                 0x080081fd   Thumb Code   104  melp_encoder.o(i.melp_quantize_parameters)
    melp_voicing_analysis                    0x0800826d   Thumb Code    86  melp_encoder.o(i.melp_voicing_analysis)
    _get_lc_numeric                          0x080082d5   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08008301   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _fp_init                                 0x0800832d   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08008335   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08008335   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x08008337   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x0800833b   Thumb Code     4  printf2.o(x$fpl$printf2)
    UARTPrescTable                           0x0800833e   Data          24  stm32h7xx_hal_uart.o(.constdata)
    __I$use$fp                               0x0800833e   Number         0  usenofp.o(x$fpl$usenofp)
    D1CorePrescTable                         0x08008366   Data          16  system_stm32h7xx.o(.constdata)
    Region$$Table$$Base                      0x080084a8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080084c8   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x080084f1   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x24000000   Data           1  stm32h7xx_hal.o(.data)
    uwTickPrio                               0x24000004   Data           4  stm32h7xx_hal.o(.data)
    uwTick                                   0x24000008   Data           4  stm32h7xx_hal.o(.data)
    SystemCoreClock                          0x2400000c   Data           4  system_stm32h7xx.o(.data)
    SystemD2Clock                            0x24000010   Data           4  system_stm32h7xx.o(.data)
    hadc1                                    0x24000158   Data         100  main.o(.bss)
    hdac1                                    0x240001bc   Data          20  main.o(.bss)
    huart1                                   0x240001d0   Data         148  main.o(.bss)
    __libspace_start                         0x240019b4   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x24001a14   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000299

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000864c, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000085f4, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000298   Data   RO            3    RESET               startup_stm32h750xx.o
    0x08000298   0x08000298   0x00000008   Code   RO         5546  * !!!main             c_w.l(__main.o)
    0x080002a0   0x080002a0   0x00000034   Code   RO         5987    !!!scatter          c_w.l(__scatter.o)
    0x080002d4   0x080002d4   0x0000001a   Code   RO         5989    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080002ee   0x080002ee   0x00000002   PAD
    0x080002f0   0x080002f0   0x0000001c   Code   RO         5991    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800030c   0x0800030c   0x00000000   Code   RO         5712    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800030c   0x0800030c   0x00000006   Code   RO         5701    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000312   0x08000312   0x00000006   Code   RO         5703    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000318   0x08000318   0x00000006   Code   RO         5708    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800031e   0x0800031e   0x00000006   Code   RO         5709    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000324   0x08000324   0x00000006   Code   RO         5710    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800032a   0x0800032a   0x00000006   Code   RO         5711    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000330   0x08000330   0x0000000a   Code   RO         5716    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800033a   0x0800033a   0x00000006   Code   RO         5705    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000340   0x08000340   0x00000006   Code   RO         5706    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000346   0x08000346   0x00000006   Code   RO         5707    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800034c   0x0800034c   0x00000006   Code   RO         5704    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000352   0x08000352   0x00000006   Code   RO         5702    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000358   0x08000358   0x00000006   Code   RO         5713    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800035e   0x0800035e   0x00000006   Code   RO         5714    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000364   0x08000364   0x00000006   Code   RO         5715    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800036a   0x0800036a   0x00000006   Code   RO         5720    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x08000370   0x08000370   0x00000006   Code   RO         5721    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x08000376   0x08000376   0x0000000a   Code   RO         5717    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000380   0x08000380   0x00000006   Code   RO         5699    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000386   0x08000386   0x00000006   Code   RO         5700    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800038c   0x0800038c   0x00000006   Code   RO         5718    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x08000392   0x08000392   0x00000006   Code   RO         5719    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x08000398   0x08000398   0x00000004   Code   RO         5793    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800039c   0x0800039c   0x00000002   Code   RO         5851    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800039e   0x0800039e   0x00000004   Code   RO         5867    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080003a2   0x080003a2   0x00000000   Code   RO         5870    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080003a2   0x080003a2   0x00000000   Code   RO         5873    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080003a2   0x080003a2   0x00000000   Code   RO         5875    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080003a2   0x080003a2   0x00000000   Code   RO         5877    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080003a2   0x080003a2   0x00000006   Code   RO         5878    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080003a8   0x080003a8   0x00000000   Code   RO         5880    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080003a8   0x080003a8   0x0000000c   Code   RO         5881    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080003b4   0x080003b4   0x00000000   Code   RO         5882    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080003b4   0x080003b4   0x00000000   Code   RO         5884    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080003b4   0x080003b4   0x0000000a   Code   RO         5885    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000000   Code   RO         5886    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000000   Code   RO         5888    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000000   Code   RO         5890    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000000   Code   RO         5892    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000000   Code   RO         5894    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000000   Code   RO         5896    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000000   Code   RO         5898    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000000   Code   RO         5900    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000000   Code   RO         5904    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000000   Code   RO         5906    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000000   Code   RO         5908    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000000   Code   RO         5910    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080003be   0x080003be   0x00000002   Code   RO         5911    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080003c0   0x080003c0   0x00000002   Code   RO         5942    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080003c2   0x080003c2   0x00000000   Code   RO         5968    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080003c2   0x080003c2   0x00000000   Code   RO         5970    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080003c2   0x080003c2   0x00000000   Code   RO         5972    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080003c2   0x080003c2   0x00000000   Code   RO         5975    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080003c2   0x080003c2   0x00000000   Code   RO         5978    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080003c2   0x080003c2   0x00000000   Code   RO         5980    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080003c2   0x080003c2   0x00000000   Code   RO         5983    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080003c2   0x080003c2   0x00000002   Code   RO         5984    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080003c4   0x080003c4   0x00000000   Code   RO         5622    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080003c4   0x080003c4   0x00000000   Code   RO         5757    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080003c4   0x080003c4   0x00000006   Code   RO         5769    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080003ca   0x080003ca   0x00000000   Code   RO         5759    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080003ca   0x080003ca   0x00000004   Code   RO         5760    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080003ce   0x080003ce   0x00000000   Code   RO         5762    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080003ce   0x080003ce   0x00000008   Code   RO         5763    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080003d6   0x080003d6   0x00000002   Code   RO         5854    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080003d8   0x080003d8   0x00000000   Code   RO         5915    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080003d8   0x080003d8   0x00000004   Code   RO         5916    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080003dc   0x080003dc   0x00000006   Code   RO         5917    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080003e2   0x080003e2   0x00000002   PAD
    0x080003e4   0x080003e4   0x00000048   Code   RO            4    .text               startup_stm32h750xx.o
    0x0800042c   0x0800042c   0x000000ee   Code   RO         5530    .text               c_w.l(lludivv7m.o)
    0x0800051a   0x0800051a   0x00000002   PAD
    0x0800051c   0x0800051c   0x00000034   Code   RO         5532    .text               c_w.l(vsnprintf.o)
    0x08000550   0x08000550   0x0000003e   Code   RO         5534    .text               c_w.l(strlen.o)
    0x0800058e   0x0800058e   0x0000008a   Code   RO         5536    .text               c_w.l(rt_memcpy_v6.o)
    0x08000618   0x08000618   0x00000084   Code   RO         5538    .text               c_w.l(rt_memmove_v6.o)
    0x0800069c   0x0800069c   0x00000064   Code   RO         5540    .text               c_w.l(rt_memcpy_w.o)
    0x08000700   0x08000700   0x0000004e   Code   RO         5542    .text               c_w.l(rt_memclr_w.o)
    0x0800074e   0x0800074e   0x00000006   Code   RO         5544    .text               c_w.l(heapauxi.o)
    0x08000754   0x08000754   0x00000016   Code   RO         5627    .text               c_w.l(_rserrno.o)
    0x0800076a   0x0800076a   0x0000004e   Code   RO         5631    .text               c_w.l(_printf_pad.o)
    0x080007b8   0x080007b8   0x00000024   Code   RO         5633    .text               c_w.l(_printf_truncate.o)
    0x080007dc   0x080007dc   0x00000052   Code   RO         5635    .text               c_w.l(_printf_str.o)
    0x0800082e   0x0800082e   0x00000002   PAD
    0x08000830   0x08000830   0x00000078   Code   RO         5637    .text               c_w.l(_printf_dec.o)
    0x080008a8   0x080008a8   0x00000028   Code   RO         5639    .text               c_w.l(_printf_charcount.o)
    0x080008d0   0x080008d0   0x00000030   Code   RO         5641    .text               c_w.l(_printf_char_common.o)
    0x08000900   0x08000900   0x0000000a   Code   RO         5643    .text               c_w.l(_sputc.o)
    0x0800090a   0x0800090a   0x00000010   Code   RO         5645    .text               c_w.l(_snputc.o)
    0x0800091a   0x0800091a   0x00000002   PAD
    0x0800091c   0x0800091c   0x000000bc   Code   RO         5647    .text               c_w.l(_printf_wctomb.o)
    0x080009d8   0x080009d8   0x0000007c   Code   RO         5650    .text               c_w.l(_printf_longlong_dec.o)
    0x08000a54   0x08000a54   0x00000070   Code   RO         5656    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000ac4   0x08000ac4   0x00000094   Code   RO         5676    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000b58   0x08000b58   0x00000188   Code   RO         5696    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000ce0   0x08000ce0   0x0000007a   Code   RO         5722    .text               c_w.l(rt_memmove_w.o)
    0x08000d5a   0x08000d5a   0x00000002   PAD
    0x08000d5c   0x08000d5c   0x00000008   Code   RO         5776    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000d64   0x08000d64   0x0000008a   Code   RO         5778    .text               c_w.l(lludiv10.o)
    0x08000dee   0x08000dee   0x000000b2   Code   RO         5780    .text               c_w.l(_printf_intcommon.o)
    0x08000ea0   0x08000ea0   0x0000041e   Code   RO         5782    .text               c_w.l(_printf_fp_dec.o)
    0x080012be   0x080012be   0x00000002   PAD
    0x080012c0   0x080012c0   0x000002fc   Code   RO         5784    .text               c_w.l(_printf_fp_hex.o)
    0x080015bc   0x080015bc   0x0000002c   Code   RO         5789    .text               c_w.l(_printf_char.o)
    0x080015e8   0x080015e8   0x0000002c   Code   RO         5791    .text               c_w.l(_printf_wchar.o)
    0x08001614   0x08001614   0x00000040   Code   RO         5794    .text               c_w.l(_wcrtomb.o)
    0x08001654   0x08001654   0x00000008   Code   RO         5798    .text               c_w.l(libspace.o)
    0x0800165c   0x0800165c   0x0000004a   Code   RO         5801    .text               c_w.l(sys_stackheap_outer.o)
    0x080016a6   0x080016a6   0x00000002   PAD
    0x080016a8   0x080016a8   0x00000010   Code   RO         5803    .text               c_w.l(rt_ctype_table.o)
    0x080016b8   0x080016b8   0x00000008   Code   RO         5808    .text               c_w.l(rt_locale_intlibspace.o)
    0x080016c0   0x080016c0   0x00000080   Code   RO         5810    .text               c_w.l(_printf_fp_infnan.o)
    0x08001740   0x08001740   0x000000e4   Code   RO         5812    .text               c_w.l(bigflt0.o)
    0x08001824   0x08001824   0x00000012   Code   RO         5840    .text               c_w.l(exit.o)
    0x08001836   0x08001836   0x00000002   PAD
    0x08001838   0x08001838   0x00000068   Code   RO         5865    .text               c_w.l(strcmpv7m_pel.o)
    0x080018a0   0x080018a0   0x0000000c   Code   RO         5912    .text               c_w.l(sys_exit.o)
    0x080018ac   0x080018ac   0x00000002   Code   RO         5931    .text               c_w.l(use_no_semi.o)
    0x080018ae   0x080018ae   0x00000000   Code   RO         5933    .text               c_w.l(indicate_semi.o)
    0x080018ae   0x080018ae   0x0000003e   Code   RO         5815    CL$$btod_d2e        c_w.l(btod.o)
    0x080018ec   0x080018ec   0x00000046   Code   RO         5817    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001932   0x08001932   0x00000060   Code   RO         5816    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08001992   0x08001992   0x00000338   Code   RO         5825    CL$$btod_div_common  c_w.l(btod.o)
    0x08001cca   0x08001cca   0x000000dc   Code   RO         5822    CL$$btod_e2e        c_w.l(btod.o)
    0x08001da6   0x08001da6   0x0000002a   Code   RO         5819    CL$$btod_ediv       c_w.l(btod.o)
    0x08001dd0   0x08001dd0   0x0000002a   Code   RO         5818    CL$$btod_emul       c_w.l(btod.o)
    0x08001dfa   0x08001dfa   0x00000244   Code   RO         5824    CL$$btod_mult_common  c_w.l(btod.o)
    0x0800203e   0x0800203e   0x00000002   PAD
    0x08002040   0x08002040   0x00000108   Code   RO          399    i.ADC_ConfigureBoostMode  stm32h7xx_hal_adc.o
    0x08002148   0x08002148   0x000000e4   Code   RO          400    i.ADC_ConversionStop  stm32h7xx_hal_adc.o
    0x0800222c   0x0800222c   0x00000068   Code   RO          401    i.ADC_DMAConvCplt   stm32h7xx_hal_adc.o
    0x08002294   0x08002294   0x0000001a   Code   RO          402    i.ADC_DMAError      stm32h7xx_hal_adc.o
    0x080022ae   0x080022ae   0x0000000a   Code   RO          403    i.ADC_DMAHalfConvCplt  stm32h7xx_hal_adc.o
    0x080022b8   0x080022b8   0x00000070   Code   RO          404    i.ADC_Disable       stm32h7xx_hal_adc.o
    0x08002328   0x08002328   0x000000a8   Code   RO          405    i.ADC_Enable        stm32h7xx_hal_adc.o
    0x080023d0   0x080023d0   0x00000002   Code   RO          252    i.BusFault_Handler  stm32h7xx_it.o
    0x080023d2   0x080023d2   0x00000002   PAD
    0x080023d4   0x080023d4   0x00000220   Code   RO         1464    i.DMA_SetConfig     stm32h7xx_hal_dma.o
    0x080025f4   0x080025f4   0x00000002   Code   RO          253    i.DebugMon_Handler  stm32h7xx_it.o
    0x080025f6   0x080025f6   0x00000002   PAD
    0x080025f8   0x080025f8   0x000000cc   Code   RO           13    i.Error_Handler     main.o
    0x080026c4   0x080026c4   0x0000001c   Code   RO         4350    i.ExitRun0Mode      system_stm32h7xx.o
    0x080026e0   0x080026e0   0x0000009c   Code   RO          616    i.HAL_ADCEx_Calibration_Start  stm32h7xx_hal_adc_ex.o
    0x0800277c   0x0800277c   0x00000128   Code   RO          636    i.HAL_ADCEx_MultiModeConfigChannel  stm32h7xx_hal_adc_ex.o
    0x080028a4   0x080028a4   0x00000420   Code   RO          407    i.HAL_ADC_ConfigChannel  stm32h7xx_hal_adc.o
    0x08002cc4   0x08002cc4   0x0000003c   Code   RO         4707    i.HAL_ADC_ConvCpltCallback  hal_adc.o
    0x08002d00   0x08002d00   0x00000030   Code   RO         4708    i.HAL_ADC_ConvHalfCpltCallback  hal_adc.o
    0x08002d30   0x08002d30   0x00000002   Code   RO          411    i.HAL_ADC_ErrorCallback  stm32h7xx_hal_adc.o
    0x08002d32   0x08002d32   0x00000002   PAD
    0x08002d34   0x08002d34   0x00000228   Code   RO          416    i.HAL_ADC_Init      stm32h7xx_hal_adc.o
    0x08002f5c   0x08002f5c   0x00000098   Code   RO          328    i.HAL_ADC_MspInit   stm32h7xx_hal_msp.o
    0x08002ff4   0x08002ff4   0x00000108   Code   RO          423    i.HAL_ADC_Start_DMA  stm32h7xx_hal_adc.o
    0x080030fc   0x080030fc   0x0000007a   Code   RO          426    i.HAL_ADC_Stop_DMA  stm32h7xx_hal_adc.o
    0x08003176   0x08003176   0x00000002   PAD
    0x08003178   0x08003178   0x0000014c   Code   RO         3233    i.HAL_DAC_ConfigChannel  stm32h7xx_hal_dac.o
    0x080032c4   0x080032c4   0x00000028   Code   RO         3243    i.HAL_DAC_Init      stm32h7xx_hal_dac.o
    0x080032ec   0x080032ec   0x00000060   Code   RO          330    i.HAL_DAC_MspInit   stm32h7xx_hal_msp.o
    0x0800334c   0x0800334c   0x00000024   Code   RO         3246    i.HAL_DAC_SetValue  stm32h7xx_hal_dac.o
    0x08003370   0x08003370   0x00000060   Code   RO         3247    i.HAL_DAC_Start     stm32h7xx_hal_dac.o
    0x080033d0   0x080033d0   0x00000360   Code   RO         1465    i.HAL_DMA_Abort     stm32h7xx_hal_dma.o
    0x08003730   0x08003730   0x0000029c   Code   RO         1475    i.HAL_DMA_Start_IT  stm32h7xx_hal_dma.o
    0x080039cc   0x080039cc   0x00000024   Code   RO         2327    i.HAL_Delay         stm32h7xx_hal.o
    0x080039f0   0x080039f0   0x0000022c   Code   RO         1313    i.HAL_GPIO_Init     stm32h7xx_hal_gpio.o
    0x08003c1c   0x08003c1c   0x0000000a   Code   RO         1315    i.HAL_GPIO_ReadPin  stm32h7xx_hal_gpio.o
    0x08003c26   0x08003c26   0x00000010   Code   RO         1316    i.HAL_GPIO_TogglePin  stm32h7xx_hal_gpio.o
    0x08003c36   0x08003c36   0x0000000a   Code   RO         1317    i.HAL_GPIO_WritePin  stm32h7xx_hal_gpio.o
    0x08003c40   0x08003c40   0x00000010   Code   RO         2335    i.HAL_GetDEVID      stm32h7xx_hal.o
    0x08003c50   0x08003c50   0x00000008   Code   RO         2337    i.HAL_GetHalVersion  stm32h7xx_hal.o
    0x08003c58   0x08003c58   0x0000000c   Code   RO         2338    i.HAL_GetREVID      stm32h7xx_hal.o
    0x08003c64   0x08003c64   0x0000000c   Code   RO         2339    i.HAL_GetTick       stm32h7xx_hal.o
    0x08003c70   0x08003c70   0x0000000c   Code   RO         2342    i.HAL_GetUIDw0      stm32h7xx_hal.o
    0x08003c7c   0x08003c7c   0x0000000c   Code   RO         2343    i.HAL_GetUIDw1      stm32h7xx_hal.o
    0x08003c88   0x08003c88   0x0000000c   Code   RO         2344    i.HAL_GetUIDw2      stm32h7xx_hal.o
    0x08003c94   0x08003c94   0x00000010   Code   RO         2345    i.HAL_IncTick       stm32h7xx_hal.o
    0x08003ca4   0x08003ca4   0x0000005c   Code   RO         2346    i.HAL_Init          stm32h7xx_hal.o
    0x08003d00   0x08003d00   0x00000040   Code   RO         2347    i.HAL_InitTick      stm32h7xx_hal.o
    0x08003d40   0x08003d40   0x0000005c   Code   RO         2168    i.HAL_MPU_ConfigRegion  stm32h7xx_hal_cortex.o
    0x08003d9c   0x08003d9c   0x0000001c   Code   RO         2169    i.HAL_MPU_Disable   stm32h7xx_hal_cortex.o
    0x08003db8   0x08003db8   0x00000024   Code   RO         2171    i.HAL_MPU_Enable    stm32h7xx_hal_cortex.o
    0x08003ddc   0x08003ddc   0x0000001c   Code   RO          331    i.HAL_MspInit       stm32h7xx_hal_msp.o
    0x08003df8   0x08003df8   0x00000040   Code   RO         2181    i.HAL_NVIC_SetPriority  stm32h7xx_hal_cortex.o
    0x08003e38   0x08003e38   0x00000024   Code   RO         2182    i.HAL_NVIC_SetPriorityGrouping  stm32h7xx_hal_cortex.o
    0x08003e5c   0x08003e5c   0x00000054   Code   RO         1914    i.HAL_PWREx_ConfigSupply  stm32h7xx_hal_pwr_ex.o
    0x08003eb0   0x08003eb0   0x00000024   Code   RO          956    i.HAL_RCCEx_GetD3PCLK1Freq  stm32h7xx_hal_rcc_ex.o
    0x08003ed4   0x08003ed4   0x00000148   Code   RO          957    i.HAL_RCCEx_GetPLL1ClockFreq  stm32h7xx_hal_rcc_ex.o
    0x0800401c   0x0800401c   0x00000144   Code   RO          958    i.HAL_RCCEx_GetPLL2ClockFreq  stm32h7xx_hal_rcc_ex.o
    0x08004160   0x08004160   0x00000144   Code   RO          959    i.HAL_RCCEx_GetPLL3ClockFreq  stm32h7xx_hal_rcc_ex.o
    0x080042a4   0x080042a4   0x000002c8   Code   RO          961    i.HAL_RCCEx_GetPeriphCLKFreq  stm32h7xx_hal_rcc_ex.o
    0x0800456c   0x0800456c   0x000009ac   Code   RO          965    i.HAL_RCCEx_PeriphCLKConfig  stm32h7xx_hal_rcc_ex.o
    0x08004f18   0x08004f18   0x0000025c   Code   RO          841    i.HAL_RCC_ClockConfig  stm32h7xx_hal_rcc.o
    0x08005174   0x08005174   0x00000044   Code   RO          846    i.HAL_RCC_GetHCLKFreq  stm32h7xx_hal_rcc.o
    0x080051b8   0x080051b8   0x00000024   Code   RO          848    i.HAL_RCC_GetPCLK1Freq  stm32h7xx_hal_rcc.o
    0x080051dc   0x080051dc   0x00000024   Code   RO          849    i.HAL_RCC_GetPCLK2Freq  stm32h7xx_hal_rcc.o
    0x08005200   0x08005200   0x00000138   Code   RO          850    i.HAL_RCC_GetSysClockFreq  stm32h7xx_hal_rcc.o
    0x08005338   0x08005338   0x00000582   Code   RO          853    i.HAL_RCC_OscConfig  stm32h7xx_hal_rcc.o
    0x080058ba   0x080058ba   0x00000026   Code   RO         2186    i.HAL_SYSTICK_Config  stm32h7xx_hal_cortex.o
    0x080058e0   0x080058e0   0x00000040   Code   RO         4232    i.HAL_UARTEx_DisableFifoMode  stm32h7xx_hal_uart_ex.o
    0x08005920   0x08005920   0x0000004c   Code   RO         4241    i.HAL_UARTEx_SetRxFifoThreshold  stm32h7xx_hal_uart_ex.o
    0x0800596c   0x0800596c   0x0000004c   Code   RO         4242    i.HAL_UARTEx_SetTxFifoThreshold  stm32h7xx_hal_uart_ex.o
    0x080059b8   0x080059b8   0x00000020   Code   RO         4962    i.HAL_UART_ErrorCallback  hal_uart.o
    0x080059d8   0x080059d8   0x0000006a   Code   RO         3841    i.HAL_UART_Init     stm32h7xx_hal_uart.o
    0x08005a42   0x08005a42   0x00000002   PAD
    0x08005a44   0x08005a44   0x00000088   Code   RO          335    i.HAL_UART_MspInit  stm32h7xx_hal_msp.o
    0x08005acc   0x08005acc   0x000000b0   Code   RO         3850    i.HAL_UART_Transmit  stm32h7xx_hal_uart.o
    0x08005b7c   0x08005b7c   0x00000090   Code   RO         3851    i.HAL_UART_Transmit_DMA  stm32h7xx_hal_uart.o
    0x08005c0c   0x08005c0c   0x0000001c   Code   RO         4964    i.HAL_UART_TxCpltCallback  hal_uart.o
    0x08005c28   0x08005c28   0x00000002   Code   RO         3854    i.HAL_UART_TxHalfCpltCallback  stm32h7xx_hal_uart.o
    0x08005c2a   0x08005c2a   0x00000002   Code   RO          254    i.HardFault_Handler  stm32h7xx_it.o
    0x08005c2c   0x08005c2c   0x00000008   Code   RO          428    i.LL_ADC_INJ_IsConversionOngoing  stm32h7xx_hal_adc.o
    0x08005c34   0x08005c34   0x00000008   Code   RO          429    i.LL_ADC_REG_IsConversionOngoing  stm32h7xx_hal_adc.o
    0x08005c3c   0x08005c3c   0x00000008   Code   RO          646    i.LL_ADC_REG_IsConversionOngoing  stm32h7xx_hal_adc_ex.o
    0x08005c44   0x08005c44   0x00000010   Code   RO          430    i.LL_ADC_REG_IsTriggerSourceSWStart  stm32h7xx_hal_adc.o
    0x08005c54   0x08005c54   0x00000088   Code   RO           14    i.MX_ADC1_Init      main.o
    0x08005cdc   0x08005cdc   0x000000b0   Code   RO           15    i.MX_GPIO_Init      main.o
    0x08005d8c   0x08005d8c   0x00000058   Code   RO           16    i.MX_USART1_Init    main.o
    0x08005de4   0x08005de4   0x00000002   Code   RO          255    i.MemManage_Handler  stm32h7xx_it.o
    0x08005de6   0x08005de6   0x00000002   Code   RO          256    i.NMI_Handler       stm32h7xx_it.o
    0x08005de8   0x08005de8   0x00000002   Code   RO          257    i.PendSV_Handler    stm32h7xx_it.o
    0x08005dea   0x08005dea   0x00000002   PAD
    0x08005dec   0x08005dec   0x00000120   Code   RO          968    i.RCCEx_PLL2_Config  stm32h7xx_hal_rcc_ex.o
    0x08005f0c   0x08005f0c   0x00000120   Code   RO          969    i.RCCEx_PLL3_Config  stm32h7xx_hal_rcc_ex.o
    0x0800602c   0x0800602c   0x00000002   Code   RO          258    i.SVC_Handler       stm32h7xx_it.o
    0x0800602e   0x0800602e   0x00000004   Code   RO          259    i.SysTick_Handler   stm32h7xx_it.o
    0x08006032   0x08006032   0x00000002   PAD
    0x08006034   0x08006034   0x000000a4   Code   RO           17    i.SystemClock_Config  main.o
    0x080060d8   0x080060d8   0x000000f8   Code   RO         4352    i.SystemInit        system_stm32h7xx.o
    0x080061d0   0x080061d0   0x00000044   Code   RO         4246    i.UARTEx_SetNbDataToProcess  stm32h7xx_hal_uart_ex.o
    0x08006214   0x08006214   0x000000c8   Code   RO         3855    i.UART_AdvFeatureConfig  stm32h7xx_hal_uart.o
    0x080062dc   0x080062dc   0x000000aa   Code   RO         3856    i.UART_CheckIdleState  stm32h7xx_hal_uart.o
    0x08006386   0x08006386   0x0000004e   Code   RO         3858    i.UART_DMAError     stm32h7xx_hal_uart.o
    0x080063d4   0x080063d4   0x00000040   Code   RO         3863    i.UART_DMATransmitCplt  stm32h7xx_hal_uart.o
    0x08006414   0x08006414   0x0000000a   Code   RO         3865    i.UART_DMATxHalfCplt  stm32h7xx_hal_uart.o
    0x0800641e   0x0800641e   0x00000002   PAD
    0x08006420   0x08006420   0x00000054   Code   RO         3867    i.UART_EndRxTransfer  stm32h7xx_hal_uart.o
    0x08006474   0x08006474   0x0000002e   Code   RO         3868    i.UART_EndTxTransfer  stm32h7xx_hal_uart.o
    0x080064a2   0x080064a2   0x00000002   PAD
    0x080064a4   0x080064a4   0x000003ac   Code   RO         3873    i.UART_SetConfig    stm32h7xx_hal_uart.o
    0x08006850   0x08006850   0x00000094   Code   RO         3880    i.UART_WaitOnFlagUntilTimeout  stm32h7xx_hal_uart.o
    0x080068e4   0x080068e4   0x00000002   Code   RO          260    i.UsageFault_Handler  stm32h7xx_it.o
    0x080068e6   0x080068e6   0x00000030   Code   RO         5852    i.__ARM_fpclassify  m_wv.l(fpclassify.o)
    0x08006916   0x08006916   0x00000022   Code   RO         2188    i.__NVIC_SetPriority  stm32h7xx_hal_cortex.o
    0x08006938   0x08006938   0x00000150   Code   RO         5560    i.__hardfp_cosf     m_wv.l(cosf.o)
    0x08006a88   0x08006a88   0x0000003a   Code   RO         5610    i.__hardfp_sqrtf    m_wv.l(sqrtf.o)
    0x08006ac2   0x08006ac2   0x00000006   Code   RO         5740    i.__mathlib_flt_infnan  m_wv.l(funder.o)
    0x08006ac8   0x08006ac8   0x00000010   Code   RO         5742    i.__mathlib_flt_invalid  m_wv.l(funder.o)
    0x08006ad8   0x08006ad8   0x00000154   Code   RO         5753    i.__mathlib_rredf2  m_wv.l(rredf.o)
    0x08006c2c   0x08006c2c   0x0000000e   Code   RO         5689    i._is_digit         c_w.l(__printf_wp.o)
    0x08006c3a   0x08006c3a   0x00000002   PAD
    0x08006c3c   0x08006c3c   0x0000001c   Code   RO         4393    i.app_adc_callback  app_main.o
    0x08006c58   0x08006c58   0x0000004c   Code   RO         4394    i.app_button_callback  app_main.o
    0x08006ca4   0x08006ca4   0x00000068   Code   RO         4396    i.app_init          app_main.o
    0x08006d0c   0x08006d0c   0x0000002c   Code   RO         4492    i.app_melp_change_state  app_melp.o
    0x08006d38   0x08006d38   0x00000078   Code   RO         4495    i.app_melp_encode_frame  app_melp.o
    0x08006db0   0x08006db0   0x0000000c   Code   RO         4397    i.app_melp_error_callback  app_main.o
    0x08006dbc   0x08006dbc   0x00000032   Code   RO         4398    i.app_melp_frame_decoded_callback  app_main.o
    0x08006dee   0x08006dee   0x00000002   PAD
    0x08006df0   0x08006df0   0x0000000c   Code   RO         4399    i.app_melp_frame_encoded_callback  app_main.o
    0x08006dfc   0x08006dfc   0x0000005c   Code   RO         4498    i.app_melp_init     app_melp.o
    0x08006e58   0x08006e58   0x000000a8   Code   RO         4499    i.app_melp_process_input  app_melp.o
    0x08006f00   0x08006f00   0x00000028   Code   RO         4503    i.app_melp_start_recording  app_melp.o
    0x08006f28   0x08006f28   0x00000002   Code   RO         4400    i.app_melp_state_changed_callback  app_main.o
    0x08006f2a   0x08006f2a   0x00000022   Code   RO         4505    i.app_melp_stop_recording  app_melp.o
    0x08006f4c   0x08006f4c   0x00000002   Code   RO         4403    i.app_uart_tx_callback  app_main.o
    0x08006f4e   0x08006f4e   0x00000002   PAD
    0x08006f50   0x08006f50   0x00000084   Code   RO           18    i.debug_init        main.o
    0x08006fd4   0x08006fd4   0x00000174   Code   RO           19    i.debug_print_system_info  main.o
    0x08007148   0x08007148   0x00000030   Code   RO           20    i.debug_printf      main.o
    0x08007178   0x08007178   0x00000158   Code   RO           21    i.debug_test_button  main.o
    0x080072d0   0x080072d0   0x000000e0   Code   RO           22    i.debug_test_led    main.o
    0x080073b0   0x080073b0   0x000000d0   Code   RO           23    i.debug_test_uart   main.o
    0x08007480   0x08007480   0x0000002c   Code   RO         4711    i.hal_adc_init      hal_adc.o
    0x080074ac   0x080074ac   0x00000030   Code   RO         4714    i.hal_adc_start_continuous  hal_adc.o
    0x080074dc   0x080074dc   0x00000024   Code   RO         4715    i.hal_adc_stop_continuous  hal_adc.o
    0x08007500   0x08007500   0x00000028   Code   RO         4795    i.hal_dac_init      hal_dac.o
    0x08007528   0x08007528   0x00000028   Code   RO         4801    i.hal_dac_write_single  hal_dac.o
    0x08007550   0x08007550   0x0000001c   Code   RO         4881    i.hal_gpio_init     hal_gpio.o
    0x0800756c   0x0800756c   0x00000024   Code   RO         4882    i.hal_gpio_read_button  hal_gpio.o
    0x08007590   0x08007590   0x00000014   Code   RO         4883    i.hal_gpio_set_button_callback  hal_gpio.o
    0x080075a4   0x080075a4   0x0000002c   Code   RO         4884    i.hal_gpio_set_led  hal_gpio.o
    0x080075d0   0x080075d0   0x00000014   Code   RO         4967    i.hal_uart_init     hal_uart.o
    0x080075e4   0x080075e4   0x0000002c   Code   RO         4971    i.hal_uart_send_async  hal_uart.o
    0x08007610   0x08007610   0x00000734   Code   RO           24    i.main              main.o
    0x08007d44   0x08007d44   0x00000078   Code   RO         5073    i.melp_decoder_init  melp_decoder.o
    0x08007dbc   0x08007dbc   0x00000012   Code   RO         5313    i.melp_encoder_deinit  melp_encoder.o
    0x08007dce   0x08007dce   0x00000002   PAD
    0x08007dd0   0x08007dd0   0x00000108   Code   RO         5314    i.melp_encoder_encode_frame  melp_encoder.o
    0x08007ed8   0x08007ed8   0x0000008c   Code   RO         5316    i.melp_encoder_init  melp_encoder.o
    0x08007f64   0x08007f64   0x00000038   Code   RO         5317    i.melp_encoder_pack_frame  melp_encoder.o
    0x08007f9c   0x08007f9c   0x000000f0   Code   RO         5318    i.melp_levinson_durbin  melp_encoder.o
    0x0800808c   0x0800808c   0x000000c8   Code   RO         5319    i.melp_lpc_analysis  melp_encoder.o
    0x08008154   0x08008154   0x0000003c   Code   RO         5320    i.melp_lpc_to_lsf   melp_encoder.o
    0x08008190   0x08008190   0x0000006c   Code   RO         5321    i.melp_pitch_estimation  melp_encoder.o
    0x080081fc   0x080081fc   0x00000070   Code   RO         5322    i.melp_quantize_parameters  melp_encoder.o
    0x0800826c   0x0800826c   0x00000068   Code   RO         5323    i.melp_voicing_analysis  melp_encoder.o
    0x080082d4   0x080082d4   0x0000002c   Code   RO         5838    locale$$code        c_w.l(lc_numeric_c.o)
    0x08008300   0x08008300   0x0000002c   Code   RO         5859    locale$$code        c_w.l(lc_ctype_c.o)
    0x0800832c   0x0800832c   0x0000000a   Code   RO         5927    x$fpl$fpinit        fz_wv.l(fpinit.o)
    0x08008336   0x08008336   0x00000004   Code   RO         5728    x$fpl$printf1       fz_wv.l(printf1.o)
    0x0800833a   0x0800833a   0x00000004   Code   RO         5730    x$fpl$printf2       fz_wv.l(printf2.o)
    0x0800833e   0x0800833e   0x00000000   Code   RO         5736    x$fpl$usenofp       fz_wv.l(usenofp.o)
    0x0800833e   0x0800833e   0x00000018   Data   RO         3881    .constdata          stm32h7xx_hal_uart.o
    0x08008356   0x08008356   0x00000010   Data   RO         4247    .constdata          stm32h7xx_hal_uart_ex.o
    0x08008366   0x08008366   0x00000010   Data   RO         4353    .constdata          system_stm32h7xx.o
    0x08008376   0x08008376   0x00000002   PAD
    0x08008378   0x08008378   0x00000010   Data   RO         4405    .constdata          app_main.o
    0x08008388   0x08008388   0x00000008   Data   RO         5648    .constdata          c_w.l(_printf_wctomb.o)
    0x08008390   0x08008390   0x00000028   Data   RO         5677    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x080083b8   0x080083b8   0x00000011   Data   RO         5697    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x080083c9   0x080083c9   0x00000003   PAD
    0x080083cc   0x080083cc   0x00000020   Data   RO         5754    .constdata          m_wv.l(rredf.o)
    0x080083ec   0x080083ec   0x00000026   Data   RO         5785    .constdata          c_w.l(_printf_fp_hex.o)
    0x08008412   0x08008412   0x00000002   PAD
    0x08008414   0x08008414   0x00000094   Data   RO         5813    .constdata          c_w.l(bigflt0.o)
    0x080084a8   0x080084a8   0x00000020   Data   RO         5985    Region$$Table       anon$$obj.o
    0x080084c8   0x080084c8   0x0000001c   Data   RO         5837    locale$$data        c_w.l(lc_numeric_c.o)
    0x080084e4   0x080084e4   0x00000110   Data   RO         5858    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080085f4, Size: 0x00000000, Max: 0x00020000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM2 (Exec base: 0x24000000, Load base: 0x080085f4, Size: 0x00002018, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x24000000   0x080085f4   0x0000000c   Data   RW         2368    .data               stm32h7xx_hal.o
    0x2400000c   0x08008600   0x00000008   Data   RW         4354    .data               system_stm32h7xx.o
    0x24000014   0x08008608   0x0000000c   Data   RW         4406    .data               app_main.o
    0x24000020   0x08008614   0x00000001   Data   RW         4507    .data               app_melp.o
    0x24000021   0x08008615   0x00000003   PAD
    0x24000024   0x08008618   0x00000008   Data   RW         4718    .data               hal_adc.o
    0x2400002c   0x08008620   0x00000008   Data   RW         4802    .data               hal_dac.o
    0x24000034   0x08008628   0x00000008   Data   RW         4886    .data               hal_gpio.o
    0x2400003c   0x08008630   0x00000010   Data   RW         4977    .data               hal_uart.o
    0x2400004c   0x08008640   0x00000008   Data   RW         5081    .data               melp_decoder.o
    0x24000054   0x08008648   0x00000001   Data   RW         5325    .data               melp_encoder.o
    0x24000055   0x08008649   0x00000003   PAD
    0x24000058        -       0x0000020c   Zero   RW           25    .bss                main.o
    0x24000264        -       0x000011a0   Zero   RW         4404    .bss                app_main.o
    0x24001404        -       0x00000010   Zero   RW         4506    .bss                app_melp.o
    0x24001414        -       0x000002d0   Zero   RW         4717    .bss                hal_adc.o
    0x240016e4        -       0x000002d0   Zero   RW         5324    .bss                melp_encoder.o
    0x240019b4        -       0x00000060   Zero   RW         5799    .bss                c_w.l(libspace.o)
    0x24001a14   0x08008649   0x00000004   PAD
    0x24001a18        -       0x00000200   Zero   RW            2    HEAP                startup_stm32h750xx.o
    0x24001c18        -       0x00000400   Zero   RW            1    STACK               startup_stm32h750xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       286         46         16         12       4512      10591   app_main.o
       498         30          0          1         16       5314   app_melp.o
       236         54          0          8        720       3968   hal_adc.o
        80         16          0          8          0       2040   hal_dac.o
       128         24          0          8          0       3189   hal_gpio.o
       124         32          0         16          0       3428   hal_uart.o
      3940       2170          0          0        524    1327410   main.o
       120         16          0          8          0       4391   melp_decoder.o
      1302         94          0          1        720      12337   melp_encoder.o
        72         30        664          0       1536        860   startup_stm32h750xx.o
       292         72          0         12          0      26368   stm32h7xx_hal.o
      2940        272          0          0          0      77186   stm32h7xx_hal_adc.o
       460         32          0          0          0      64469   stm32h7xx_hal_adc_ex.o
       328         28          0          0          0      41304   stm32h7xx_hal_cortex.o
       504          4          0          0          0       4310   stm32h7xx_hal_dac.o
      2076         94          0          0          0       5235   stm32h7xx_hal_dma.o
       592         50          0          0          0       3696   stm32h7xx_hal_gpio.o
       412         46          0          0          0       3321   stm32h7xx_hal_msp.o
        84          6          0          0          0        801   stm32h7xx_hal_pwr_ex.o
      2466        102          0          0          0       7268   stm32h7xx_hal_rcc.o
      4776        174          0          0          0      10703   stm32h7xx_hal_rcc_ex.o
      2168        124         24          0          0      31016   stm32h7xx_hal_uart.o
       284          6         16          0          0       3889   stm32h7xx_hal_uart_ex.o
        20          0          0          0          0       4274   stm32h7xx_it.o
       276         48         16          8          0       2065   system_stm32h7xx.o

    ----------------------------------------------------------------------
     24488       <USER>        <GROUP>         88       8028    1659433   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        24          0          2          6          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
       132          0          0          0          0         68   rt_memmove_v6.o
       122          0          0          0          0         80   rt_memmove_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       104          0          0          0          0         68   strcmpv7m_pel.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
       336         60          0          0          0        136   cosf.o
        48          0          0          0          0        124   fpclassify.o
        22          6          0          0          0        232   funder.o
       340         24         32          0          0        160   rredf.o
        58          0          0          0          0        136   sqrtf.o

    ----------------------------------------------------------------------
      8446        <USER>        <GROUP>          0        100       5820   Library Totals
        22          0          5          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7602        270        551          0         96       4684   c_w.l
        18          0          0          0          0        348   fz_wv.l
       804         90         32          0          0        788   m_wv.l

    ----------------------------------------------------------------------
      8446        <USER>        <GROUP>          0        100       5820   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     32934       3930       1358         88       8128    1648353   Grand Totals
     32934       3930       1358         88       8128    1648353   ELF Image Totals
     32934       3930       1358         88          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                34292 (  33.49kB)
    Total RW  Size (RW Data + ZI Data)              8216 (   8.02kB)
    Total ROM Size (Code + RO Data + RW Data)      34380 (  33.57kB)

==============================================================================

