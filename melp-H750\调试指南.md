# STM32H750 MELP 项目调试指南

## 问题描述
1. CPU启动时串口没有任何打印输出
2. 蓝灯常亮，按键没有反应

## 解决方案

### 步骤1: 使用简化测试程序

首先使用简化的测试程序来验证基本硬件功能：

1. **备份当前main.c**：
   ```
   重命名 melp-H750/Core/Src/main.c 为 main_backup.c
   ```

2. **使用简化测试程序**：
   ```
   重命名 melp-H750/Core/Src/main_simple_test.c 为 main.c
   ```

3. **编译并下载程序**

4. **连接串口调试**：
   - 波特率：921600
   - 数据位：8
   - 停止位：1
   - 校验位：无
   - 流控：无

### 步骤2: 检查硬件连接

#### LED连接 (PC13)
- LED应该连接到PC13引脚
- LED为低电平有效（PC13=0时LED亮，PC13=1时LED灭）

#### 按键连接 (PC1)
- 按键应该连接到PC1引脚
- 按键为低电平有效（按下时PC1=0，释放时PC1=1）
- 内部已配置上拉电阻

#### 串口连接 (USART1)
- TX: PA9
- RX: PA10
- 波特率: 921600

### 步骤3: 预期的调试输出

如果硬件正常，您应该看到以下输出：

```
=== STM32H750 MELP Debug System ===
System Clock: 240000000 Hz
Debug initialized successfully

=== STM32H750 Simple Hardware Test ===
This is a simplified test program
System Clock: 240000000 Hz
Testing LED (PC13)...
LED OFF (PC13 = HIGH)
LED ON (PC13 = LOW)
Starting main test loop...
Press button (PC1) to see response
Loop: 0, Button: RELEASED, LED: OFF
Loop: 100000, Button: RELEASED, LED: OFF
...
```

### 步骤4: 测试按键功能

1. 按下按键，应该看到：
   ```
   Button PRESSED (PC1 = LOW)
   ```
   同时LED应该点亮

2. 释放按键，应该看到：
   ```
   Button RELEASED (PC1 = HIGH)
   ```
   同时LED应该熄灭

### 步骤5: 故障排除

#### 如果没有串口输出：
1. 检查串口连接线
2. 检查串口参数设置
3. 检查PA9(TX)引脚连接
4. 尝试不同的串口工具

#### 如果LED不工作：
1. 检查PC13引脚连接
2. 检查LED极性
3. 检查LED限流电阻

#### 如果按键不工作：
1. 检查PC1引脚连接
2. 检查按键接地连接
3. 用万用表测试按键通断

### 步骤6: 恢复完整程序

如果简化测试程序工作正常，说明硬件没有问题，可以恢复完整程序：

1. **恢复原程序**：
   ```
   重命名 main.c 为 main_simple_test.c
   重命名 main_backup.c 为 main.c
   ```

2. **编译并运行**，现在应该能看到详细的调试输出

### 步骤7: 分析完整程序的调试输出

完整程序会输出详细的初始化信息：

```
=== STM32H750 MELP System Starting ===
HAL_Init: OK
SystemClock_Config: OK
MX_GPIO_Init: OK
MX_ADC1_Init: OK
MX_DAC1_Init: OK
MX_USART1_Init: OK
=== System Information ===
MCU: STM32H750VBT6
System Clock: 240000000 Hz
...
Testing basic hardware...
...
Initializing application...
app_init() SUCCESS
Starting main application loop...
```

如果在某个步骤失败，会看到错误信息：
```
[ERROR] app_init() FAILED!
=== SYSTEM ERROR OCCURRED ===
```

## 常见问题解决

### 问题1: 蓝灯常亮
**原因**: 可能是app_init()失败，进入Error_Handler()
**解决**: 查看串口输出中的错误信息

### 问题2: 按键无反应
**原因**: 系统可能在Error_Handler()中禁用了中断
**解决**: 修复导致Error_Handler()的根本问题

### 问题3: 串口无输出
**原因**: 
- 串口参数不匹配
- 硬件连接问题
- 时钟配置问题
**解决**: 使用简化测试程序验证基本功能

## 联系支持

如果问题仍然存在，请提供：
1. 串口调试输出的完整日志
2. 硬件连接图片
3. 使用的开发板型号
4. 编译器版本和设置
