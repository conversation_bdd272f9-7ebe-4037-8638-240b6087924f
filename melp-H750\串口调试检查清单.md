# STM32H750 串口调试检查清单

## 问题现象
- 蓝灯闪烁3次，然后常亮几秒后熄灭
- 串口没有任何输出

## 分析
蓝灯有反应说明程序在运行，但串口无输出可能是以下原因：

## 🔍 检查清单

### 1. 硬件连接检查

#### USART1引脚连接 (最重要!)
- **TX引脚**: PA9 → 连接到USB转串口的RX
- **RX引脚**: PA10 → 连接到USB转串口的TX  
- **GND**: 必须连接STM32的GND到USB转串口的GND
- **电源**: 确认STM32供电正常

#### 常见错误
❌ TX和RX接反了 (PA9应该接USB转串口的RX)
❌ 没有连接GND
❌ 使用了错误的引脚 (不是PA9/PA10)

### 2. 串口工具设置检查

#### 串口参数
- **波特率**: 921600 (非常重要!)
- **数据位**: 8
- **停止位**: 1  
- **校验位**: 无
- **流控**: 无

#### 常见问题
❌ 波特率设置错误 (很多工具默认115200)
❌ 选择了错误的COM口
❌ 串口被其他程序占用

### 3. 使用最小测试程序

我已经创建了 `main_minimal.c`，它会通过LED闪烁告诉您每个步骤的状态：

#### LED闪烁含义
1. **5次快闪** → HAL_Init 成功
2. **3次中速闪** → SystemClock_Config 成功  
3. **2次慢闪** → GPIO_Init 成功
4. **1次长亮** → UART_Init 成功
5. **常亮2秒** → UART发送成功
6. **快速闪烁** → UART发送失败

#### 使用方法
```bash
# 备份当前main.c
mv main.c main_backup.c

# 使用最小测试版本
mv main_minimal.c main.c

# 编译下载
```

### 4. 逐步排查

#### 步骤1: 观察LED闪烁模式
根据LED闪烁判断程序执行到哪一步：
- 如果只有5次快闪，说明在SystemClock_Config失败
- 如果有5+3次闪烁，说明在GPIO_Init失败
- 以此类推...

#### 步骤2: 检查串口连接
如果LED显示UART_Init成功但仍无输出：

1. **确认引脚连接**:
   ```
   STM32 PA9 (TX) → USB转串口 RX
   STM32 PA10(RX) → USB转串口 TX  
   STM32 GND     → USB转串口 GND
   ```

2. **测试串口工具**:
   - 先断开STM32
   - 将USB转串口的TX和RX短接
   - 在串口工具中发送字符，应该能收到回显
   - 如果没有回显，说明串口工具或驱动有问题

3. **尝试不同波特率**:
   - 921600 (默认)
   - 115200 (常用)
   - 9600 (最稳定)

#### 步骤3: 检查时钟配置
如果程序在SystemClock_Config卡住：
- 可能是HSE晶振问题
- 尝试使用HSI内部时钟

### 5. 替代测试方法

#### 方法1: 使用更低波特率
修改main_minimal.c中的波特率：
```c
huart1.Init.BaudRate = 115200;  // 改为115200
```

#### 方法2: 使用不同串口
如果PA9/PA10有问题，可以尝试其他UART：
- UART4: PC10(TX), PC11(RX)  
- USART2: PA2(TX), PA3(RX)
- USART3: PB10(TX), PB11(RX)

#### 方法3: 使用SWO调试输出
如果有ST-Link，可以使用SWO输出：
```c
// 在main函数开始添加
ITM_SendChar('H');
ITM_SendChar('i');
ITM_SendChar('\n');
```

### 6. 常见问题解决

#### 问题1: 程序重启循环
现象：LED不断重复闪烁模式
原因：可能是看门狗或硬件故障重启
解决：检查电源稳定性

#### 问题2: LED闪烁后停止
现象：LED闪烁几次后不再闪烁
原因：程序可能进入Error_Handler或死循环
解决：使用调试器查看程序状态

#### 问题3: 串口工具无法打开
现象：提示COM口被占用
解决：
- 关闭其他可能使用串口的程序
- 重新插拔USB转串口
- 重启电脑

### 7. 最终验证

如果一切正常，您应该看到：
```
STM32H750 UART Test - Hello World!
System Clock: 240000000 Hz
HAL Version: ...
Device ID: 0x...
Press button (PC1) to test...
Heartbeat: 0
Heartbeat: 5
Heartbeat: 10
...
```

按下按键应该看到：
```
Button PRESSED
Button RELEASED
```

## 📞 如果仍有问题

请提供以下信息：
1. LED闪烁的具体模式 (几次快闪，几次慢闪等)
2. 使用的串口工具名称和设置
3. 硬件连接的照片
4. 是否使用了USB转串口模块 (型号)

这样我就能更准确地帮您定位问题！
