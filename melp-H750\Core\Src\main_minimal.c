/* SPDX-License-Identifier: MIT */
/**
 * @file main_minimal.c
 * @brief Minimal test version for basic hardware verification
 * <AUTHOR> Assistant
 * @date 2025-01-22
 * 
 * This is the most basic version to test hardware step by step.
 * It will help identify exactly where the problem occurs.
 */

#include "main.h"

/* External variables */
UART_HandleTypeDef huart1;

/* Function prototypes */
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_USART1_Init(void);
void Error_Handler(void);

/**
 * @brief Minimal main function for step-by-step testing
 */
int main(void)
{
    /* Step 1: HAL Init */
    HAL_Init();
    
    /* Step 2: LED test immediately after HAL_Init */
    // Enable GPIOC clock first
    __HAL_RCC_GPIOC_CLK_ENABLE();
    
    // Configure PC13 as output for LED
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_13;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    
    // LED test sequence - this should work immediately
    // Flash LED 5 times to indicate HAL_Init success
    for (int i = 0; i < 5; i++) {
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET); // LED ON
        HAL_Delay(200);
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);   // LED OFF
        HAL_Delay(200);
    }
    
    /* Step 3: System Clock Config */
    SystemClock_Config();
    
    // Flash LED 3 times to indicate SystemClock_Config success
    for (int i = 0; i < 3; i++) {
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET); // LED ON
        HAL_Delay(300);
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);   // LED OFF
        HAL_Delay(300);
    }
    
    /* Step 4: GPIO Init */
    MX_GPIO_Init();
    
    // Flash LED 2 times to indicate GPIO init success
    for (int i = 0; i < 2; i++) {
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET); // LED ON
        HAL_Delay(400);
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);   // LED OFF
        HAL_Delay(400);
    }
    
    /* Step 5: UART Init */
    MX_USART1_Init();
    
    // Flash LED 1 time to indicate UART init success
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET); // LED ON
    HAL_Delay(500);
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);   // LED OFF
    HAL_Delay(500);
    
    /* Step 6: Test UART output */
    const char test_msg[] = "STM32H750 UART Test - Hello World!\r\n";
    HAL_StatusTypeDef uart_status = HAL_UART_Transmit(&huart1, (uint8_t*)test_msg, sizeof(test_msg)-1, 1000);
    
    if (uart_status == HAL_OK) {
        // UART success - LED solid ON for 2 seconds
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
        HAL_Delay(2000);
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);
    } else {
        // UART failed - rapid blinking
        for (int i = 0; i < 10; i++) {
            HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
            HAL_Delay(100);
            HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);
            HAL_Delay(100);
        }
    }
    
    /* Step 7: Send more detailed info */
    char buffer[200];
    int len = sprintf(buffer, "System Clock: %lu Hz\r\nHAL Version: %lu\r\nDevice ID: 0x%04lX\r\n", 
                     HAL_RCC_GetSysClockFreq(), HAL_GetHalVersion(), HAL_GetDEVID());
    HAL_UART_Transmit(&huart1, (uint8_t*)buffer, len, 1000);
    
    /* Step 8: Button test setup */
    const char button_msg[] = "Press button (PC1) to test...\r\n";
    HAL_UART_Transmit(&huart1, (uint8_t*)button_msg, sizeof(button_msg)-1, 1000);
    
    uint32_t loop_count = 0;
    GPIO_PinState last_button_state = GPIO_PIN_SET;
    
    /* Main loop */
    while (1)
    {
        // Read button state (PC1, active low)
        GPIO_PinState button_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_1);
        
        // Check for button state change
        if (button_state != last_button_state) {
            if (button_state == GPIO_PIN_RESET) {
                const char pressed_msg[] = "Button PRESSED\r\n";
                HAL_UART_Transmit(&huart1, (uint8_t*)pressed_msg, sizeof(pressed_msg)-1, 100);
                // Turn on LED when button pressed
                HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
            } else {
                const char released_msg[] = "Button RELEASED\r\n";
                HAL_UART_Transmit(&huart1, (uint8_t*)released_msg, sizeof(released_msg)-1, 100);
                // Turn off LED when button released
                HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);
            }
            last_button_state = button_state;
            HAL_Delay(50); // Debounce
        }
        
        // Send heartbeat every 5 seconds
        if (loop_count % 5000 == 0) {
            char heartbeat[50];
            int hb_len = sprintf(heartbeat, "Heartbeat: %lu\r\n", loop_count / 1000);
            HAL_UART_Transmit(&huart1, (uint8_t*)heartbeat, hb_len, 100);
        }
        
        loop_count++;
        HAL_Delay(1);
    }
}

/**
 * @brief System Clock Configuration
 * @retval None
 */
void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    /** Supply configuration update enable */
    HAL_PWREx_ConfigSupply(PWR_LDO_SUPPLY);

    /** Configure the main internal regulator output voltage */
    __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE2);

    while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {}

    /** Initializes the RCC Oscillators according to the specified parameters */
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
    RCC_OscInitStruct.HSIState = RCC_HSI_DIV1;
    RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
    RCC_OscInitStruct.PLL.PLLM = 4;
    RCC_OscInitStruct.PLL.PLLN = 30;
    RCC_OscInitStruct.PLL.PLLP = 2;
    RCC_OscInitStruct.PLL.PLLQ = 4;
    RCC_OscInitStruct.PLL.PLLR = 2;
    RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_3;
    RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOWIDE;
    RCC_OscInitStruct.PLL.PLLFRACN = 0;
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
    {
        Error_Handler();
    }

    /** Initializes the CPU, AHB and APB buses clocks */
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                                |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2
                                |RCC_CLOCKTYPE_D3PCLK1|RCC_CLOCKTYPE_D1PCLK1;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;
    RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV1;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV1;
    RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV1;

    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
    {
        Error_Handler();
    }
}

/**
 * @brief USART1 Initialization Function
 */
static void MX_USART1_Init(void)
{
    // Enable clocks
    __HAL_RCC_USART1_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
    
    // Configure GPIO pins for USART1 (PA9=TX, PA10=RX)
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_9|GPIO_PIN_10;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART1;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    // Configure USART1
    huart1.Instance = USART1;
    huart1.Init.BaudRate = 921600;
    huart1.Init.WordLength = UART_WORDLENGTH_8B;
    huart1.Init.StopBits = UART_STOPBITS_1;
    huart1.Init.Parity = UART_PARITY_NONE;
    huart1.Init.Mode = UART_MODE_TX_RX;
    huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart1.Init.OverSampling = UART_OVERSAMPLING_16;
    huart1.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
    huart1.Init.ClockPrescaler = UART_PRESCALER_DIV1;
    huart1.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
    if (HAL_UART_Init(&huart1) != HAL_OK)
    {
        Error_Handler();
    }
}

/**
 * @brief GPIO Initialization Function
 */
static void MX_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* GPIO Ports Clock Enable */
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();

    /*Configure GPIO pin Output Level for LED (PC13) */
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET); // LED OFF initially

    /*Configure GPIO pin : PC13 (LED) */
    GPIO_InitStruct.Pin = GPIO_PIN_13;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    /*Configure GPIO pin : PC1 (Button) */
    GPIO_InitStruct.Pin = GPIO_PIN_1;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
}

/**
 * @brief Error Handler
 */
void Error_Handler(void)
{
    /* Disable interrupts */
    __disable_irq();
    
    /* Flash LED very rapidly to indicate error */
    while (1)
    {
        HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
        for (volatile int i = 0; i < 200000; i++); // Very fast blink
    }
}
