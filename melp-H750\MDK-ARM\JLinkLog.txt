T9704 000:004.029   SEGGER J-Link V8.16 Log File
T9704 000:004.139   DLL Compiled: Feb 26 2025 12:07:26
T9704 000:004.145   Logging started @ 2025-07-23 01:06
T9704 000:004.150   Process: d:\Keil_v5\UV4\UV4.exe
T9704 000:004.165 - 4.155ms 
T9704 000:004.174 JLINK_SetWarnOutHandler(...)
T9704 000:004.179 - 0.006ms 
T9704 000:004.189 JLINK_OpenEx(...)
T9704 000:007.451   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T9704 000:008.636   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T9704 000:008.774   Decompressing FW timestamp took 119 us
T9704 000:017.646   Hardware: V9.40
T9704 000:017.661   S/N: 69402410
T9704 000:017.669   OEM: SEGGER
T9704 000:017.676   Feature(s): R<PERSON>, GD<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JFlash
T9704 000:018.874   Bootloader: (FW returned invalid version)
T9704 000:020.198   TELNET listener socket opened on port 19021
T9704 000:020.293   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T9704 000:020.416   WEBSRV Webserver running on local port 19080
T9704 000:020.495   Looking for J-Link GUI Server exe at: d:\Keil_v5\ARM\Segger\JLinkGUIServer.exe
T9704 000:020.590   Looking for J-Link GUI Server exe at: C:\Program Files\SEGGER\JLink_V816\JLinkGUIServer.exe
T9704 000:020.624   Forking J-Link GUI Server: C:\Program Files\SEGGER\JLink_V816\JLinkGUIServer.exe
T9704 000:022.909   J-Link GUI Server info: "J-Link GUI server V8.16 "
T9704 000:023.133 - 18.939ms returns "O.K."
T9704 000:023.152 JLINK_GetEmuCaps()
T9704 000:023.161 - 0.006ms returns 0xB9FF7BBF
T9704 000:023.171 JLINK_TIF_GetAvailable(...)
T9704 000:023.512 - 0.341ms 
T9704 000:023.530 JLINK_SetErrorOutHandler(...)
T9704 000:023.534 - 0.004ms 
T9704 000:023.552 JLINK_ExecCommand("ProjectFile = "G:\work\AHD_TEST\My_prj_Embedded\voice\melp-H750\MDK-ARM\JLinkSettings.ini"", ...). 
T9704 000:039.150   Ref file found at: d:\Keil_v5\ARM\Segger\JLinkDevices.ref
T9704 000:039.238   REF file references invalid XML file: C:\Program Files\SEGGER\JLink_V816\JLinkDevices.xml
T9704 000:040.994 - 17.442ms returns 0x00
T9704 000:044.585 JLINK_ExecCommand("Device = STM32H750VBTx", ...). 
T9704 000:045.816   Flash bank @ 0x08000000: SFL: Parsing sectorization info from ELF file
T9704 000:045.828     FlashDevice.SectorInfo[0]: .SectorSize = 0x00020000, .SectorStartAddr = 0x00000000
T9704 000:045.834   FlashBank @0x08000000: Sectorization info from SFL ELF file ignored because sectorization override from DLL / XML file is active.
T9704 000:045.898   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
T9704 000:045.904     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
T9704 000:065.994   Device "STM32H750VB" selected.
T9704 000:066.233 - 21.636ms returns 0x00
T9704 000:066.249 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T9704 000:066.290   ERROR: Unknown command
T9704 000:066.299 - 0.022ms returns 0x01
T9704 000:066.306 JLINK_GetHardwareVersion()
T9704 000:066.312 - 0.005ms returns 94000
T9704 000:066.318 JLINK_GetDLLVersion()
T9704 000:066.323 - 0.004ms returns 81600
T9704 000:066.329 JLINK_GetOEMString(...)
T9704 000:066.336 JLINK_GetFirmwareString(...)
T9704 000:066.341 - 0.005ms 
T9704 000:076.816 JLINK_GetDLLVersion()
T9704 000:076.831 - 0.014ms returns 81600
T9704 000:076.837 JLINK_GetCompileDateTime()
T9704 000:076.843 - 0.005ms 
T9704 000:079.118 JLINK_GetFirmwareString(...)
T9704 000:079.130 - 0.011ms 
T9704 000:083.324 JLINK_GetHardwareVersion()
T9704 000:083.336 - 0.012ms returns 94000
T9704 000:086.510 JLINK_GetSN()
T9704 000:086.524 - 0.013ms returns 69402410
T9704 000:089.792 JLINK_GetOEMString(...)
T9704 000:095.413 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T9704 000:096.640 - 1.228ms returns 0x00
T9704 000:096.650 JLINK_HasError()
T9704 000:096.661 JLINK_SetSpeed(1000)
T9704 000:096.941 - 0.281ms 
T9704 000:096.949 JLINK_GetId()
T9704 000:104.329   ConfigTargetSettings() start
T9704 000:104.351    J-Link Script File: Executing ConfigTargetSettings()
T9704 000:106.644   ConfigTargetSettings() end - Took 34us
T9704 000:109.210   InitTarget() start
T9704 000:109.259    J-Link Script File: Executing InitTarget()
T9704 000:112.370   SWD selected. Executing JTAG -> SWD switching sequence.
T9704 000:119.811   DAP initialized successfully.
T9704 000:140.587   InitTarget() end - Took 28.9ms
T9704 000:144.471   Found SW-DP with ID 0x6BA02477
T9704 000:151.142   DPIDR: 0x6BA02477
T9704 000:154.027   CoreSight SoC-400 or earlier
T9704 000:156.307   Scanning AP map to find all available APs
T9704 000:161.220   AP[3]: Stopped AP scan as end of AP map has been reached
T9704 000:164.527   AP[0]: AHB-AP (IDR: 0x84770001, ADDR: 0x00000000)
T9704 000:166.723   AP[1]: AHB-AP (IDR: 0x84770001, ADDR: 0x01000000)
T9704 000:169.238   AP[2]: APB-AP (IDR: 0x54770002, ADDR: 0x02000000)
T9704 000:171.604   Iterating through AP map to find AHB-AP to use
T9704 000:175.597   AP[0]: Core found
T9704 000:177.855   AP[0]: AHB-AP ROM base: 0xE00FE000
T9704 000:183.056   CPUID register: 0x411FC271. Implementer code: 0x41 (ARM)
T9704 000:188.745   Cache: L1 I/D-cache present
T9704 000:191.089   Found Cortex-M7 r1p1, Little endian.
T9704 000:191.795   -- Max. mem block: 0x00010E60
T9704 000:192.897   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T9704 000:193.700   CPU_ReadMem(4 bytes @ 0xE0002000)
T9704 000:197.265   FPUnit: 8 code (BP) slots and 0 literal slots
T9704 000:197.284   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T9704 000:198.052   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T9704 000:198.929   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:199.724   CPU_WriteMem(4 bytes @ 0xE0001000)
T9704 000:200.473   CPU_ReadMem(4 bytes @ 0xE000ED88)
T9704 000:201.298   CPU_WriteMem(4 bytes @ 0xE000ED88)
T9704 000:202.045   CPU_ReadMem(4 bytes @ 0xE000ED88)
T9704 000:202.794   CPU_WriteMem(4 bytes @ 0xE000ED88)
T9704 000:205.897   CoreSight components:
T9704 000:208.174   ROMTbl[0] @ E00FE000
T9704 000:208.199   CPU_ReadMem(64 bytes @ 0xE00FE000)
T9704 000:210.021   CPU_ReadMem(32 bytes @ 0xE00FFFE0)
T9704 000:214.065   [0][0]: E00FF000 CID B105100D PID 000BB4C7 ROM Table
T9704 000:216.292   ROMTbl[1] @ E00FF000
T9704 000:216.311   CPU_ReadMem(64 bytes @ 0xE00FF000)
T9704 000:218.358   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T9704 000:223.592   [1][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T9704 000:223.613   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T9704 000:227.561   [1][1]: E0001000 CID B105E00D PID 000BB002 DWT
T9704 000:227.583   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T9704 000:231.565   [1][2]: E0002000 CID B105E00D PID 000BB00E FPB-M7
T9704 000:231.585   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T9704 000:235.683   [1][3]: E0000000 CID B105E00D PID 000BB001 ITM
T9704 000:235.703   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T9704 000:239.434   [0][1]: E0041000 CID B105900D PID 001BB975 ETM-M7
T9704 000:239.452   CPU_ReadMem(32 bytes @ 0xE0043FE0)
T9704 000:243.454   [0][2]: ******** CID B105900D PID 004BB906 CTI
T9704 000:243.473   CPU_WriteMem(4 bytes @ 0xE000ED84)
T9704 000:244.228   CPU_ReadMem(4 bytes @ 0xE000ED80)
T9704 000:247.530   I-Cache L1: 16 KB, 256 Sets, 32 Bytes/Line, 2-Way
T9704 000:247.550   CPU_WriteMem(4 bytes @ 0xE000ED84)
T9704 000:248.379   CPU_ReadMem(4 bytes @ 0xE000ED80)
T9704 000:252.117   D-Cache L1: 16 KB, 128 Sets, 32 Bytes/Line, 4-Way
T9704 000:252.568 - 155.617ms returns 0x6BA02477
T9704 000:252.579 JLINK_GetDLLVersion()
T9704 000:252.583 - 0.003ms returns 81600
T9704 000:252.608 JLINK_CORE_GetFound()
T9704 000:252.612 - 0.004ms returns 0xE0100FF
T9704 000:252.617 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T9704 000:252.622   Value=0xE00FE000
T9704 000:252.628 - 0.011ms returns 0
T9704 000:257.131 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T9704 000:257.144   Value=0xE00FE000
T9704 000:257.150 - 0.019ms returns 0
T9704 000:257.155 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T9704 000:257.158   Value=0xE0041000
T9704 000:257.163 - 0.008ms returns 0
T9704 000:257.168 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T9704 000:257.202   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T9704 000:258.507   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T9704 000:258.520 - 1.351ms returns 32 (0x20)
T9704 000:258.527 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T9704 000:258.531   Value=0x00000000
T9704 000:258.538 - 0.010ms returns 0
T9704 000:258.544 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T9704 000:258.549   Value=0xE00F5000
T9704 000:258.555 - 0.011ms returns 0
T9704 000:258.559 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T9704 000:258.563   Value=0xE0000000
T9704 000:258.568 - 0.008ms returns 0
T9704 000:258.572 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T9704 000:258.575   Value=0xE0001000
T9704 000:258.580 - 0.008ms returns 0
T9704 000:258.584 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T9704 000:258.587   Value=0xE0002000
T9704 000:258.592 - 0.008ms returns 0
T9704 000:258.596 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T9704 000:258.600   Value=0xE000E000
T9704 000:258.605 - 0.008ms returns 0
T9704 000:258.617 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T9704 000:258.622   Value=0xE000EDF0
T9704 000:258.630 - 0.013ms returns 0
T9704 000:258.634 JLINK_GetDebugInfo(0x01 = Unknown)
T9704 000:258.638   Value=0x00000001
T9704 000:258.642 - 0.008ms returns 0
T9704 000:258.647 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T9704 000:258.656   CPU_ReadMem(4 bytes @ 0xE000ED00)
T9704 000:259.560   Data:  71 C2 1F 41
T9704 000:259.569   Debug reg: CPUID
T9704 000:259.574 - 0.927ms returns 1 (0x1)
T9704 000:259.581 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T9704 000:259.584   Value=0x00000000
T9704 000:259.590 - 0.009ms returns 0
T9704 000:259.594 JLINK_HasError()
T9704 000:259.602 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T9704 000:259.606 - 0.007ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T9704 000:259.610 JLINK_Reset()
T9704 000:259.618   JLINK_GetResetTypeDesc
T9704 000:259.622   - 0.003ms 
T9704 000:262.575   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
T9704 000:262.600   CPU is running
T9704 000:262.610   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T9704 000:263.365   CPU is running
T9704 000:263.376   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T9704 000:266.554   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T9704 000:271.744   Reset: Reset device via AIRCR.SYSRESETREQ.
T9704 000:271.762   CPU is running
T9704 000:271.772   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T9704 000:325.022   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T9704 000:325.727   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T9704 000:335.195   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T9704 000:341.439   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T9704 000:350.753   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:351.559   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T9704 000:352.381   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:353.255 - 93.643ms 
T9704 000:353.269 JLINK_Halt()
T9704 000:353.274 - 0.004ms returns 0x00
T9704 000:353.300 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T9704 000:353.312   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T9704 000:354.171   Data:  03 00 03 00
T9704 000:354.198   Debug reg: DHCSR
T9704 000:354.216 - 0.915ms returns 1 (0x1)
T9704 000:354.307 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
T9704 000:354.313   Debug reg: DHCSR
T9704 000:354.710   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T9704 000:355.488 - 1.181ms returns 0 (0x00000000)
T9704 000:355.500 JLINK_WriteU32(0xE000EDFC, 0x01000000)
T9704 000:355.504   Debug reg: DEMCR
T9704 000:355.517   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T9704 000:356.270 - 0.769ms returns 0 (0x00000000)
T9704 000:366.546 JLINK_GetHWStatus(...)
T9704 000:366.970 - 0.423ms returns 0
T9704 000:379.797 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T9704 000:379.812 - 0.015ms returns 0x08
T9704 000:379.818 JLINK_GetNumBPUnits(Type = 0xF0)
T9704 000:379.823 - 0.004ms returns 0x2000
T9704 000:379.828 JLINK_GetNumWPUnits()
T9704 000:379.833 - 0.004ms returns 4
T9704 000:391.418 JLINK_GetSpeed()
T9704 000:391.429 - 0.010ms returns 1000
T9704 000:397.390 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T9704 000:397.414   CPU_ReadMem(4 bytes @ 0xE000E004)
T9704 000:398.182   Data:  04 00 00 00
T9704 000:398.192 - 0.802ms returns 1 (0x1)
T9704 000:398.200 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T9704 000:398.208   CPU_ReadMem(4 bytes @ 0xE000E004)
T9704 000:398.951   Data:  04 00 00 00
T9704 000:398.959 - 0.758ms returns 1 (0x1)
T9704 000:398.965 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T9704 000:398.969   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T9704 000:398.980   CPU_WriteMem(28 bytes @ 0xE0001000)
T9704 000:400.183 - 1.218ms returns 0x1C
T9704 000:400.196 JLINK_Halt()
T9704 000:400.200 - 0.004ms returns 0x00
T9704 000:400.205 JLINK_IsHalted()
T9704 000:400.209 - 0.004ms returns TRUE
T9704 000:402.602 JLINK_WriteMem(0x20000000, 0x418 Bytes, ...)
T9704 000:402.612   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T9704 000:402.886   CPU_WriteMem(1048 bytes @ 0x20000000)
T9704 000:421.946 - 19.342ms returns 0x418
T9704 000:421.983 JLINK_HasError()
T9704 000:421.990 JLINK_WriteReg(R0, 0x08000000)
T9704 000:421.997 - 0.006ms returns 0
T9704 000:422.001 JLINK_WriteReg(R1, 0x017D7840)
T9704 000:422.005 - 0.003ms returns 0
T9704 000:422.009 JLINK_WriteReg(R2, 0x00000001)
T9704 000:422.013 - 0.003ms returns 0
T9704 000:422.017 JLINK_WriteReg(R3, 0x00000000)
T9704 000:422.020 - 0.003ms returns 0
T9704 000:422.024 JLINK_WriteReg(R4, 0x00000000)
T9704 000:422.028 - 0.003ms returns 0
T9704 000:422.032 JLINK_WriteReg(R5, 0x00000000)
T9704 000:422.048 - 0.016ms returns 0
T9704 000:422.054 JLINK_WriteReg(R6, 0x00000000)
T9704 000:422.059 - 0.004ms returns 0
T9704 000:422.065 JLINK_WriteReg(R7, 0x00000000)
T9704 000:422.071 - 0.005ms returns 0
T9704 000:422.089 JLINK_WriteReg(R8, 0x00000000)
T9704 000:422.096 - 0.019ms returns 0
T9704 000:422.102 JLINK_WriteReg(R9, 0x20000414)
T9704 000:422.107 - 0.005ms returns 0
T9704 000:422.113 JLINK_WriteReg(R10, 0x00000000)
T9704 000:422.118 - 0.005ms returns 0
T9704 000:422.124 JLINK_WriteReg(R11, 0x00000000)
T9704 000:422.130 - 0.005ms returns 0
T9704 000:422.135 JLINK_WriteReg(R12, 0x00000000)
T9704 000:422.140 - 0.005ms returns 0
T9704 000:422.146 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 000:422.152 - 0.005ms returns 0
T9704 000:422.158 JLINK_WriteReg(R14, 0x20000001)
T9704 000:422.163 - 0.005ms returns 0
T9704 000:422.174 JLINK_WriteReg(R15 (PC), 0x2000003E)
T9704 000:422.181 - 0.012ms returns 0
T9704 000:422.187 JLINK_WriteReg(XPSR, 0x01000000)
T9704 000:422.192 - 0.005ms returns 0
T9704 000:422.199 JLINK_WriteReg(MSP, 0x20008000)
T9704 000:422.204 - 0.005ms returns 0
T9704 000:422.210 JLINK_WriteReg(PSP, 0x20008000)
T9704 000:422.215 - 0.005ms returns 0
T9704 000:422.220 JLINK_WriteReg(CFBP, 0x00000000)
T9704 000:422.226 - 0.005ms returns 0
T9704 000:422.232 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 000:422.239 - 0.007ms returns 0x00000001
T9704 000:422.245 JLINK_Go()
T9704 000:422.256   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:423.090   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:423.842   CPU_WriteMem(4 bytes @ 0xE0002008)
T9704 000:423.850   CPU_WriteMem(4 bytes @ 0xE000200C)
T9704 000:423.855   CPU_WriteMem(4 bytes @ 0xE0002010)
T9704 000:423.860   CPU_WriteMem(4 bytes @ 0xE0002014)
T9704 000:423.865   CPU_WriteMem(4 bytes @ 0xE0002018)
T9704 000:423.870   CPU_WriteMem(4 bytes @ 0xE000201C)
T9704 000:423.875   CPU_WriteMem(4 bytes @ 0xE0002020)
T9704 000:423.880   CPU_WriteMem(4 bytes @ 0xE0002024)
T9704 000:428.246   CPU_WriteMem(4 bytes @ 0xE0001004)
T9704 000:440.510   Memory map 'after startup completion point' is active
T9704 000:440.528 - 18.282ms 
T9704 000:440.537 JLINK_IsHalted()
T9704 000:449.094 - 8.555ms returns TRUE
T9704 000:449.110 JLINK_ReadReg(R15 (PC))
T9704 000:449.119 - 0.008ms returns 0x20000000
T9704 000:449.148 JLINK_ClrBPEx(BPHandle = 0x00000001)
T9704 000:449.155 - 0.007ms returns 0x00
T9704 000:449.159 JLINK_ReadReg(R0)
T9704 000:449.191 - 0.032ms returns 0x00000000
T9704 000:449.437 JLINK_HasError()
T9704 000:449.447 JLINK_WriteReg(R0, 0x08000000)
T9704 000:449.452 - 0.005ms returns 0
T9704 000:449.457 JLINK_WriteReg(R1, 0x00020000)
T9704 000:449.460 - 0.003ms returns 0
T9704 000:449.465 JLINK_WriteReg(R2, 0x000000FF)
T9704 000:449.468 - 0.003ms returns 0
T9704 000:449.472 JLINK_WriteReg(R3, 0x00000000)
T9704 000:449.476 - 0.003ms returns 0
T9704 000:449.480 JLINK_WriteReg(R4, 0x00000000)
T9704 000:449.483 - 0.003ms returns 0
T9704 000:449.487 JLINK_WriteReg(R5, 0x00000000)
T9704 000:449.491 - 0.003ms returns 0
T9704 000:449.495 JLINK_WriteReg(R6, 0x00000000)
T9704 000:449.501 - 0.003ms returns 0
T9704 000:449.506 JLINK_WriteReg(R7, 0x00000000)
T9704 000:449.510 - 0.003ms returns 0
T9704 000:449.514 JLINK_WriteReg(R8, 0x00000000)
T9704 000:449.517 - 0.003ms returns 0
T9704 000:449.521 JLINK_WriteReg(R9, 0x20000414)
T9704 000:449.525 - 0.003ms returns 0
T9704 000:449.529 JLINK_WriteReg(R10, 0x00000000)
T9704 000:449.533 - 0.003ms returns 0
T9704 000:449.537 JLINK_WriteReg(R11, 0x00000000)
T9704 000:449.540 - 0.003ms returns 0
T9704 000:449.545 JLINK_WriteReg(R12, 0x00000000)
T9704 000:449.548 - 0.003ms returns 0
T9704 000:449.552 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 000:449.556 - 0.004ms returns 0
T9704 000:449.560 JLINK_WriteReg(R14, 0x20000001)
T9704 000:449.564 - 0.003ms returns 0
T9704 000:449.568 JLINK_WriteReg(R15 (PC), 0x20000020)
T9704 000:449.572 - 0.003ms returns 0
T9704 000:449.576 JLINK_WriteReg(XPSR, 0x01000000)
T9704 000:449.579 - 0.003ms returns 0
T9704 000:449.583 JLINK_WriteReg(MSP, 0x20008000)
T9704 000:449.587 - 0.003ms returns 0
T9704 000:449.591 JLINK_WriteReg(PSP, 0x20008000)
T9704 000:449.595 - 0.003ms returns 0
T9704 000:449.599 JLINK_WriteReg(CFBP, 0x00000000)
T9704 000:449.602 - 0.003ms returns 0
T9704 000:449.607 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 000:449.612 - 0.005ms returns 0x00000002
T9704 000:449.616 JLINK_Go()
T9704 000:449.624   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:450.485   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:459.730 - 10.113ms 
T9704 000:459.742 JLINK_IsHalted()
T9704 000:460.610 - 0.868ms returns FALSE
T9704 000:460.621 JLINK_HasError()
T9704 000:471.851 JLINK_IsHalted()
T9704 000:472.686 - 0.835ms returns FALSE
T9704 000:472.702 JLINK_HasError()
T9704 000:475.565 JLINK_IsHalted()
T9704 000:476.376 - 0.810ms returns FALSE
T9704 000:476.385 JLINK_HasError()
T9704 000:477.653 JLINK_IsHalted()
T9704 000:486.418 - 8.764ms returns TRUE
T9704 000:486.430 JLINK_ReadReg(R15 (PC))
T9704 000:486.436 - 0.006ms returns 0x20000000
T9704 000:486.441 JLINK_ClrBPEx(BPHandle = 0x00000002)
T9704 000:486.445 - 0.004ms returns 0x00
T9704 000:486.450 JLINK_ReadReg(R0)
T9704 000:486.454 - 0.003ms returns 0x00000000
T9704 000:486.719 JLINK_HasError()
T9704 000:486.732 JLINK_WriteReg(R0, 0x00000001)
T9704 000:486.740 - 0.007ms returns 0
T9704 000:486.746 JLINK_WriteReg(R1, 0x00020000)
T9704 000:486.751 - 0.005ms returns 0
T9704 000:486.757 JLINK_WriteReg(R2, 0x000000FF)
T9704 000:486.762 - 0.005ms returns 0
T9704 000:486.768 JLINK_WriteReg(R3, 0x00000000)
T9704 000:486.773 - 0.005ms returns 0
T9704 000:486.779 JLINK_WriteReg(R4, 0x00000000)
T9704 000:486.784 - 0.005ms returns 0
T9704 000:486.789 JLINK_WriteReg(R5, 0x00000000)
T9704 000:486.795 - 0.005ms returns 0
T9704 000:486.801 JLINK_WriteReg(R6, 0x00000000)
T9704 000:486.807 - 0.005ms returns 0
T9704 000:486.813 JLINK_WriteReg(R7, 0x00000000)
T9704 000:486.818 - 0.005ms returns 0
T9704 000:486.823 JLINK_WriteReg(R8, 0x00000000)
T9704 000:486.828 - 0.005ms returns 0
T9704 000:486.833 JLINK_WriteReg(R9, 0x20000414)
T9704 000:486.836 - 0.003ms returns 0
T9704 000:486.841 JLINK_WriteReg(R10, 0x00000000)
T9704 000:486.967 - 0.126ms returns 0
T9704 000:486.972 JLINK_WriteReg(R11, 0x00000000)
T9704 000:486.976 - 0.003ms returns 0
T9704 000:486.980 JLINK_WriteReg(R12, 0x00000000)
T9704 000:486.984 - 0.003ms returns 0
T9704 000:486.988 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 000:486.996 - 0.008ms returns 0
T9704 000:487.000 JLINK_WriteReg(R14, 0x20000001)
T9704 000:487.026 - 0.025ms returns 0
T9704 000:487.030 JLINK_WriteReg(R15 (PC), 0x2000009C)
T9704 000:487.034 - 0.003ms returns 0
T9704 000:487.038 JLINK_WriteReg(XPSR, 0x01000000)
T9704 000:487.042 - 0.003ms returns 0
T9704 000:487.046 JLINK_WriteReg(MSP, 0x20008000)
T9704 000:487.049 - 0.003ms returns 0
T9704 000:487.053 JLINK_WriteReg(PSP, 0x20008000)
T9704 000:487.057 - 0.003ms returns 0
T9704 000:487.061 JLINK_WriteReg(CFBP, 0x00000000)
T9704 000:487.065 - 0.003ms returns 0
T9704 000:487.069 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 000:487.074 - 0.005ms returns 0x00000003
T9704 000:487.079 JLINK_Go()
T9704 000:487.088   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:487.941   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:497.389 - 10.309ms 
T9704 000:497.404 JLINK_IsHalted()
T9704 000:506.097 - 8.692ms returns TRUE
T9704 000:506.115 JLINK_ReadReg(R15 (PC))
T9704 000:506.123 - 0.008ms returns 0x20000000
T9704 000:506.150 JLINK_ClrBPEx(BPHandle = 0x00000003)
T9704 000:506.157 - 0.006ms returns 0x00
T9704 000:506.161 JLINK_ReadReg(R0)
T9704 000:506.165 - 0.003ms returns 0x00000000
T9704 000:563.836 JLINK_WriteMem(0x20000000, 0x418 Bytes, ...)
T9704 000:563.847   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T9704 000:563.868   CPU_WriteMem(1048 bytes @ 0x20000000)
T9704 000:582.952 - 19.114ms returns 0x418
T9704 000:582.999 JLINK_HasError()
T9704 000:583.009 JLINK_WriteReg(R0, 0x08000000)
T9704 000:583.018 - 0.008ms returns 0
T9704 000:583.024 JLINK_WriteReg(R1, 0x017D7840)
T9704 000:583.030 - 0.005ms returns 0
T9704 000:583.036 JLINK_WriteReg(R2, 0x00000002)
T9704 000:583.041 - 0.005ms returns 0
T9704 000:583.047 JLINK_WriteReg(R3, 0x00000000)
T9704 000:583.052 - 0.005ms returns 0
T9704 000:583.058 JLINK_WriteReg(R4, 0x00000000)
T9704 000:583.063 - 0.005ms returns 0
T9704 000:583.069 JLINK_WriteReg(R5, 0x00000000)
T9704 000:583.074 - 0.005ms returns 0
T9704 000:583.080 JLINK_WriteReg(R6, 0x00000000)
T9704 000:583.085 - 0.005ms returns 0
T9704 000:583.091 JLINK_WriteReg(R7, 0x00000000)
T9704 000:583.096 - 0.005ms returns 0
T9704 000:583.102 JLINK_WriteReg(R8, 0x00000000)
T9704 000:583.107 - 0.005ms returns 0
T9704 000:583.112 JLINK_WriteReg(R9, 0x20000414)
T9704 000:583.118 - 0.005ms returns 0
T9704 000:583.124 JLINK_WriteReg(R10, 0x00000000)
T9704 000:583.129 - 0.005ms returns 0
T9704 000:583.135 JLINK_WriteReg(R11, 0x00000000)
T9704 000:583.140 - 0.005ms returns 0
T9704 000:583.145 JLINK_WriteReg(R12, 0x00000000)
T9704 000:583.150 - 0.005ms returns 0
T9704 000:583.156 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 000:583.162 - 0.006ms returns 0
T9704 000:583.168 JLINK_WriteReg(R14, 0x20000001)
T9704 000:583.173 - 0.005ms returns 0
T9704 000:583.179 JLINK_WriteReg(R15 (PC), 0x2000003E)
T9704 000:583.185 - 0.006ms returns 0
T9704 000:583.191 JLINK_WriteReg(XPSR, 0x01000000)
T9704 000:583.197 - 0.005ms returns 0
T9704 000:583.202 JLINK_WriteReg(MSP, 0x20008000)
T9704 000:583.208 - 0.005ms returns 0
T9704 000:583.214 JLINK_WriteReg(PSP, 0x20008000)
T9704 000:583.219 - 0.005ms returns 0
T9704 000:583.225 JLINK_WriteReg(CFBP, 0x00000000)
T9704 000:583.230 - 0.005ms returns 0
T9704 000:583.237 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 000:583.244 - 0.007ms returns 0x00000004
T9704 000:583.249 JLINK_Go()
T9704 000:583.260   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:583.999   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:593.367 - 10.116ms 
T9704 000:593.379 JLINK_IsHalted()
T9704 000:602.094 - 8.713ms returns TRUE
T9704 000:602.107 JLINK_ReadReg(R15 (PC))
T9704 000:602.113 - 0.005ms returns 0x20000000
T9704 000:602.117 JLINK_ClrBPEx(BPHandle = 0x00000004)
T9704 000:602.122 - 0.004ms returns 0x00
T9704 000:602.126 JLINK_ReadReg(R0)
T9704 000:602.130 - 0.003ms returns 0x00000000
T9704 000:602.420 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 000:602.429   Data:  18 20 00 24 E5 03 00 08 E7 5D 00 08 2B 5C 00 08 ...
T9704 000:602.450   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 000:620.561 - 18.140ms returns 0x3E8
T9704 000:620.576 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 000:620.580   Data:  0A 48 80 47 0A 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T9704 000:620.594   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 000:621.679 - 1.103ms returns 0x18
T9704 000:621.690 JLINK_HasError()
T9704 000:621.714 JLINK_WriteReg(R0, 0x08000000)
T9704 000:621.720 - 0.006ms returns 0
T9704 000:621.724 JLINK_WriteReg(R1, 0x00000400)
T9704 000:621.728 - 0.003ms returns 0
T9704 000:621.732 JLINK_WriteReg(R2, 0x20000418)
T9704 000:621.736 - 0.003ms returns 0
T9704 000:621.740 JLINK_WriteReg(R3, 0x00000000)
T9704 000:621.747 - 0.006ms returns 0
T9704 000:621.751 JLINK_WriteReg(R4, 0x00000000)
T9704 000:621.755 - 0.003ms returns 0
T9704 000:621.759 JLINK_WriteReg(R5, 0x00000000)
T9704 000:621.762 - 0.003ms returns 0
T9704 000:621.766 JLINK_WriteReg(R6, 0x00000000)
T9704 000:621.770 - 0.003ms returns 0
T9704 000:621.774 JLINK_WriteReg(R7, 0x00000000)
T9704 000:621.778 - 0.003ms returns 0
T9704 000:621.782 JLINK_WriteReg(R8, 0x00000000)
T9704 000:621.786 - 0.004ms returns 0
T9704 000:621.790 JLINK_WriteReg(R9, 0x20000414)
T9704 000:621.793 - 0.003ms returns 0
T9704 000:621.798 JLINK_WriteReg(R10, 0x00000000)
T9704 000:621.802 - 0.004ms returns 0
T9704 000:621.809 JLINK_WriteReg(R11, 0x00000000)
T9704 000:621.814 - 0.005ms returns 0
T9704 000:621.820 JLINK_WriteReg(R12, 0x00000000)
T9704 000:621.825 - 0.004ms returns 0
T9704 000:621.830 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 000:621.834 - 0.004ms returns 0
T9704 000:621.838 JLINK_WriteReg(R14, 0x20000001)
T9704 000:621.842 - 0.003ms returns 0
T9704 000:621.846 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 000:621.850 - 0.003ms returns 0
T9704 000:621.854 JLINK_WriteReg(XPSR, 0x01000000)
T9704 000:621.858 - 0.003ms returns 0
T9704 000:621.862 JLINK_WriteReg(MSP, 0x20008000)
T9704 000:621.865 - 0.003ms returns 0
T9704 000:621.869 JLINK_WriteReg(PSP, 0x20008000)
T9704 000:621.873 - 0.003ms returns 0
T9704 000:621.877 JLINK_WriteReg(CFBP, 0x00000000)
T9704 000:621.881 - 0.003ms returns 0
T9704 000:621.885 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 000:621.891 - 0.005ms returns 0x00000005
T9704 000:621.895 JLINK_Go()
T9704 000:621.902   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:622.699   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:631.979 - 10.083ms 
T9704 000:631.991 JLINK_IsHalted()
T9704 000:632.759 - 0.767ms returns FALSE
T9704 000:632.768 JLINK_HasError()
T9704 000:638.219 JLINK_IsHalted()
T9704 000:639.043 - 0.823ms returns FALSE
T9704 000:639.053 JLINK_HasError()
T9704 000:640.326 JLINK_IsHalted()
T9704 000:648.979 - 8.652ms returns TRUE
T9704 000:648.993 JLINK_ReadReg(R15 (PC))
T9704 000:648.999 - 0.006ms returns 0x20000000
T9704 000:649.023 JLINK_ClrBPEx(BPHandle = 0x00000005)
T9704 000:649.028 - 0.004ms returns 0x00
T9704 000:649.032 JLINK_ReadReg(R0)
T9704 000:649.036 - 0.003ms returns 0x00000000
T9704 000:649.405 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 000:649.413   Data:  FE E7 FE E7 05 48 06 49 06 4A 07 4B 70 47 00 00 ...
T9704 000:649.426   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 000:667.493 - 18.087ms returns 0x3E8
T9704 000:667.506 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 000:667.510   Data:  80 06 00 D5 EA 69 00 23 02 E0 01 23 05 E0 5B 1C ...
T9704 000:667.524   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 000:668.743 - 1.236ms returns 0x18
T9704 000:668.756 JLINK_HasError()
T9704 000:668.763 JLINK_WriteReg(R0, 0x08000400)
T9704 000:668.770 - 0.007ms returns 0
T9704 000:668.774 JLINK_WriteReg(R1, 0x00000400)
T9704 000:668.778 - 0.003ms returns 0
T9704 000:668.782 JLINK_WriteReg(R2, 0x20000418)
T9704 000:668.785 - 0.003ms returns 0
T9704 000:668.790 JLINK_WriteReg(R3, 0x00000000)
T9704 000:668.793 - 0.003ms returns 0
T9704 000:668.799 JLINK_WriteReg(R4, 0x00000000)
T9704 000:668.804 - 0.005ms returns 0
T9704 000:668.810 JLINK_WriteReg(R5, 0x00000000)
T9704 000:668.823 - 0.013ms returns 0
T9704 000:668.832 JLINK_WriteReg(R6, 0x00000000)
T9704 000:668.837 - 0.005ms returns 0
T9704 000:668.843 JLINK_WriteReg(R7, 0x00000000)
T9704 000:668.848 - 0.005ms returns 0
T9704 000:668.853 JLINK_WriteReg(R8, 0x00000000)
T9704 000:668.857 - 0.003ms returns 0
T9704 000:668.861 JLINK_WriteReg(R9, 0x20000414)
T9704 000:668.865 - 0.003ms returns 0
T9704 000:668.869 JLINK_WriteReg(R10, 0x00000000)
T9704 000:668.873 - 0.003ms returns 0
T9704 000:668.877 JLINK_WriteReg(R11, 0x00000000)
T9704 000:668.881 - 0.003ms returns 0
T9704 000:668.885 JLINK_WriteReg(R12, 0x00000000)
T9704 000:668.888 - 0.003ms returns 0
T9704 000:668.892 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 000:668.897 - 0.004ms returns 0
T9704 000:668.901 JLINK_WriteReg(R14, 0x20000001)
T9704 000:668.904 - 0.003ms returns 0
T9704 000:668.909 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 000:668.912 - 0.003ms returns 0
T9704 000:668.916 JLINK_WriteReg(XPSR, 0x01000000)
T9704 000:668.920 - 0.003ms returns 0
T9704 000:668.924 JLINK_WriteReg(MSP, 0x20008000)
T9704 000:668.928 - 0.003ms returns 0
T9704 000:668.932 JLINK_WriteReg(PSP, 0x20008000)
T9704 000:668.936 - 0.004ms returns 0
T9704 000:668.942 JLINK_WriteReg(CFBP, 0x00000000)
T9704 000:668.947 - 0.005ms returns 0
T9704 000:668.953 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 000:668.960 - 0.007ms returns 0x00000006
T9704 000:668.966 JLINK_Go()
T9704 000:668.978   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:669.731   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:678.818 - 9.850ms 
T9704 000:678.840 JLINK_IsHalted()
T9704 000:679.622 - 0.780ms returns FALSE
T9704 000:679.635 JLINK_HasError()
T9704 000:682.590 JLINK_IsHalted()
T9704 000:683.367 - 0.776ms returns FALSE
T9704 000:683.379 JLINK_HasError()
T9704 000:684.808 JLINK_IsHalted()
T9704 000:685.659 - 0.850ms returns FALSE
T9704 000:685.674 JLINK_HasError()
T9704 000:687.098 JLINK_IsHalted()
T9704 000:695.749 - 8.650ms returns TRUE
T9704 000:695.764 JLINK_ReadReg(R15 (PC))
T9704 000:695.773 - 0.009ms returns 0x20000000
T9704 000:695.780 JLINK_ClrBPEx(BPHandle = 0x00000006)
T9704 000:695.786 - 0.006ms returns 0x00
T9704 000:695.791 JLINK_ReadReg(R0)
T9704 000:695.795 - 0.004ms returns 0x00000000
T9704 000:696.209 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 000:696.221   Data:  F9 D1 A8 69 E6 18 C0 1A A8 61 28 6A 18 44 28 62 ...
T9704 000:696.237   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 000:714.285 - 18.075ms returns 0x3E8
T9704 000:714.299 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 000:714.303   Data:  18 D1 E0 69 00 28 20 DA 26 F0 20 06 1D E0 D8 F8 ...
T9704 000:714.316   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 000:715.501 - 1.202ms returns 0x18
T9704 000:715.512 JLINK_HasError()
T9704 000:715.534 JLINK_WriteReg(R0, 0x08000800)
T9704 000:715.540 - 0.005ms returns 0
T9704 000:715.544 JLINK_WriteReg(R1, 0x00000400)
T9704 000:715.548 - 0.003ms returns 0
T9704 000:715.552 JLINK_WriteReg(R2, 0x20000418)
T9704 000:715.556 - 0.003ms returns 0
T9704 000:715.560 JLINK_WriteReg(R3, 0x00000000)
T9704 000:715.563 - 0.003ms returns 0
T9704 000:715.567 JLINK_WriteReg(R4, 0x00000000)
T9704 000:715.571 - 0.003ms returns 0
T9704 000:715.575 JLINK_WriteReg(R5, 0x00000000)
T9704 000:715.579 - 0.003ms returns 0
T9704 000:715.583 JLINK_WriteReg(R6, 0x00000000)
T9704 000:715.586 - 0.003ms returns 0
T9704 000:715.590 JLINK_WriteReg(R7, 0x00000000)
T9704 000:715.594 - 0.003ms returns 0
T9704 000:715.598 JLINK_WriteReg(R8, 0x00000000)
T9704 000:715.602 - 0.004ms returns 0
T9704 000:715.606 JLINK_WriteReg(R9, 0x20000414)
T9704 000:715.610 - 0.003ms returns 0
T9704 000:715.614 JLINK_WriteReg(R10, 0x00000000)
T9704 000:715.617 - 0.003ms returns 0
T9704 000:715.621 JLINK_WriteReg(R11, 0x00000000)
T9704 000:715.625 - 0.003ms returns 0
T9704 000:715.629 JLINK_WriteReg(R12, 0x00000000)
T9704 000:715.633 - 0.003ms returns 0
T9704 000:715.637 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 000:715.641 - 0.004ms returns 0
T9704 000:715.679 JLINK_WriteReg(R14, 0x20000001)
T9704 000:715.688 - 0.007ms returns 0
T9704 000:715.693 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 000:715.697 - 0.004ms returns 0
T9704 000:715.701 JLINK_WriteReg(XPSR, 0x01000000)
T9704 000:715.705 - 0.003ms returns 0
T9704 000:715.709 JLINK_WriteReg(MSP, 0x20008000)
T9704 000:715.713 - 0.003ms returns 0
T9704 000:715.717 JLINK_WriteReg(PSP, 0x20008000)
T9704 000:715.720 - 0.003ms returns 0
T9704 000:715.724 JLINK_WriteReg(CFBP, 0x00000000)
T9704 000:715.728 - 0.003ms returns 0
T9704 000:715.733 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 000:715.738 - 0.005ms returns 0x00000007
T9704 000:715.742 JLINK_Go()
T9704 000:715.749   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:716.506   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:725.804 - 10.060ms 
T9704 000:725.821 JLINK_IsHalted()
T9704 000:726.765 - 0.942ms returns FALSE
T9704 000:726.776 JLINK_HasError()
T9704 000:730.104 JLINK_IsHalted()
T9704 000:730.979 - 0.875ms returns FALSE
T9704 000:730.990 JLINK_HasError()
T9704 000:732.198 JLINK_IsHalted()
T9704 000:732.989 - 0.789ms returns FALSE
T9704 000:733.000 JLINK_HasError()
T9704 000:734.332 JLINK_IsHalted()
T9704 000:743.033 - 8.699ms returns TRUE
T9704 000:743.052 JLINK_ReadReg(R15 (PC))
T9704 000:743.061 - 0.009ms returns 0x20000000
T9704 000:743.088 JLINK_ClrBPEx(BPHandle = 0x00000007)
T9704 000:743.095 - 0.006ms returns 0x00
T9704 000:743.099 JLINK_ReadReg(R0)
T9704 000:743.103 - 0.003ms returns 0x00000000
T9704 000:743.486 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 000:743.494   Data:  40 00 30 38 C8 F8 18 00 20 46 E1 68 88 47 05 46 ...
T9704 000:743.508   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 000:761.697 - 18.209ms returns 0x3E8
T9704 000:761.710 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 000:761.715   Data:  20 78 30 28 01 D1 00 22 7F 1E 00 2A 09 97 84 D0 ...
T9704 000:761.729   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 000:762.809 - 1.098ms returns 0x18
T9704 000:762.819 JLINK_HasError()
T9704 000:762.825 JLINK_WriteReg(R0, 0x08000C00)
T9704 000:762.831 - 0.006ms returns 0
T9704 000:762.835 JLINK_WriteReg(R1, 0x00000400)
T9704 000:762.839 - 0.003ms returns 0
T9704 000:762.843 JLINK_WriteReg(R2, 0x20000418)
T9704 000:762.847 - 0.003ms returns 0
T9704 000:762.851 JLINK_WriteReg(R3, 0x00000000)
T9704 000:762.855 - 0.003ms returns 0
T9704 000:762.859 JLINK_WriteReg(R4, 0x00000000)
T9704 000:762.862 - 0.003ms returns 0
T9704 000:762.866 JLINK_WriteReg(R5, 0x00000000)
T9704 000:762.870 - 0.003ms returns 0
T9704 000:762.874 JLINK_WriteReg(R6, 0x00000000)
T9704 000:762.877 - 0.003ms returns 0
T9704 000:762.881 JLINK_WriteReg(R7, 0x00000000)
T9704 000:762.885 - 0.003ms returns 0
T9704 000:762.889 JLINK_WriteReg(R8, 0x00000000)
T9704 000:762.893 - 0.003ms returns 0
T9704 000:762.897 JLINK_WriteReg(R9, 0x20000414)
T9704 000:762.901 - 0.003ms returns 0
T9704 000:762.905 JLINK_WriteReg(R10, 0x00000000)
T9704 000:762.908 - 0.003ms returns 0
T9704 000:762.912 JLINK_WriteReg(R11, 0x00000000)
T9704 000:762.916 - 0.003ms returns 0
T9704 000:762.920 JLINK_WriteReg(R12, 0x00000000)
T9704 000:762.924 - 0.003ms returns 0
T9704 000:762.928 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 000:762.932 - 0.004ms returns 0
T9704 000:762.936 JLINK_WriteReg(R14, 0x20000001)
T9704 000:762.940 - 0.003ms returns 0
T9704 000:762.944 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 000:762.947 - 0.003ms returns 0
T9704 000:762.951 JLINK_WriteReg(XPSR, 0x01000000)
T9704 000:762.955 - 0.003ms returns 0
T9704 000:762.960 JLINK_WriteReg(MSP, 0x20008000)
T9704 000:762.963 - 0.003ms returns 0
T9704 000:762.967 JLINK_WriteReg(PSP, 0x20008000)
T9704 000:762.971 - 0.003ms returns 0
T9704 000:762.975 JLINK_WriteReg(CFBP, 0x00000000)
T9704 000:762.979 - 0.003ms returns 0
T9704 000:762.983 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 000:762.988 - 0.005ms returns 0x00000008
T9704 000:762.992 JLINK_Go()
T9704 000:762.999   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:763.832   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:773.186 - 10.192ms 
T9704 000:773.200 JLINK_IsHalted()
T9704 000:774.003 - 0.802ms returns FALSE
T9704 000:774.013 JLINK_HasError()
T9704 000:777.846 JLINK_IsHalted()
T9704 000:778.719 - 0.872ms returns FALSE
T9704 000:778.728 JLINK_HasError()
T9704 000:779.933 JLINK_IsHalted()
T9704 000:780.691 - 0.757ms returns FALSE
T9704 000:780.698 JLINK_HasError()
T9704 000:782.031 JLINK_IsHalted()
T9704 000:790.481 - 8.449ms returns TRUE
T9704 000:790.495 JLINK_ReadReg(R15 (PC))
T9704 000:790.505 - 0.009ms returns 0x20000000
T9704 000:790.511 JLINK_ClrBPEx(BPHandle = 0x00000008)
T9704 000:790.517 - 0.005ms returns 0x00
T9704 000:790.522 JLINK_ReadReg(R0)
T9704 000:790.527 - 0.004ms returns 0x00000000
T9704 000:790.912 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 000:790.921   Data:  B1 FE 30 32 62 55 6D 1C 50 EA 01 02 F5 D1 08 43 ...
T9704 000:790.934   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 000:808.950 - 18.037ms returns 0x3E8
T9704 000:808.971 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 000:808.977   Data:  02 DC 4F F0 30 01 01 E0 4F F0 31 01 4F F0 01 08 ...
T9704 000:808.993   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 000:810.105 - 1.133ms returns 0x18
T9704 000:810.118 JLINK_HasError()
T9704 000:810.141 JLINK_WriteReg(R0, 0x08001000)
T9704 000:810.148 - 0.006ms returns 0
T9704 000:810.152 JLINK_WriteReg(R1, 0x00000400)
T9704 000:810.156 - 0.003ms returns 0
T9704 000:810.160 JLINK_WriteReg(R2, 0x20000418)
T9704 000:810.164 - 0.003ms returns 0
T9704 000:810.168 JLINK_WriteReg(R3, 0x00000000)
T9704 000:810.171 - 0.004ms returns 0
T9704 000:810.176 JLINK_WriteReg(R4, 0x00000000)
T9704 000:810.179 - 0.003ms returns 0
T9704 000:810.183 JLINK_WriteReg(R5, 0x00000000)
T9704 000:810.187 - 0.003ms returns 0
T9704 000:810.195 JLINK_WriteReg(R6, 0x00000000)
T9704 000:810.199 - 0.003ms returns 0
T9704 000:810.203 JLINK_WriteReg(R7, 0x00000000)
T9704 000:810.207 - 0.003ms returns 0
T9704 000:810.211 JLINK_WriteReg(R8, 0x00000000)
T9704 000:810.214 - 0.003ms returns 0
T9704 000:810.218 JLINK_WriteReg(R9, 0x20000414)
T9704 000:810.222 - 0.003ms returns 0
T9704 000:810.226 JLINK_WriteReg(R10, 0x00000000)
T9704 000:810.230 - 0.003ms returns 0
T9704 000:810.234 JLINK_WriteReg(R11, 0x00000000)
T9704 000:810.237 - 0.003ms returns 0
T9704 000:810.241 JLINK_WriteReg(R12, 0x00000000)
T9704 000:810.245 - 0.003ms returns 0
T9704 000:810.249 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 000:810.253 - 0.004ms returns 0
T9704 000:810.257 JLINK_WriteReg(R14, 0x20000001)
T9704 000:810.261 - 0.003ms returns 0
T9704 000:810.265 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 000:810.269 - 0.003ms returns 0
T9704 000:810.273 JLINK_WriteReg(XPSR, 0x01000000)
T9704 000:810.276 - 0.003ms returns 0
T9704 000:810.280 JLINK_WriteReg(MSP, 0x20008000)
T9704 000:810.284 - 0.003ms returns 0
T9704 000:810.288 JLINK_WriteReg(PSP, 0x20008000)
T9704 000:810.292 - 0.003ms returns 0
T9704 000:810.296 JLINK_WriteReg(CFBP, 0x00000000)
T9704 000:810.299 - 0.003ms returns 0
T9704 000:810.304 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 000:810.309 - 0.005ms returns 0x00000009
T9704 000:810.313 JLINK_Go()
T9704 000:810.321   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:811.120   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:820.391 - 10.077ms 
T9704 000:820.407 JLINK_IsHalted()
T9704 000:821.205 - 0.797ms returns FALSE
T9704 000:821.217 JLINK_HasError()
T9704 000:824.816 JLINK_IsHalted()
T9704 000:825.611 - 0.794ms returns FALSE
T9704 000:825.624 JLINK_HasError()
T9704 000:826.952 JLINK_IsHalted()
T9704 000:827.729 - 0.776ms returns FALSE
T9704 000:827.742 JLINK_HasError()
T9704 000:829.063 JLINK_IsHalted()
T9704 000:837.604 - 8.538ms returns TRUE
T9704 000:837.624 JLINK_ReadReg(R15 (PC))
T9704 000:837.634 - 0.010ms returns 0x20000000
T9704 000:837.667 JLINK_ClrBPEx(BPHandle = 0x00000009)
T9704 000:837.674 - 0.007ms returns 0x00
T9704 000:837.680 JLINK_ReadReg(R0)
T9704 000:837.685 - 0.004ms returns 0x00000000
T9704 000:838.069 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 000:838.080   Data:  FF 36 03 E0 59 EA 47 01 00 D0 6B 4E 00 2D 02 DC ...
T9704 000:838.094   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 000:856.286 - 18.216ms returns 0x3E8
T9704 000:856.298 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 000:856.303   Data:  F3 FA 03 AB 83 E8 07 00 6D 10 64 1C 00 2D E3 D1 ...
T9704 000:856.316   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 000:857.427 - 1.128ms returns 0x18
T9704 000:857.437 JLINK_HasError()
T9704 000:857.442 JLINK_WriteReg(R0, 0x08001400)
T9704 000:857.448 - 0.005ms returns 0
T9704 000:857.452 JLINK_WriteReg(R1, 0x00000400)
T9704 000:857.456 - 0.004ms returns 0
T9704 000:857.461 JLINK_WriteReg(R2, 0x20000418)
T9704 000:857.465 - 0.003ms returns 0
T9704 000:857.469 JLINK_WriteReg(R3, 0x00000000)
T9704 000:857.472 - 0.003ms returns 0
T9704 000:857.476 JLINK_WriteReg(R4, 0x00000000)
T9704 000:857.480 - 0.003ms returns 0
T9704 000:857.484 JLINK_WriteReg(R5, 0x00000000)
T9704 000:857.488 - 0.004ms returns 0
T9704 000:857.493 JLINK_WriteReg(R6, 0x00000000)
T9704 000:857.499 - 0.005ms returns 0
T9704 000:857.505 JLINK_WriteReg(R7, 0x00000000)
T9704 000:857.511 - 0.005ms returns 0
T9704 000:857.517 JLINK_WriteReg(R8, 0x00000000)
T9704 000:857.521 - 0.004ms returns 0
T9704 000:857.525 JLINK_WriteReg(R9, 0x20000414)
T9704 000:857.528 - 0.003ms returns 0
T9704 000:857.533 JLINK_WriteReg(R10, 0x00000000)
T9704 000:857.537 - 0.003ms returns 0
T9704 000:857.541 JLINK_WriteReg(R11, 0x00000000)
T9704 000:857.545 - 0.003ms returns 0
T9704 000:857.549 JLINK_WriteReg(R12, 0x00000000)
T9704 000:857.552 - 0.003ms returns 0
T9704 000:857.556 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 000:857.561 - 0.004ms returns 0
T9704 000:857.565 JLINK_WriteReg(R14, 0x20000001)
T9704 000:857.568 - 0.003ms returns 0
T9704 000:857.573 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 000:857.576 - 0.003ms returns 0
T9704 000:857.580 JLINK_WriteReg(XPSR, 0x01000000)
T9704 000:857.584 - 0.003ms returns 0
T9704 000:857.588 JLINK_WriteReg(MSP, 0x20008000)
T9704 000:857.592 - 0.003ms returns 0
T9704 000:857.596 JLINK_WriteReg(PSP, 0x20008000)
T9704 000:857.599 - 0.003ms returns 0
T9704 000:857.603 JLINK_WriteReg(CFBP, 0x00000000)
T9704 000:857.607 - 0.004ms returns 0
T9704 000:857.612 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 000:857.617 - 0.005ms returns 0x0000000A
T9704 000:857.622 JLINK_Go()
T9704 000:857.628   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:858.498   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:867.966 - 10.343ms 
T9704 000:867.982 JLINK_IsHalted()
T9704 000:868.856 - 0.872ms returns FALSE
T9704 000:868.873 JLINK_HasError()
T9704 000:872.422 JLINK_IsHalted()
T9704 000:873.272 - 0.850ms returns FALSE
T9704 000:873.282 JLINK_HasError()
T9704 000:874.527 JLINK_IsHalted()
T9704 000:875.474 - 0.947ms returns FALSE
T9704 000:875.482 JLINK_HasError()
T9704 000:876.622 JLINK_IsHalted()
T9704 000:885.252 - 8.628ms returns TRUE
T9704 000:885.273 JLINK_ReadReg(R15 (PC))
T9704 000:885.283 - 0.009ms returns 0x20000000
T9704 000:885.289 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T9704 000:885.296 - 0.006ms returns 0x00
T9704 000:885.302 JLINK_ReadReg(R0)
T9704 000:885.307 - 0.005ms returns 0x00000000
T9704 000:885.995 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 000:886.010   Data:  B8 F1 00 0F 03 A8 02 D0 00 F0 CD FA 01 E0 00 F0 ...
T9704 000:886.030   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 000:904.134 - 18.139ms returns 0x3E8
T9704 000:904.149 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 000:904.154   Data:  62 46 39 46 4E EB 0E 0E 4F F0 00 0B 00 18 52 41 ...
T9704 000:904.168   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 000:905.287 - 1.137ms returns 0x18
T9704 000:905.297 JLINK_HasError()
T9704 000:905.322 JLINK_WriteReg(R0, 0x08001800)
T9704 000:905.328 - 0.005ms returns 0
T9704 000:905.332 JLINK_WriteReg(R1, 0x00000400)
T9704 000:905.339 - 0.006ms returns 0
T9704 000:905.343 JLINK_WriteReg(R2, 0x20000418)
T9704 000:905.347 - 0.003ms returns 0
T9704 000:905.351 JLINK_WriteReg(R3, 0x00000000)
T9704 000:905.358 - 0.006ms returns 0
T9704 000:905.362 JLINK_WriteReg(R4, 0x00000000)
T9704 000:905.366 - 0.003ms returns 0
T9704 000:905.370 JLINK_WriteReg(R5, 0x00000000)
T9704 000:905.373 - 0.003ms returns 0
T9704 000:905.377 JLINK_WriteReg(R6, 0x00000000)
T9704 000:905.381 - 0.003ms returns 0
T9704 000:905.385 JLINK_WriteReg(R7, 0x00000000)
T9704 000:905.388 - 0.003ms returns 0
T9704 000:905.393 JLINK_WriteReg(R8, 0x00000000)
T9704 000:905.396 - 0.003ms returns 0
T9704 000:905.400 JLINK_WriteReg(R9, 0x20000414)
T9704 000:905.404 - 0.003ms returns 0
T9704 000:905.408 JLINK_WriteReg(R10, 0x00000000)
T9704 000:905.411 - 0.003ms returns 0
T9704 000:905.416 JLINK_WriteReg(R11, 0x00000000)
T9704 000:905.419 - 0.003ms returns 0
T9704 000:905.423 JLINK_WriteReg(R12, 0x00000000)
T9704 000:905.427 - 0.003ms returns 0
T9704 000:905.431 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 000:905.435 - 0.004ms returns 0
T9704 000:905.439 JLINK_WriteReg(R14, 0x20000001)
T9704 000:905.443 - 0.003ms returns 0
T9704 000:905.447 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 000:905.451 - 0.003ms returns 0
T9704 000:905.455 JLINK_WriteReg(XPSR, 0x01000000)
T9704 000:905.459 - 0.003ms returns 0
T9704 000:905.463 JLINK_WriteReg(MSP, 0x20008000)
T9704 000:905.466 - 0.003ms returns 0
T9704 000:905.470 JLINK_WriteReg(PSP, 0x20008000)
T9704 000:905.474 - 0.003ms returns 0
T9704 000:905.478 JLINK_WriteReg(CFBP, 0x00000000)
T9704 000:905.482 - 0.003ms returns 0
T9704 000:905.486 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 000:905.491 - 0.005ms returns 0x0000000B
T9704 000:905.495 JLINK_Go()
T9704 000:905.502   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:906.271   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:915.620 - 10.123ms 
T9704 000:915.633 JLINK_IsHalted()
T9704 000:916.580 - 0.946ms returns FALSE
T9704 000:916.594 JLINK_HasError()
T9704 000:919.962 JLINK_IsHalted()
T9704 000:920.866 - 0.903ms returns FALSE
T9704 000:920.877 JLINK_HasError()
T9704 000:922.074 JLINK_IsHalted()
T9704 000:922.983 - 0.909ms returns FALSE
T9704 000:922.993 JLINK_HasError()
T9704 000:924.201 JLINK_IsHalted()
T9704 000:932.822 - 8.620ms returns TRUE
T9704 000:932.841 JLINK_ReadReg(R15 (PC))
T9704 000:932.851 - 0.009ms returns 0x20000000
T9704 000:932.882 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T9704 000:932.889 - 0.007ms returns 0x00
T9704 000:932.895 JLINK_ReadReg(R0)
T9704 000:932.901 - 0.005ms returns 0x00000000
T9704 000:933.319 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 000:933.331   Data:  08 0C 71 EB 03 07 7B F1 00 0B 24 BF 62 46 39 46 ...
T9704 000:933.347   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 000:951.394 - 18.073ms returns 0x3E8
T9704 000:951.413 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 000:951.421   Data:  48 BF 70 47 B6 19 52 41 41 EB 01 01 A3 F1 01 03 ...
T9704 000:951.442   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 000:952.632 - 1.218ms returns 0x18
T9704 000:952.646 JLINK_HasError()
T9704 000:952.653 JLINK_WriteReg(R0, 0x08001C00)
T9704 000:952.660 - 0.007ms returns 0
T9704 000:952.665 JLINK_WriteReg(R1, 0x00000400)
T9704 000:952.669 - 0.003ms returns 0
T9704 000:952.673 JLINK_WriteReg(R2, 0x20000418)
T9704 000:952.676 - 0.003ms returns 0
T9704 000:952.680 JLINK_WriteReg(R3, 0x00000000)
T9704 000:952.684 - 0.003ms returns 0
T9704 000:952.688 JLINK_WriteReg(R4, 0x00000000)
T9704 000:952.691 - 0.003ms returns 0
T9704 000:952.695 JLINK_WriteReg(R5, 0x00000000)
T9704 000:952.699 - 0.003ms returns 0
T9704 000:952.703 JLINK_WriteReg(R6, 0x00000000)
T9704 000:952.707 - 0.003ms returns 0
T9704 000:952.711 JLINK_WriteReg(R7, 0x00000000)
T9704 000:952.714 - 0.003ms returns 0
T9704 000:952.718 JLINK_WriteReg(R8, 0x00000000)
T9704 000:952.724 - 0.005ms returns 0
T9704 000:952.728 JLINK_WriteReg(R9, 0x20000414)
T9704 000:952.732 - 0.003ms returns 0
T9704 000:952.736 JLINK_WriteReg(R10, 0x00000000)
T9704 000:952.740 - 0.003ms returns 0
T9704 000:952.744 JLINK_WriteReg(R11, 0x00000000)
T9704 000:952.747 - 0.003ms returns 0
T9704 000:952.754 JLINK_WriteReg(R12, 0x00000000)
T9704 000:952.759 - 0.004ms returns 0
T9704 000:952.763 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 000:952.767 - 0.004ms returns 0
T9704 000:952.771 JLINK_WriteReg(R14, 0x20000001)
T9704 000:952.775 - 0.003ms returns 0
T9704 000:952.780 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 000:952.785 - 0.005ms returns 0
T9704 000:952.791 JLINK_WriteReg(XPSR, 0x01000000)
T9704 000:952.796 - 0.005ms returns 0
T9704 000:952.800 JLINK_WriteReg(MSP, 0x20008000)
T9704 000:952.804 - 0.003ms returns 0
T9704 000:952.808 JLINK_WriteReg(PSP, 0x20008000)
T9704 000:952.811 - 0.003ms returns 0
T9704 000:952.815 JLINK_WriteReg(CFBP, 0x00000000)
T9704 000:952.819 - 0.003ms returns 0
T9704 000:952.824 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 000:952.829 - 0.005ms returns 0x0000000C
T9704 000:952.848 JLINK_Go()
T9704 000:952.859   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 000:953.614   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 000:963.123 - 10.274ms 
T9704 000:963.138 JLINK_IsHalted()
T9704 000:963.968 - 0.829ms returns FALSE
T9704 000:963.979 JLINK_HasError()
T9704 000:967.497 JLINK_IsHalted()
T9704 000:968.379 - 0.881ms returns FALSE
T9704 000:968.392 JLINK_HasError()
T9704 000:969.454 JLINK_IsHalted()
T9704 000:970.367 - 0.912ms returns FALSE
T9704 000:970.377 JLINK_HasError()
T9704 000:971.604 JLINK_IsHalted()
T9704 000:980.189 - 8.584ms returns TRUE
T9704 000:980.203 JLINK_ReadReg(R15 (PC))
T9704 000:980.209 - 0.006ms returns 0x20000000
T9704 000:980.214 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T9704 000:980.218 - 0.004ms returns 0x00
T9704 000:980.223 JLINK_ReadReg(R0)
T9704 000:980.227 - 0.003ms returns 0x00000000
T9704 000:980.617 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 000:980.625   Data:  11 47 24 EA 05 46 21 EA 07 4C 05 FB 07 F1 06 FB ...
T9704 000:980.640   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 000:998.743 - 18.125ms returns 0x3E8
T9704 000:998.757 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 000:998.762   Data:  04 68 85 6D 0A F1 30 0A DF F8 EC B1 4C 45 DF F8 ...
T9704 000:998.775   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 000:999.931 - 1.172ms returns 0x18
T9704 000:999.942 JLINK_HasError()
T9704 000:999.966 JLINK_WriteReg(R0, 0x08002000)
T9704 000:999.972 - 0.006ms returns 0
T9704 000:999.976 JLINK_WriteReg(R1, 0x00000400)
T9704 000:999.980 - 0.003ms returns 0
T9704 000:999.984 JLINK_WriteReg(R2, 0x20000418)
T9704 000:999.988 - 0.003ms returns 0
T9704 000:999.992 JLINK_WriteReg(R3, 0x00000000)
T9704 000:999.995 - 0.003ms returns 0
T9704 000:999.999 JLINK_WriteReg(R4, 0x00000000)
T9704 001:000.003 - 0.003ms returns 0
T9704 001:000.007 JLINK_WriteReg(R5, 0x00000000)
T9704 001:000.011 - 0.003ms returns 0
T9704 001:000.015 JLINK_WriteReg(R6, 0x00000000)
T9704 001:000.018 - 0.003ms returns 0
T9704 001:000.022 JLINK_WriteReg(R7, 0x00000000)
T9704 001:000.026 - 0.003ms returns 0
T9704 001:000.030 JLINK_WriteReg(R8, 0x00000000)
T9704 001:000.033 - 0.003ms returns 0
T9704 001:000.037 JLINK_WriteReg(R9, 0x20000414)
T9704 001:000.041 - 0.003ms returns 0
T9704 001:000.045 JLINK_WriteReg(R10, 0x00000000)
T9704 001:000.049 - 0.003ms returns 0
T9704 001:000.053 JLINK_WriteReg(R11, 0x00000000)
T9704 001:000.057 - 0.003ms returns 0
T9704 001:000.060 JLINK_WriteReg(R12, 0x00000000)
T9704 001:000.064 - 0.003ms returns 0
T9704 001:000.068 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:000.072 - 0.004ms returns 0
T9704 001:000.076 JLINK_WriteReg(R14, 0x20000001)
T9704 001:000.080 - 0.003ms returns 0
T9704 001:000.084 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:000.088 - 0.004ms returns 0
T9704 001:000.092 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:000.096 - 0.003ms returns 0
T9704 001:000.100 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:000.104 - 0.003ms returns 0
T9704 001:000.108 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:000.114 - 0.006ms returns 0
T9704 001:000.118 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:000.122 - 0.003ms returns 0
T9704 001:000.127 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:000.134 - 0.007ms returns 0x0000000D
T9704 001:000.139 JLINK_Go()
T9704 001:000.146   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:000.998   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:010.279 - 10.139ms 
T9704 001:010.292 JLINK_IsHalted()
T9704 001:011.156 - 0.863ms returns FALSE
T9704 001:011.164 JLINK_HasError()
T9704 001:013.898 JLINK_IsHalted()
T9704 001:014.682 - 0.783ms returns FALSE
T9704 001:014.692 JLINK_HasError()
T9704 001:015.893 JLINK_IsHalted()
T9704 001:016.686 - 0.792ms returns FALSE
T9704 001:016.697 JLINK_HasError()
T9704 001:018.022 JLINK_IsHalted()
T9704 001:026.731 - 8.708ms returns TRUE
T9704 001:026.747 JLINK_ReadReg(R15 (PC))
T9704 001:026.755 - 0.008ms returns 0x20000000
T9704 001:026.782 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T9704 001:026.788 - 0.006ms returns 0x00
T9704 001:027.246 JLINK_ReadReg(R0)
T9704 001:027.257 - 0.010ms returns 0x00000000
T9704 001:027.682 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:027.693   Data:  B4 42 6B D0 54 45 69 D0 BC 42 67 D0 DF F8 D0 81 ...
T9704 001:027.711   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:045.808 - 18.126ms returns 0x3E8
T9704 001:045.821 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:045.825   Data:  48 46 84 F8 50 60 E7 E7 20 68 41 46 88 42 01 D0 ...
T9704 001:045.838   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:046.932 - 1.111ms returns 0x18
T9704 001:046.943 JLINK_HasError()
T9704 001:046.949 JLINK_WriteReg(R0, 0x08002400)
T9704 001:046.954 - 0.005ms returns 0
T9704 001:046.959 JLINK_WriteReg(R1, 0x00000400)
T9704 001:046.963 - 0.004ms returns 0
T9704 001:046.967 JLINK_WriteReg(R2, 0x20000418)
T9704 001:046.970 - 0.003ms returns 0
T9704 001:046.974 JLINK_WriteReg(R3, 0x00000000)
T9704 001:046.978 - 0.003ms returns 0
T9704 001:046.982 JLINK_WriteReg(R4, 0x00000000)
T9704 001:046.986 - 0.003ms returns 0
T9704 001:046.990 JLINK_WriteReg(R5, 0x00000000)
T9704 001:046.993 - 0.003ms returns 0
T9704 001:046.997 JLINK_WriteReg(R6, 0x00000000)
T9704 001:047.001 - 0.003ms returns 0
T9704 001:047.005 JLINK_WriteReg(R7, 0x00000000)
T9704 001:047.009 - 0.003ms returns 0
T9704 001:047.013 JLINK_WriteReg(R8, 0x00000000)
T9704 001:047.017 - 0.004ms returns 0
T9704 001:047.021 JLINK_WriteReg(R9, 0x20000414)
T9704 001:047.024 - 0.003ms returns 0
T9704 001:047.028 JLINK_WriteReg(R10, 0x00000000)
T9704 001:047.032 - 0.003ms returns 0
T9704 001:047.036 JLINK_WriteReg(R11, 0x00000000)
T9704 001:047.040 - 0.003ms returns 0
T9704 001:047.044 JLINK_WriteReg(R12, 0x00000000)
T9704 001:047.048 - 0.003ms returns 0
T9704 001:047.052 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:047.057 - 0.004ms returns 0
T9704 001:047.061 JLINK_WriteReg(R14, 0x20000001)
T9704 001:047.065 - 0.003ms returns 0
T9704 001:047.069 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:047.073 - 0.004ms returns 0
T9704 001:047.077 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:047.081 - 0.003ms returns 0
T9704 001:047.085 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:047.089 - 0.003ms returns 0
T9704 001:047.093 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:047.096 - 0.003ms returns 0
T9704 001:047.100 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:047.104 - 0.003ms returns 0
T9704 001:047.109 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:047.114 - 0.005ms returns 0x0000000E
T9704 001:047.119 JLINK_Go()
T9704 001:047.125   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:047.971   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:057.269 - 10.149ms 
T9704 001:057.283 JLINK_IsHalted()
T9704 001:058.112 - 0.828ms returns FALSE
T9704 001:058.123 JLINK_HasError()
T9704 001:061.219 JLINK_IsHalted()
T9704 001:062.042 - 0.821ms returns FALSE
T9704 001:062.053 JLINK_HasError()
T9704 001:063.298 JLINK_IsHalted()
T9704 001:064.040 - 0.742ms returns FALSE
T9704 001:064.048 JLINK_HasError()
T9704 001:065.394 JLINK_IsHalted()
T9704 001:073.922 - 8.527ms returns TRUE
T9704 001:073.940 JLINK_ReadReg(R15 (PC))
T9704 001:073.950 - 0.009ms returns 0x20000000
T9704 001:073.956 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T9704 001:073.966 - 0.010ms returns 0x00
T9704 001:073.973 JLINK_ReadReg(R0)
T9704 001:073.978 - 0.005ms returns 0x00000000
T9704 001:074.473 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:074.485   Data:  26 48 40 F6 1F 72 D7 F8 00 C0 25 4B BC F1 00 0F ...
T9704 001:074.504   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:092.497 - 18.023ms returns 0x3E8
T9704 001:092.513 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:092.518   Data:  42 F4 00 00 23 F0 E0 72 02 43 8A 60 30 49 2F 48 ...
T9704 001:092.536   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:093.730 - 1.217ms returns 0x18
T9704 001:093.742 JLINK_HasError()
T9704 001:093.770 JLINK_WriteReg(R0, 0x08002800)
T9704 001:093.776 - 0.006ms returns 0
T9704 001:093.780 JLINK_WriteReg(R1, 0x00000400)
T9704 001:093.784 - 0.003ms returns 0
T9704 001:093.788 JLINK_WriteReg(R2, 0x20000418)
T9704 001:093.792 - 0.003ms returns 0
T9704 001:093.796 JLINK_WriteReg(R3, 0x00000000)
T9704 001:093.799 - 0.003ms returns 0
T9704 001:093.803 JLINK_WriteReg(R4, 0x00000000)
T9704 001:093.807 - 0.003ms returns 0
T9704 001:093.811 JLINK_WriteReg(R5, 0x00000000)
T9704 001:093.814 - 0.003ms returns 0
T9704 001:093.819 JLINK_WriteReg(R6, 0x00000000)
T9704 001:093.822 - 0.003ms returns 0
T9704 001:093.826 JLINK_WriteReg(R7, 0x00000000)
T9704 001:093.830 - 0.003ms returns 0
T9704 001:093.834 JLINK_WriteReg(R8, 0x00000000)
T9704 001:093.837 - 0.003ms returns 0
T9704 001:093.841 JLINK_WriteReg(R9, 0x20000414)
T9704 001:093.845 - 0.003ms returns 0
T9704 001:093.849 JLINK_WriteReg(R10, 0x00000000)
T9704 001:093.853 - 0.003ms returns 0
T9704 001:093.857 JLINK_WriteReg(R11, 0x00000000)
T9704 001:093.860 - 0.003ms returns 0
T9704 001:093.864 JLINK_WriteReg(R12, 0x00000000)
T9704 001:093.868 - 0.003ms returns 0
T9704 001:093.872 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:093.876 - 0.004ms returns 0
T9704 001:093.880 JLINK_WriteReg(R14, 0x20000001)
T9704 001:093.884 - 0.003ms returns 0
T9704 001:093.888 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:093.892 - 0.003ms returns 0
T9704 001:093.896 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:093.900 - 0.003ms returns 0
T9704 001:093.904 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:093.907 - 0.003ms returns 0
T9704 001:093.911 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:093.915 - 0.003ms returns 0
T9704 001:093.919 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:093.922 - 0.003ms returns 0
T9704 001:093.927 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:093.932 - 0.005ms returns 0x0000000F
T9704 001:093.936 JLINK_Go()
T9704 001:093.943   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:094.739   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:104.047 - 10.109ms 
T9704 001:104.058 JLINK_IsHalted()
T9704 001:104.858 - 0.799ms returns FALSE
T9704 001:104.867 JLINK_HasError()
T9704 001:108.321 JLINK_IsHalted()
T9704 001:109.221 - 0.899ms returns FALSE
T9704 001:109.231 JLINK_HasError()
T9704 001:110.415 JLINK_IsHalted()
T9704 001:111.305 - 0.890ms returns FALSE
T9704 001:111.314 JLINK_HasError()
T9704 001:112.508 JLINK_IsHalted()
T9704 001:121.114 - 8.604ms returns TRUE
T9704 001:121.135 JLINK_ReadReg(R15 (PC))
T9704 001:121.143 - 0.008ms returns 0x20000000
T9704 001:121.174 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T9704 001:121.181 - 0.007ms returns 0x00
T9704 001:121.186 JLINK_ReadReg(R0)
T9704 001:121.192 - 0.005ms returns 0x00000000
T9704 001:121.668 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:121.680   Data:  80 1C 00 E0 40 1E 00 90 00 28 FB D1 DA E7 2B 4D ...
T9704 001:121.697   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:139.747 - 18.078ms returns 0x3E8
T9704 001:139.759 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:139.764   Data:  00 20 02 40 D8 44 02 58 00 00 02 58 2D E9 F0 47 ...
T9704 001:139.778   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:140.882 - 1.122ms returns 0x18
T9704 001:140.894 JLINK_HasError()
T9704 001:140.900 JLINK_WriteReg(R0, 0x08002C00)
T9704 001:140.906 - 0.006ms returns 0
T9704 001:140.913 JLINK_WriteReg(R1, 0x00000400)
T9704 001:140.918 - 0.004ms returns 0
T9704 001:140.922 JLINK_WriteReg(R2, 0x20000418)
T9704 001:140.926 - 0.003ms returns 0
T9704 001:140.930 JLINK_WriteReg(R3, 0x00000000)
T9704 001:140.933 - 0.003ms returns 0
T9704 001:140.937 JLINK_WriteReg(R4, 0x00000000)
T9704 001:140.941 - 0.003ms returns 0
T9704 001:140.945 JLINK_WriteReg(R5, 0x00000000)
T9704 001:140.948 - 0.003ms returns 0
T9704 001:140.952 JLINK_WriteReg(R6, 0x00000000)
T9704 001:140.956 - 0.003ms returns 0
T9704 001:140.960 JLINK_WriteReg(R7, 0x00000000)
T9704 001:140.964 - 0.003ms returns 0
T9704 001:140.968 JLINK_WriteReg(R8, 0x00000000)
T9704 001:140.971 - 0.003ms returns 0
T9704 001:140.976 JLINK_WriteReg(R9, 0x20000414)
T9704 001:140.979 - 0.003ms returns 0
T9704 001:140.983 JLINK_WriteReg(R10, 0x00000000)
T9704 001:140.987 - 0.003ms returns 0
T9704 001:140.991 JLINK_WriteReg(R11, 0x00000000)
T9704 001:140.995 - 0.003ms returns 0
T9704 001:140.999 JLINK_WriteReg(R12, 0x00000000)
T9704 001:141.002 - 0.003ms returns 0
T9704 001:141.006 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:141.011 - 0.004ms returns 0
T9704 001:141.015 JLINK_WriteReg(R14, 0x20000001)
T9704 001:141.019 - 0.003ms returns 0
T9704 001:141.023 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:141.026 - 0.004ms returns 0
T9704 001:141.031 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:141.034 - 0.003ms returns 0
T9704 001:141.039 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:141.042 - 0.003ms returns 0
T9704 001:141.046 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:141.050 - 0.003ms returns 0
T9704 001:141.054 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:141.057 - 0.003ms returns 0
T9704 001:141.062 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:141.067 - 0.005ms returns 0x00000010
T9704 001:141.071 JLINK_Go()
T9704 001:141.078   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:141.855   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:151.033 - 9.960ms 
T9704 001:151.054 JLINK_IsHalted()
T9704 001:151.999 - 0.944ms returns FALSE
T9704 001:152.022 JLINK_HasError()
T9704 001:155.756 JLINK_IsHalted()
T9704 001:156.543 - 0.787ms returns FALSE
T9704 001:156.554 JLINK_HasError()
T9704 001:157.849 JLINK_IsHalted()
T9704 001:158.612 - 0.762ms returns FALSE
T9704 001:158.621 JLINK_HasError()
T9704 001:160.033 JLINK_IsHalted()
T9704 001:168.797 - 8.763ms returns TRUE
T9704 001:168.819 JLINK_ReadReg(R15 (PC))
T9704 001:168.826 - 0.007ms returns 0x20000000
T9704 001:168.831 JLINK_ClrBPEx(BPHandle = 0x00000010)
T9704 001:168.836 - 0.004ms returns 0x00
T9704 001:168.840 JLINK_ReadReg(R0)
T9704 001:168.844 - 0.003ms returns 0x00000000
T9704 001:169.275 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:169.284   Data:  DF F8 DC 80 8A 46 B0 42 01 D0 40 45 01 D1 35 49 ...
T9704 001:169.299   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:187.494 - 18.218ms returns 0x3E8
T9704 001:187.507 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:187.513   Data:  61 65 00 20 E4 E0 01 20 BD E8 F8 8F C7 4D C7 4E ...
T9704 001:187.529   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:188.612 - 1.104ms returns 0x18
T9704 001:188.624 JLINK_HasError()
T9704 001:188.652 JLINK_WriteReg(R0, 0x08003000)
T9704 001:188.661 - 0.009ms returns 0
T9704 001:188.667 JLINK_WriteReg(R1, 0x00000400)
T9704 001:188.673 - 0.005ms returns 0
T9704 001:188.679 JLINK_WriteReg(R2, 0x20000418)
T9704 001:188.684 - 0.005ms returns 0
T9704 001:188.690 JLINK_WriteReg(R3, 0x00000000)
T9704 001:188.695 - 0.004ms returns 0
T9704 001:188.699 JLINK_WriteReg(R4, 0x00000000)
T9704 001:188.702 - 0.003ms returns 0
T9704 001:188.706 JLINK_WriteReg(R5, 0x00000000)
T9704 001:188.710 - 0.003ms returns 0
T9704 001:188.714 JLINK_WriteReg(R6, 0x00000000)
T9704 001:188.717 - 0.003ms returns 0
T9704 001:188.721 JLINK_WriteReg(R7, 0x00000000)
T9704 001:188.725 - 0.003ms returns 0
T9704 001:188.729 JLINK_WriteReg(R8, 0x00000000)
T9704 001:188.732 - 0.003ms returns 0
T9704 001:188.736 JLINK_WriteReg(R9, 0x20000414)
T9704 001:188.740 - 0.003ms returns 0
T9704 001:188.773 JLINK_WriteReg(R10, 0x00000000)
T9704 001:188.779 - 0.005ms returns 0
T9704 001:188.783 JLINK_WriteReg(R11, 0x00000000)
T9704 001:188.786 - 0.003ms returns 0
T9704 001:188.790 JLINK_WriteReg(R12, 0x00000000)
T9704 001:188.794 - 0.003ms returns 0
T9704 001:188.798 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:188.802 - 0.004ms returns 0
T9704 001:188.806 JLINK_WriteReg(R14, 0x20000001)
T9704 001:188.810 - 0.003ms returns 0
T9704 001:188.814 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:188.818 - 0.003ms returns 0
T9704 001:188.822 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:188.825 - 0.003ms returns 0
T9704 001:188.829 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:188.833 - 0.003ms returns 0
T9704 001:188.837 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:188.841 - 0.003ms returns 0
T9704 001:188.845 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:188.848 - 0.003ms returns 0
T9704 001:188.853 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:188.858 - 0.005ms returns 0x00000011
T9704 001:188.862 JLINK_Go()
T9704 001:188.870   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:189.731   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:199.126 - 10.262ms 
T9704 001:199.140 JLINK_IsHalted()
T9704 001:199.988 - 0.847ms returns FALSE
T9704 001:200.001 JLINK_HasError()
T9704 001:206.192 JLINK_IsHalted()
T9704 001:207.042 - 0.849ms returns FALSE
T9704 001:207.051 JLINK_HasError()
T9704 001:208.263 JLINK_IsHalted()
T9704 001:216.916 - 8.653ms returns TRUE
T9704 001:216.929 JLINK_ReadReg(R15 (PC))
T9704 001:216.935 - 0.006ms returns 0x20000000
T9704 001:216.958 JLINK_ClrBPEx(BPHandle = 0x00000011)
T9704 001:216.963 - 0.005ms returns 0x00
T9704 001:216.968 JLINK_ReadReg(R0)
T9704 001:216.972 - 0.003ms returns 0x00000000
T9704 001:217.367 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:217.376   Data:  DF F8 14 83 DF F8 14 93 88 42 DF F8 14 A3 DF F8 ...
T9704 001:217.389   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:235.561 - 18.193ms returns 0x3E8
T9704 001:235.574 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:235.579   Data:  21 F0 01 01 01 60 61 46 20 46 FE F7 EF FD 6B 49 ...
T9704 001:235.592   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:236.718 - 1.143ms returns 0x18
T9704 001:236.728 JLINK_HasError()
T9704 001:236.734 JLINK_WriteReg(R0, 0x08003400)
T9704 001:236.740 - 0.005ms returns 0
T9704 001:236.744 JLINK_WriteReg(R1, 0x00000400)
T9704 001:236.748 - 0.003ms returns 0
T9704 001:236.752 JLINK_WriteReg(R2, 0x20000418)
T9704 001:236.755 - 0.003ms returns 0
T9704 001:236.759 JLINK_WriteReg(R3, 0x00000000)
T9704 001:236.763 - 0.003ms returns 0
T9704 001:236.767 JLINK_WriteReg(R4, 0x00000000)
T9704 001:236.771 - 0.003ms returns 0
T9704 001:236.775 JLINK_WriteReg(R5, 0x00000000)
T9704 001:236.778 - 0.003ms returns 0
T9704 001:236.782 JLINK_WriteReg(R6, 0x00000000)
T9704 001:236.786 - 0.003ms returns 0
T9704 001:236.790 JLINK_WriteReg(R7, 0x00000000)
T9704 001:236.794 - 0.003ms returns 0
T9704 001:236.798 JLINK_WriteReg(R8, 0x00000000)
T9704 001:236.802 - 0.003ms returns 0
T9704 001:236.806 JLINK_WriteReg(R9, 0x20000414)
T9704 001:236.813 - 0.003ms returns 0
T9704 001:236.817 JLINK_WriteReg(R10, 0x00000000)
T9704 001:236.821 - 0.003ms returns 0
T9704 001:236.825 JLINK_WriteReg(R11, 0x00000000)
T9704 001:236.828 - 0.003ms returns 0
T9704 001:236.832 JLINK_WriteReg(R12, 0x00000000)
T9704 001:236.836 - 0.004ms returns 0
T9704 001:236.841 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:236.847 - 0.006ms returns 0
T9704 001:236.853 JLINK_WriteReg(R14, 0x20000001)
T9704 001:236.858 - 0.005ms returns 0
T9704 001:236.864 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:236.868 - 0.004ms returns 0
T9704 001:236.873 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:236.876 - 0.003ms returns 0
T9704 001:236.880 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:236.884 - 0.003ms returns 0
T9704 001:236.888 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:236.892 - 0.004ms returns 0
T9704 001:236.898 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:236.904 - 0.005ms returns 0
T9704 001:236.943 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:236.948 - 0.005ms returns 0x00000012
T9704 001:236.952 JLINK_Go()
T9704 001:236.959   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:237.826   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:247.185 - 10.231ms 
T9704 001:247.197 JLINK_IsHalted()
T9704 001:248.046 - 0.848ms returns FALSE
T9704 001:248.059 JLINK_HasError()
T9704 001:251.041 JLINK_IsHalted()
T9704 001:251.859 - 0.816ms returns FALSE
T9704 001:251.875 JLINK_HasError()
T9704 001:253.259 JLINK_IsHalted()
T9704 001:254.115 - 0.855ms returns FALSE
T9704 001:254.126 JLINK_HasError()
T9704 001:255.369 JLINK_IsHalted()
T9704 001:263.978 - 8.609ms returns TRUE
T9704 001:263.992 JLINK_ReadReg(R15 (PC))
T9704 001:263.998 - 0.006ms returns 0x20000000
T9704 001:264.002 JLINK_ClrBPEx(BPHandle = 0x00000012)
T9704 001:264.007 - 0.004ms returns 0x00
T9704 001:264.011 JLINK_ReadReg(R0)
T9704 001:264.015 - 0.003ms returns 0x00000000
T9704 001:264.471 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:264.480   Data:  2A D0 B0 42 28 D0 B8 42 26 D0 40 45 24 D0 48 45 ...
T9704 001:264.494   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:282.630 - 18.158ms returns 0x3E8
T9704 001:282.646 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:282.650   Data:  F8 8F 00 00 80 00 00 58 F4 44 02 58 00 00 02 58 ...
T9704 001:282.665   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:283.798 - 1.151ms returns 0x18
T9704 001:283.810 JLINK_HasError()
T9704 001:283.835 JLINK_WriteReg(R0, 0x08003800)
T9704 001:283.841 - 0.006ms returns 0
T9704 001:283.845 JLINK_WriteReg(R1, 0x00000400)
T9704 001:283.849 - 0.003ms returns 0
T9704 001:283.853 JLINK_WriteReg(R2, 0x20000418)
T9704 001:283.857 - 0.003ms returns 0
T9704 001:283.861 JLINK_WriteReg(R3, 0x00000000)
T9704 001:283.864 - 0.003ms returns 0
T9704 001:283.868 JLINK_WriteReg(R4, 0x00000000)
T9704 001:283.872 - 0.003ms returns 0
T9704 001:283.876 JLINK_WriteReg(R5, 0x00000000)
T9704 001:283.879 - 0.003ms returns 0
T9704 001:283.883 JLINK_WriteReg(R6, 0x00000000)
T9704 001:283.887 - 0.003ms returns 0
T9704 001:283.891 JLINK_WriteReg(R7, 0x00000000)
T9704 001:283.895 - 0.003ms returns 0
T9704 001:283.899 JLINK_WriteReg(R8, 0x00000000)
T9704 001:283.903 - 0.003ms returns 0
T9704 001:283.907 JLINK_WriteReg(R9, 0x20000414)
T9704 001:283.910 - 0.003ms returns 0
T9704 001:283.914 JLINK_WriteReg(R10, 0x00000000)
T9704 001:283.918 - 0.003ms returns 0
T9704 001:283.922 JLINK_WriteReg(R11, 0x00000000)
T9704 001:283.925 - 0.003ms returns 0
T9704 001:283.930 JLINK_WriteReg(R12, 0x00000000)
T9704 001:283.933 - 0.003ms returns 0
T9704 001:283.937 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:283.941 - 0.004ms returns 0
T9704 001:283.945 JLINK_WriteReg(R14, 0x20000001)
T9704 001:283.949 - 0.003ms returns 0
T9704 001:283.953 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:283.957 - 0.004ms returns 0
T9704 001:283.961 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:283.965 - 0.003ms returns 0
T9704 001:283.969 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:283.972 - 0.003ms returns 0
T9704 001:283.976 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:283.980 - 0.003ms returns 0
T9704 001:283.984 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:283.988 - 0.003ms returns 0
T9704 001:283.992 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:283.997 - 0.005ms returns 0x00000013
T9704 001:284.001 JLINK_Go()
T9704 001:284.009   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:284.833   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:294.018 - 10.015ms 
T9704 001:294.029 JLINK_IsHalted()
T9704 001:294.976 - 0.946ms returns FALSE
T9704 001:294.985 JLINK_HasError()
T9704 001:298.601 JLINK_IsHalted()
T9704 001:299.374 - 0.772ms returns FALSE
T9704 001:299.438 JLINK_HasError()
T9704 001:300.774 JLINK_IsHalted()
T9704 001:301.699 - 0.925ms returns FALSE
T9704 001:301.714 JLINK_HasError()
T9704 001:302.921 JLINK_IsHalted()
T9704 001:311.457 - 8.535ms returns TRUE
T9704 001:311.472 JLINK_ReadReg(R15 (PC))
T9704 001:311.482 - 0.010ms returns 0x20000000
T9704 001:311.511 JLINK_ClrBPEx(BPHandle = 0x00000013)
T9704 001:311.516 - 0.005ms returns 0x00
T9704 001:311.521 JLINK_ReadReg(R0)
T9704 001:311.525 - 0.003ms returns 0x00000000
T9704 001:311.915 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:311.923   Data:  00 0C 02 58 00 10 02 58 00 14 02 58 00 18 02 58 ...
T9704 001:311.936   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:330.135 - 18.220ms returns 0x3E8
T9704 001:330.148 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:330.153   Data:  82 EE A1 2A C1 F3 08 01 B6 E7 FF E7 00 21 01 60 ...
T9704 001:330.167   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:331.251 - 1.101ms returns 0x18
T9704 001:331.265 JLINK_HasError()
T9704 001:331.273 JLINK_WriteReg(R0, 0x08003C00)
T9704 001:331.283 - 0.011ms returns 0
T9704 001:331.289 JLINK_WriteReg(R1, 0x00000400)
T9704 001:331.295 - 0.005ms returns 0
T9704 001:331.301 JLINK_WriteReg(R2, 0x20000418)
T9704 001:331.306 - 0.005ms returns 0
T9704 001:331.311 JLINK_WriteReg(R3, 0x00000000)
T9704 001:331.315 - 0.003ms returns 0
T9704 001:331.319 JLINK_WriteReg(R4, 0x00000000)
T9704 001:331.322 - 0.003ms returns 0
T9704 001:331.327 JLINK_WriteReg(R5, 0x00000000)
T9704 001:331.332 - 0.005ms returns 0
T9704 001:331.338 JLINK_WriteReg(R6, 0x00000000)
T9704 001:331.342 - 0.003ms returns 0
T9704 001:331.346 JLINK_WriteReg(R7, 0x00000000)
T9704 001:331.350 - 0.004ms returns 0
T9704 001:331.355 JLINK_WriteReg(R8, 0x00000000)
T9704 001:331.360 - 0.004ms returns 0
T9704 001:331.365 JLINK_WriteReg(R9, 0x20000414)
T9704 001:331.370 - 0.004ms returns 0
T9704 001:331.375 JLINK_WriteReg(R10, 0x00000000)
T9704 001:331.380 - 0.004ms returns 0
T9704 001:331.385 JLINK_WriteReg(R11, 0x00000000)
T9704 001:331.390 - 0.004ms returns 0
T9704 001:331.395 JLINK_WriteReg(R12, 0x00000000)
T9704 001:331.400 - 0.004ms returns 0
T9704 001:331.405 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:331.410 - 0.005ms returns 0
T9704 001:331.415 JLINK_WriteReg(R14, 0x20000001)
T9704 001:331.420 - 0.004ms returns 0
T9704 001:331.426 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:331.430 - 0.005ms returns 0
T9704 001:331.435 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:331.440 - 0.004ms returns 0
T9704 001:331.445 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:331.450 - 0.004ms returns 0
T9704 001:331.455 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:331.460 - 0.004ms returns 0
T9704 001:331.465 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:331.470 - 0.005ms returns 0
T9704 001:331.475 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:331.481 - 0.006ms returns 0x00000014
T9704 001:331.487 JLINK_Go()
T9704 001:331.496   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:332.381   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:341.616 - 10.128ms 
T9704 001:341.629 JLINK_IsHalted()
T9704 001:342.429 - 0.799ms returns FALSE
T9704 001:342.439 JLINK_HasError()
T9704 001:346.163 JLINK_IsHalted()
T9704 001:346.989 - 0.826ms returns FALSE
T9704 001:347.001 JLINK_HasError()
T9704 001:348.334 JLINK_IsHalted()
T9704 001:349.205 - 0.870ms returns FALSE
T9704 001:349.215 JLINK_HasError()
T9704 001:350.447 JLINK_IsHalted()
T9704 001:359.097 - 8.649ms returns TRUE
T9704 001:359.108 JLINK_ReadReg(R15 (PC))
T9704 001:359.114 - 0.006ms returns 0x20000000
T9704 001:359.119 JLINK_ClrBPEx(BPHandle = 0x00000014)
T9704 001:359.123 - 0.004ms returns 0x00
T9704 001:359.128 JLINK_ReadReg(R0)
T9704 001:359.132 - 0.004ms returns 0x00000000
T9704 001:359.573 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:359.583   Data:  28 44 02 58 00 00 00 39 00 24 74 4C 30 44 02 58 ...
T9704 001:359.596   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:377.566 - 17.991ms returns 0x3E8
T9704 001:377.581 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:377.587   Data:  08 DC D9 B1 B1 F1 80 7F C6 D1 5E E0 49 E0 6C E0 ...
T9704 001:377.602   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:378.731 - 1.148ms returns 0x18
T9704 001:378.744 JLINK_HasError()
T9704 001:378.772 JLINK_WriteReg(R0, 0x08004000)
T9704 001:378.786 - 0.013ms returns 0
T9704 001:378.791 JLINK_WriteReg(R1, 0x00000400)
T9704 001:378.795 - 0.004ms returns 0
T9704 001:378.799 JLINK_WriteReg(R2, 0x20000418)
T9704 001:378.803 - 0.003ms returns 0
T9704 001:378.807 JLINK_WriteReg(R3, 0x00000000)
T9704 001:378.810 - 0.003ms returns 0
T9704 001:378.814 JLINK_WriteReg(R4, 0x00000000)
T9704 001:378.818 - 0.003ms returns 0
T9704 001:378.822 JLINK_WriteReg(R5, 0x00000000)
T9704 001:378.826 - 0.003ms returns 0
T9704 001:378.830 JLINK_WriteReg(R6, 0x00000000)
T9704 001:378.833 - 0.003ms returns 0
T9704 001:378.837 JLINK_WriteReg(R7, 0x00000000)
T9704 001:378.841 - 0.003ms returns 0
T9704 001:378.845 JLINK_WriteReg(R8, 0x00000000)
T9704 001:378.849 - 0.003ms returns 0
T9704 001:378.853 JLINK_WriteReg(R9, 0x20000414)
T9704 001:378.856 - 0.003ms returns 0
T9704 001:378.860 JLINK_WriteReg(R10, 0x00000000)
T9704 001:378.864 - 0.003ms returns 0
T9704 001:378.868 JLINK_WriteReg(R11, 0x00000000)
T9704 001:378.872 - 0.003ms returns 0
T9704 001:378.876 JLINK_WriteReg(R12, 0x00000000)
T9704 001:378.880 - 0.003ms returns 0
T9704 001:378.884 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:378.888 - 0.004ms returns 0
T9704 001:378.892 JLINK_WriteReg(R14, 0x20000001)
T9704 001:378.896 - 0.003ms returns 0
T9704 001:378.900 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:378.903 - 0.003ms returns 0
T9704 001:378.908 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:378.911 - 0.003ms returns 0
T9704 001:378.915 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:378.919 - 0.003ms returns 0
T9704 001:378.924 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:378.929 - 0.005ms returns 0
T9704 001:378.934 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:378.938 - 0.004ms returns 0
T9704 001:378.944 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:378.950 - 0.006ms returns 0x00000015
T9704 001:378.956 JLINK_Go()
T9704 001:378.968   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:379.746   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:388.991 - 10.034ms 
T9704 001:389.003 JLINK_IsHalted()
T9704 001:389.812 - 0.808ms returns FALSE
T9704 001:389.824 JLINK_HasError()
T9704 001:393.465 JLINK_IsHalted()
T9704 001:394.226 - 0.761ms returns FALSE
T9704 001:394.235 JLINK_HasError()
T9704 001:395.571 JLINK_IsHalted()
T9704 001:396.365 - 0.793ms returns FALSE
T9704 001:396.373 JLINK_HasError()
T9704 001:397.648 JLINK_IsHalted()
T9704 001:406.411 - 8.762ms returns TRUE
T9704 001:406.424 JLINK_ReadReg(R15 (PC))
T9704 001:406.431 - 0.006ms returns 0x20000000
T9704 001:406.454 JLINK_ClrBPEx(BPHandle = 0x00000015)
T9704 001:406.458 - 0.004ms returns 0x00
T9704 001:406.463 JLINK_ReadReg(R0)
T9704 001:406.467 - 0.003ms returns 0x00000000
T9704 001:406.850 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:406.859   Data:  C0 D0 B1 F1 80 6F BB D1 48 E0 DA F8 00 10 01 F4 ...
T9704 001:406.872   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:424.946 - 18.095ms returns 0x3E8
T9704 001:424.960 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:424.964   Data:  04 46 8C B1 26 46 28 88 C0 43 80 04 29 D4 68 6E ...
T9704 001:424.980   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:426.079 - 1.119ms returns 0x18
T9704 001:426.090 JLINK_HasError()
T9704 001:426.095 JLINK_WriteReg(R0, 0x08004400)
T9704 001:426.102 - 0.006ms returns 0
T9704 001:426.106 JLINK_WriteReg(R1, 0x00000400)
T9704 001:426.110 - 0.003ms returns 0
T9704 001:426.114 JLINK_WriteReg(R2, 0x20000418)
T9704 001:426.118 - 0.003ms returns 0
T9704 001:426.122 JLINK_WriteReg(R3, 0x00000000)
T9704 001:426.125 - 0.003ms returns 0
T9704 001:426.129 JLINK_WriteReg(R4, 0x00000000)
T9704 001:426.133 - 0.003ms returns 0
T9704 001:426.137 JLINK_WriteReg(R5, 0x00000000)
T9704 001:426.140 - 0.003ms returns 0
T9704 001:426.144 JLINK_WriteReg(R6, 0x00000000)
T9704 001:426.148 - 0.003ms returns 0
T9704 001:426.152 JLINK_WriteReg(R7, 0x00000000)
T9704 001:426.156 - 0.003ms returns 0
T9704 001:426.160 JLINK_WriteReg(R8, 0x00000000)
T9704 001:426.163 - 0.003ms returns 0
T9704 001:426.169 JLINK_WriteReg(R9, 0x20000414)
T9704 001:426.174 - 0.004ms returns 0
T9704 001:426.178 JLINK_WriteReg(R10, 0x00000000)
T9704 001:426.182 - 0.003ms returns 0
T9704 001:426.186 JLINK_WriteReg(R11, 0x00000000)
T9704 001:426.189 - 0.003ms returns 0
T9704 001:426.193 JLINK_WriteReg(R12, 0x00000000)
T9704 001:426.197 - 0.003ms returns 0
T9704 001:426.201 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:426.206 - 0.004ms returns 0
T9704 001:426.210 JLINK_WriteReg(R14, 0x20000001)
T9704 001:426.213 - 0.003ms returns 0
T9704 001:426.218 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:426.221 - 0.003ms returns 0
T9704 001:426.225 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:426.229 - 0.003ms returns 0
T9704 001:426.233 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:426.237 - 0.003ms returns 0
T9704 001:426.241 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:426.245 - 0.003ms returns 0
T9704 001:426.249 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:426.252 - 0.003ms returns 0
T9704 001:426.257 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:426.262 - 0.005ms returns 0x00000016
T9704 001:426.266 JLINK_Go()
T9704 001:426.273   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:427.162   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:436.484 - 10.216ms 
T9704 001:436.498 JLINK_IsHalted()
T9704 001:437.239 - 0.740ms returns FALSE
T9704 001:437.248 JLINK_HasError()
T9704 001:439.716 JLINK_IsHalted()
T9704 001:440.627 - 0.910ms returns FALSE
T9704 001:440.643 JLINK_HasError()
T9704 001:441.835 JLINK_IsHalted()
T9704 001:442.679 - 0.843ms returns FALSE
T9704 001:442.690 JLINK_HasError()
T9704 001:443.958 JLINK_IsHalted()
T9704 001:452.539 - 8.579ms returns TRUE
T9704 001:452.556 JLINK_ReadReg(R15 (PC))
T9704 001:452.565 - 0.008ms returns 0x20000000
T9704 001:452.571 JLINK_ClrBPEx(BPHandle = 0x00000016)
T9704 001:452.577 - 0.006ms returns 0x00
T9704 001:452.583 JLINK_ReadReg(R0)
T9704 001:452.588 - 0.005ms returns 0x00000000
T9704 001:453.080 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:453.092   Data:  08 B3 B0 F5 80 3F 14 D0 B0 F5 00 3F 0F D1 15 E0 ...
T9704 001:453.109   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:471.249 - 18.169ms returns 0x3E8
T9704 001:471.270 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:471.277   Data:  16 E0 D5 F8 9C 20 D9 F8 00 10 21 F4 E0 51 11 43 ...
T9704 001:471.293   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:472.376 - 1.105ms returns 0x18
T9704 001:472.394 JLINK_HasError()
T9704 001:472.428 JLINK_WriteReg(R0, 0x08004800)
T9704 001:472.437 - 0.009ms returns 0
T9704 001:472.443 JLINK_WriteReg(R1, 0x00000400)
T9704 001:472.448 - 0.005ms returns 0
T9704 001:472.454 JLINK_WriteReg(R2, 0x20000418)
T9704 001:472.459 - 0.005ms returns 0
T9704 001:472.465 JLINK_WriteReg(R3, 0x00000000)
T9704 001:472.470 - 0.005ms returns 0
T9704 001:472.475 JLINK_WriteReg(R4, 0x00000000)
T9704 001:472.480 - 0.004ms returns 0
T9704 001:472.485 JLINK_WriteReg(R5, 0x00000000)
T9704 001:472.489 - 0.003ms returns 0
T9704 001:472.493 JLINK_WriteReg(R6, 0x00000000)
T9704 001:472.496 - 0.003ms returns 0
T9704 001:472.500 JLINK_WriteReg(R7, 0x00000000)
T9704 001:472.504 - 0.003ms returns 0
T9704 001:472.508 JLINK_WriteReg(R8, 0x00000000)
T9704 001:472.512 - 0.003ms returns 0
T9704 001:472.516 JLINK_WriteReg(R9, 0x20000414)
T9704 001:472.519 - 0.003ms returns 0
T9704 001:472.523 JLINK_WriteReg(R10, 0x00000000)
T9704 001:472.527 - 0.003ms returns 0
T9704 001:472.531 JLINK_WriteReg(R11, 0x00000000)
T9704 001:472.535 - 0.003ms returns 0
T9704 001:472.539 JLINK_WriteReg(R12, 0x00000000)
T9704 001:472.542 - 0.003ms returns 0
T9704 001:472.547 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:472.551 - 0.004ms returns 0
T9704 001:472.555 JLINK_WriteReg(R14, 0x20000001)
T9704 001:472.558 - 0.003ms returns 0
T9704 001:472.563 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:472.567 - 0.004ms returns 0
T9704 001:472.573 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:472.578 - 0.005ms returns 0
T9704 001:472.584 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:472.591 - 0.007ms returns 0
T9704 001:472.596 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:472.600 - 0.003ms returns 0
T9704 001:472.604 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:472.608 - 0.003ms returns 0
T9704 001:472.612 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:472.618 - 0.005ms returns 0x00000017
T9704 001:472.622 JLINK_Go()
T9704 001:472.630   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:473.439   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:482.978 - 10.355ms 
T9704 001:482.989 JLINK_IsHalted()
T9704 001:483.815 - 0.825ms returns FALSE
T9704 001:483.826 JLINK_HasError()
T9704 001:487.609 JLINK_IsHalted()
T9704 001:488.426 - 0.817ms returns FALSE
T9704 001:488.440 JLINK_HasError()
T9704 001:489.777 JLINK_IsHalted()
T9704 001:490.750 - 0.972ms returns FALSE
T9704 001:490.763 JLINK_HasError()
T9704 001:494.176 JLINK_IsHalted()
T9704 001:502.731 - 8.554ms returns TRUE
T9704 001:502.746 JLINK_ReadReg(R15 (PC))
T9704 001:502.752 - 0.006ms returns 0x20000000
T9704 001:502.777 JLINK_ClrBPEx(BPHandle = 0x00000017)
T9704 001:502.781 - 0.004ms returns 0x00
T9704 001:502.786 JLINK_ReadReg(R0)
T9704 001:502.790 - 0.003ms returns 0x00000000
T9704 001:503.184 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:503.194   Data:  00 4F 0E D0 B0 F5 20 4F 0B D0 01 24 0A E0 01 98 ...
T9704 001:503.208   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:521.287 - 18.102ms returns 0x3E8
T9704 001:521.300 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:521.304   Data:  29 60 20 78 C0 07 3E D0 5B 49 A2 68 28 68 18 39 ...
T9704 001:521.318   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:522.506 - 1.205ms returns 0x18
T9704 001:522.518 JLINK_HasError()
T9704 001:522.524 JLINK_WriteReg(R0, 0x08004C00)
T9704 001:522.530 - 0.006ms returns 0
T9704 001:522.534 JLINK_WriteReg(R1, 0x00000400)
T9704 001:522.538 - 0.003ms returns 0
T9704 001:522.543 JLINK_WriteReg(R2, 0x20000418)
T9704 001:522.546 - 0.003ms returns 0
T9704 001:522.550 JLINK_WriteReg(R3, 0x00000000)
T9704 001:522.554 - 0.003ms returns 0
T9704 001:522.558 JLINK_WriteReg(R4, 0x00000000)
T9704 001:522.561 - 0.003ms returns 0
T9704 001:522.566 JLINK_WriteReg(R5, 0x00000000)
T9704 001:522.569 - 0.003ms returns 0
T9704 001:522.573 JLINK_WriteReg(R6, 0x00000000)
T9704 001:522.577 - 0.003ms returns 0
T9704 001:522.581 JLINK_WriteReg(R7, 0x00000000)
T9704 001:522.584 - 0.003ms returns 0
T9704 001:522.589 JLINK_WriteReg(R8, 0x00000000)
T9704 001:522.592 - 0.003ms returns 0
T9704 001:522.596 JLINK_WriteReg(R9, 0x20000414)
T9704 001:522.600 - 0.003ms returns 0
T9704 001:522.604 JLINK_WriteReg(R10, 0x00000000)
T9704 001:522.607 - 0.003ms returns 0
T9704 001:522.611 JLINK_WriteReg(R11, 0x00000000)
T9704 001:522.615 - 0.003ms returns 0
T9704 001:522.619 JLINK_WriteReg(R12, 0x00000000)
T9704 001:522.623 - 0.003ms returns 0
T9704 001:522.627 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:522.631 - 0.004ms returns 0
T9704 001:522.635 JLINK_WriteReg(R14, 0x20000001)
T9704 001:522.639 - 0.003ms returns 0
T9704 001:522.643 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:522.647 - 0.003ms returns 0
T9704 001:522.651 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:522.654 - 0.003ms returns 0
T9704 001:522.658 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:522.662 - 0.003ms returns 0
T9704 001:522.666 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:522.669 - 0.003ms returns 0
T9704 001:522.674 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:522.678 - 0.004ms returns 0
T9704 001:522.682 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:522.687 - 0.005ms returns 0x00000018
T9704 001:522.691 JLINK_Go()
T9704 001:522.699   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:523.487   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:532.787 - 10.094ms 
T9704 001:532.807 JLINK_IsHalted()
T9704 001:533.609 - 0.801ms returns FALSE
T9704 001:533.618 JLINK_HasError()
T9704 001:537.334 JLINK_IsHalted()
T9704 001:538.170 - 0.835ms returns FALSE
T9704 001:538.178 JLINK_HasError()
T9704 001:539.427 JLINK_IsHalted()
T9704 001:540.232 - 0.805ms returns FALSE
T9704 001:540.243 JLINK_HasError()
T9704 001:541.543 JLINK_IsHalted()
T9704 001:550.232 - 8.687ms returns TRUE
T9704 001:550.242 JLINK_ReadReg(R15 (PC))
T9704 001:550.248 - 0.005ms returns 0x20000000
T9704 001:550.253 JLINK_ClrBPEx(BPHandle = 0x00000018)
T9704 001:550.257 - 0.004ms returns 0x00
T9704 001:550.261 JLINK_ReadReg(R0)
T9704 001:550.265 - 0.003ms returns 0x00000000
T9704 001:550.644 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:550.653   Data:  60 68 02 28 18 D0 03 28 19 D0 09 68 01 28 19 D0 ...
T9704 001:550.667   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:568.749 - 18.103ms returns 0x3E8
T9704 001:568.768 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:568.774   Data:  03 08 D5 4E 0C 3E 80 07 69 D5 D9 F8 00 00 39 68 ...
T9704 001:568.793   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:569.944 - 1.175ms returns 0x18
T9704 001:569.959 JLINK_HasError()
T9704 001:569.992 JLINK_WriteReg(R0, 0x08005000)
T9704 001:570.004 - 0.012ms returns 0
T9704 001:570.010 JLINK_WriteReg(R1, 0x00000400)
T9704 001:570.016 - 0.005ms returns 0
T9704 001:570.021 JLINK_WriteReg(R2, 0x20000418)
T9704 001:570.026 - 0.005ms returns 0
T9704 001:570.032 JLINK_WriteReg(R3, 0x00000000)
T9704 001:570.037 - 0.005ms returns 0
T9704 001:570.042 JLINK_WriteReg(R4, 0x00000000)
T9704 001:570.048 - 0.005ms returns 0
T9704 001:570.053 JLINK_WriteReg(R5, 0x00000000)
T9704 001:570.058 - 0.005ms returns 0
T9704 001:570.064 JLINK_WriteReg(R6, 0x00000000)
T9704 001:570.069 - 0.005ms returns 0
T9704 001:570.075 JLINK_WriteReg(R7, 0x00000000)
T9704 001:570.080 - 0.005ms returns 0
T9704 001:570.085 JLINK_WriteReg(R8, 0x00000000)
T9704 001:570.090 - 0.005ms returns 0
T9704 001:570.096 JLINK_WriteReg(R9, 0x20000414)
T9704 001:570.101 - 0.005ms returns 0
T9704 001:570.107 JLINK_WriteReg(R10, 0x00000000)
T9704 001:570.112 - 0.005ms returns 0
T9704 001:570.118 JLINK_WriteReg(R11, 0x00000000)
T9704 001:570.123 - 0.005ms returns 0
T9704 001:570.128 JLINK_WriteReg(R12, 0x00000000)
T9704 001:570.133 - 0.005ms returns 0
T9704 001:570.139 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:570.145 - 0.006ms returns 0
T9704 001:570.151 JLINK_WriteReg(R14, 0x20000001)
T9704 001:570.156 - 0.005ms returns 0
T9704 001:570.162 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:570.167 - 0.005ms returns 0
T9704 001:570.173 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:570.178 - 0.005ms returns 0
T9704 001:570.184 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:570.189 - 0.005ms returns 0
T9704 001:570.195 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:570.200 - 0.005ms returns 0
T9704 001:570.206 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:570.211 - 0.005ms returns 0
T9704 001:570.218 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:570.225 - 0.007ms returns 0x00000019
T9704 001:570.230 JLINK_Go()
T9704 001:570.242   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:571.005   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:580.286 - 10.054ms 
T9704 001:580.299 JLINK_IsHalted()
T9704 001:581.128 - 0.828ms returns FALSE
T9704 001:581.139 JLINK_HasError()
T9704 001:585.600 JLINK_IsHalted()
T9704 001:586.369 - 0.769ms returns FALSE
T9704 001:586.383 JLINK_HasError()
T9704 001:587.731 JLINK_IsHalted()
T9704 001:588.502 - 0.769ms returns FALSE
T9704 001:588.511 JLINK_HasError()
T9704 001:589.861 JLINK_IsHalted()
T9704 001:598.301 - 8.439ms returns TRUE
T9704 001:598.314 JLINK_ReadReg(R15 (PC))
T9704 001:598.320 - 0.006ms returns 0x20000000
T9704 001:598.344 JLINK_ClrBPEx(BPHandle = 0x00000019)
T9704 001:598.349 - 0.004ms returns 0x00
T9704 001:598.353 JLINK_ReadReg(R0)
T9704 001:598.357 - 0.003ms returns 0x00000000
T9704 001:598.810 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:598.819   Data:  01 D1 88 07 0A D0 E0 68 90 B3 29 68 21 F0 19 01 ...
T9704 001:598.834   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:616.883 - 18.071ms returns 0x3E8
T9704 001:616.899 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:616.906   Data:  28 60 FE F7 3B FA 04 46 05 E0 00 BF FE F7 36 FA ...
T9704 001:616.927   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:618.062 - 1.162ms returns 0x18
T9704 001:618.075 JLINK_HasError()
T9704 001:618.082 JLINK_WriteReg(R0, 0x08005400)
T9704 001:618.093 - 0.011ms returns 0
T9704 001:618.101 JLINK_WriteReg(R1, 0x00000400)
T9704 001:618.107 - 0.006ms returns 0
T9704 001:618.113 JLINK_WriteReg(R2, 0x20000418)
T9704 001:618.118 - 0.004ms returns 0
T9704 001:618.123 JLINK_WriteReg(R3, 0x00000000)
T9704 001:618.128 - 0.004ms returns 0
T9704 001:618.133 JLINK_WriteReg(R4, 0x00000000)
T9704 001:618.139 - 0.005ms returns 0
T9704 001:618.144 JLINK_WriteReg(R5, 0x00000000)
T9704 001:618.149 - 0.004ms returns 0
T9704 001:618.154 JLINK_WriteReg(R6, 0x00000000)
T9704 001:618.159 - 0.005ms returns 0
T9704 001:618.164 JLINK_WriteReg(R7, 0x00000000)
T9704 001:618.169 - 0.004ms returns 0
T9704 001:618.175 JLINK_WriteReg(R8, 0x00000000)
T9704 001:618.180 - 0.005ms returns 0
T9704 001:618.186 JLINK_WriteReg(R9, 0x20000414)
T9704 001:618.191 - 0.005ms returns 0
T9704 001:618.197 JLINK_WriteReg(R10, 0x00000000)
T9704 001:618.203 - 0.005ms returns 0
T9704 001:618.209 JLINK_WriteReg(R11, 0x00000000)
T9704 001:618.215 - 0.005ms returns 0
T9704 001:618.220 JLINK_WriteReg(R12, 0x00000000)
T9704 001:618.226 - 0.005ms returns 0
T9704 001:618.232 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:618.238 - 0.006ms returns 0
T9704 001:618.243 JLINK_WriteReg(R14, 0x20000001)
T9704 001:618.248 - 0.005ms returns 0
T9704 001:618.254 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:618.259 - 0.005ms returns 0
T9704 001:618.264 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:618.270 - 0.005ms returns 0
T9704 001:618.276 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:618.280 - 0.004ms returns 0
T9704 001:618.286 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:618.290 - 0.004ms returns 0
T9704 001:618.296 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:618.301 - 0.005ms returns 0
T9704 001:618.308 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:618.333 - 0.024ms returns 0x0000001A
T9704 001:618.343 JLINK_Go()
T9704 001:618.357   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:619.184   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:628.547 - 10.202ms 
T9704 001:628.561 JLINK_IsHalted()
T9704 001:629.356 - 0.795ms returns FALSE
T9704 001:629.368 JLINK_HasError()
T9704 001:634.367 JLINK_IsHalted()
T9704 001:635.325 - 0.958ms returns FALSE
T9704 001:635.338 JLINK_HasError()
T9704 001:636.488 JLINK_IsHalted()
T9704 001:645.180 - 8.691ms returns TRUE
T9704 001:645.194 JLINK_ReadReg(R15 (PC))
T9704 001:645.202 - 0.008ms returns 0x20000000
T9704 001:645.209 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T9704 001:645.215 - 0.006ms returns 0x00
T9704 001:645.222 JLINK_ReadReg(R0)
T9704 001:645.227 - 0.005ms returns 0x00000000
T9704 001:645.679 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:645.688   Data:  80 01 F7 D5 57 E0 00 BF FE F7 2C FA 00 1B 02 28 ...
T9704 001:645.703   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:663.746 - 18.066ms returns 0x3E8
T9704 001:663.763 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:663.768   Data:  20 68 08 30 50 E8 00 1F 41 F0 80 01 40 E8 00 12 ...
T9704 001:663.781   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:665.029 - 1.264ms returns 0x18
T9704 001:665.047 JLINK_HasError()
T9704 001:665.078 JLINK_WriteReg(R0, 0x08005800)
T9704 001:665.086 - 0.007ms returns 0
T9704 001:665.091 JLINK_WriteReg(R1, 0x00000400)
T9704 001:665.094 - 0.003ms returns 0
T9704 001:665.099 JLINK_WriteReg(R2, 0x20000418)
T9704 001:665.102 - 0.003ms returns 0
T9704 001:665.106 JLINK_WriteReg(R3, 0x00000000)
T9704 001:665.110 - 0.003ms returns 0
T9704 001:665.114 JLINK_WriteReg(R4, 0x00000000)
T9704 001:665.117 - 0.003ms returns 0
T9704 001:665.121 JLINK_WriteReg(R5, 0x00000000)
T9704 001:665.125 - 0.003ms returns 0
T9704 001:665.130 JLINK_WriteReg(R6, 0x00000000)
T9704 001:665.136 - 0.005ms returns 0
T9704 001:665.141 JLINK_WriteReg(R7, 0x00000000)
T9704 001:665.147 - 0.005ms returns 0
T9704 001:665.153 JLINK_WriteReg(R8, 0x00000000)
T9704 001:665.163 - 0.010ms returns 0
T9704 001:665.169 JLINK_WriteReg(R9, 0x20000414)
T9704 001:665.174 - 0.005ms returns 0
T9704 001:665.179 JLINK_WriteReg(R10, 0x00000000)
T9704 001:665.182 - 0.004ms returns 0
T9704 001:665.188 JLINK_WriteReg(R11, 0x00000000)
T9704 001:665.193 - 0.005ms returns 0
T9704 001:665.199 JLINK_WriteReg(R12, 0x00000000)
T9704 001:665.204 - 0.005ms returns 0
T9704 001:665.209 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:665.213 - 0.004ms returns 0
T9704 001:665.217 JLINK_WriteReg(R14, 0x20000001)
T9704 001:665.221 - 0.003ms returns 0
T9704 001:665.225 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:665.228 - 0.003ms returns 0
T9704 001:665.232 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:665.236 - 0.003ms returns 0
T9704 001:665.240 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:665.244 - 0.004ms returns 0
T9704 001:665.250 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:665.255 - 0.005ms returns 0
T9704 001:665.261 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:665.264 - 0.003ms returns 0
T9704 001:665.269 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:665.274 - 0.005ms returns 0x0000001B
T9704 001:665.279 JLINK_Go()
T9704 001:665.286   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:666.142   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:675.431 - 10.150ms 
T9704 001:675.447 JLINK_IsHalted()
T9704 001:676.277 - 0.829ms returns FALSE
T9704 001:676.288 JLINK_HasError()
T9704 001:678.844 JLINK_IsHalted()
T9704 001:679.676 - 0.832ms returns FALSE
T9704 001:679.688 JLINK_HasError()
T9704 001:680.982 JLINK_IsHalted()
T9704 001:681.762 - 0.779ms returns FALSE
T9704 001:681.772 JLINK_HasError()
T9704 001:683.126 JLINK_IsHalted()
T9704 001:691.666 - 8.539ms returns TRUE
T9704 001:691.679 JLINK_ReadReg(R15 (PC))
T9704 001:691.687 - 0.008ms returns 0x20000000
T9704 001:691.713 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T9704 001:691.719 - 0.006ms returns 0x00
T9704 001:691.724 JLINK_ReadReg(R0)
T9704 001:691.728 - 0.004ms returns 0x00000000
T9704 001:692.120 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:692.128   Data:  D5 63 00 08 15 64 00 08 87 63 00 08 04 49 88 42 ...
T9704 001:692.141   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:710.207 - 18.086ms returns 0x3E8
T9704 001:710.224 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:710.229   Data:  80 70 08 60 D8 F8 00 00 44 46 40 F0 80 50 C8 F8 ...
T9704 001:710.246   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:711.392 - 1.167ms returns 0x18
T9704 001:711.402 JLINK_HasError()
T9704 001:711.407 JLINK_WriteReg(R0, 0x08005C00)
T9704 001:711.413 - 0.006ms returns 0
T9704 001:711.417 JLINK_WriteReg(R1, 0x00000400)
T9704 001:711.421 - 0.003ms returns 0
T9704 001:711.425 JLINK_WriteReg(R2, 0x20000418)
T9704 001:711.428 - 0.003ms returns 0
T9704 001:711.432 JLINK_WriteReg(R3, 0x00000000)
T9704 001:711.436 - 0.003ms returns 0
T9704 001:711.440 JLINK_WriteReg(R4, 0x00000000)
T9704 001:711.444 - 0.003ms returns 0
T9704 001:711.448 JLINK_WriteReg(R5, 0x00000000)
T9704 001:711.451 - 0.003ms returns 0
T9704 001:711.456 JLINK_WriteReg(R6, 0x00000000)
T9704 001:711.459 - 0.003ms returns 0
T9704 001:711.463 JLINK_WriteReg(R7, 0x00000000)
T9704 001:711.467 - 0.003ms returns 0
T9704 001:711.471 JLINK_WriteReg(R8, 0x00000000)
T9704 001:711.475 - 0.003ms returns 0
T9704 001:711.479 JLINK_WriteReg(R9, 0x20000414)
T9704 001:711.482 - 0.003ms returns 0
T9704 001:711.486 JLINK_WriteReg(R10, 0x00000000)
T9704 001:711.490 - 0.003ms returns 0
T9704 001:711.494 JLINK_WriteReg(R11, 0x00000000)
T9704 001:711.498 - 0.003ms returns 0
T9704 001:711.502 JLINK_WriteReg(R12, 0x00000000)
T9704 001:711.505 - 0.003ms returns 0
T9704 001:711.509 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:711.514 - 0.004ms returns 0
T9704 001:711.518 JLINK_WriteReg(R14, 0x20000001)
T9704 001:711.522 - 0.004ms returns 0
T9704 001:711.526 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:711.530 - 0.003ms returns 0
T9704 001:711.534 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:711.537 - 0.003ms returns 0
T9704 001:711.544 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:711.548 - 0.003ms returns 0
T9704 001:711.552 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:711.556 - 0.003ms returns 0
T9704 001:711.560 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:711.563 - 0.003ms returns 0
T9704 001:711.568 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:711.573 - 0.005ms returns 0x0000001C
T9704 001:711.577 JLINK_Go()
T9704 001:711.584   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:712.380   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:721.988 - 10.410ms 
T9704 001:722.004 JLINK_IsHalted()
T9704 001:722.989 - 0.984ms returns FALSE
T9704 001:723.008 JLINK_HasError()
T9704 001:727.365 JLINK_IsHalted()
T9704 001:728.337 - 0.972ms returns FALSE
T9704 001:728.350 JLINK_HasError()
T9704 001:730.001 JLINK_IsHalted()
T9704 001:738.493 - 8.490ms returns TRUE
T9704 001:738.508 JLINK_ReadReg(R15 (PC))
T9704 001:738.522 - 0.013ms returns 0x20000000
T9704 001:738.529 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T9704 001:738.535 - 0.006ms returns 0x00
T9704 001:738.542 JLINK_ReadReg(R0)
T9704 001:738.548 - 0.005ms returns 0x00000000
T9704 001:739.065 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:739.075   Data:  0D E0 08 68 40 F4 80 00 EF E7 40 F4 00 00 EC E7 ...
T9704 001:739.088   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:757.134 - 18.068ms returns 0x3E8
T9704 001:757.149 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:757.153   Data:  08 31 51 E8 00 2F 22 F0 80 02 41 E8 00 23 00 2B ...
T9704 001:757.167   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:758.392 - 1.242ms returns 0x18
T9704 001:758.403 JLINK_HasError()
T9704 001:758.410 JLINK_WriteReg(R0, 0x08006000)
T9704 001:758.418 - 0.007ms returns 0
T9704 001:758.442 JLINK_WriteReg(R1, 0x00000400)
T9704 001:758.446 - 0.004ms returns 0
T9704 001:758.450 JLINK_WriteReg(R2, 0x20000418)
T9704 001:758.454 - 0.003ms returns 0
T9704 001:758.458 JLINK_WriteReg(R3, 0x00000000)
T9704 001:758.462 - 0.003ms returns 0
T9704 001:758.466 JLINK_WriteReg(R4, 0x00000000)
T9704 001:758.469 - 0.003ms returns 0
T9704 001:758.473 JLINK_WriteReg(R5, 0x00000000)
T9704 001:758.477 - 0.003ms returns 0
T9704 001:758.481 JLINK_WriteReg(R6, 0x00000000)
T9704 001:758.484 - 0.003ms returns 0
T9704 001:758.488 JLINK_WriteReg(R7, 0x00000000)
T9704 001:758.492 - 0.003ms returns 0
T9704 001:758.496 JLINK_WriteReg(R8, 0x00000000)
T9704 001:758.500 - 0.003ms returns 0
T9704 001:758.504 JLINK_WriteReg(R9, 0x20000414)
T9704 001:758.507 - 0.003ms returns 0
T9704 001:758.511 JLINK_WriteReg(R10, 0x00000000)
T9704 001:758.515 - 0.003ms returns 0
T9704 001:758.519 JLINK_WriteReg(R11, 0x00000000)
T9704 001:758.523 - 0.003ms returns 0
T9704 001:758.527 JLINK_WriteReg(R12, 0x00000000)
T9704 001:758.531 - 0.003ms returns 0
T9704 001:758.535 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:758.539 - 0.004ms returns 0
T9704 001:758.543 JLINK_WriteReg(R14, 0x20000001)
T9704 001:758.547 - 0.003ms returns 0
T9704 001:758.551 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:758.554 - 0.003ms returns 0
T9704 001:758.558 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:758.562 - 0.003ms returns 0
T9704 001:758.566 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:758.570 - 0.003ms returns 0
T9704 001:758.574 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:758.578 - 0.003ms returns 0
T9704 001:758.582 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:758.585 - 0.003ms returns 0
T9704 001:758.590 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:758.595 - 0.005ms returns 0x0000001D
T9704 001:758.599 JLINK_Go()
T9704 001:758.606   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:759.376   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:768.629 - 10.029ms 
T9704 001:768.642 JLINK_IsHalted()
T9704 001:769.492 - 0.848ms returns FALSE
T9704 001:769.505 JLINK_HasError()
T9704 001:772.686 JLINK_IsHalted()
T9704 001:773.480 - 0.794ms returns FALSE
T9704 001:773.491 JLINK_HasError()
T9704 001:774.777 JLINK_IsHalted()
T9704 001:775.612 - 0.834ms returns FALSE
T9704 001:775.623 JLINK_HasError()
T9704 001:776.902 JLINK_IsHalted()
T9704 001:785.524 - 8.620ms returns TRUE
T9704 001:785.541 JLINK_ReadReg(R15 (PC))
T9704 001:785.548 - 0.007ms returns 0x20000000
T9704 001:785.576 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T9704 001:785.583 - 0.007ms returns 0x00
T9704 001:785.588 JLINK_ReadReg(R0)
T9704 001:785.592 - 0.003ms returns 0x00000000
T9704 001:786.929 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:786.939   Data:  41 F0 40 01 42 E8 00 13 00 2B F6 D1 10 BD FF F7 ...
T9704 001:786.958   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:804.950 - 18.020ms returns 0x3E8
T9704 001:804.968 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:804.974   Data:  01 25 06 E0 39 46 ED E7 19 46 EB E7 21 68 80 B2 ...
T9704 001:804.993   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:806.184 - 1.216ms returns 0x18
T9704 001:806.194 JLINK_HasError()
T9704 001:806.200 JLINK_WriteReg(R0, 0x08006400)
T9704 001:806.206 - 0.005ms returns 0
T9704 001:806.210 JLINK_WriteReg(R1, 0x00000400)
T9704 001:806.214 - 0.003ms returns 0
T9704 001:806.218 JLINK_WriteReg(R2, 0x20000418)
T9704 001:806.222 - 0.003ms returns 0
T9704 001:806.226 JLINK_WriteReg(R3, 0x00000000)
T9704 001:806.229 - 0.003ms returns 0
T9704 001:806.237 JLINK_WriteReg(R4, 0x00000000)
T9704 001:806.241 - 0.003ms returns 0
T9704 001:806.245 JLINK_WriteReg(R5, 0x00000000)
T9704 001:806.248 - 0.003ms returns 0
T9704 001:806.252 JLINK_WriteReg(R6, 0x00000000)
T9704 001:806.256 - 0.003ms returns 0
T9704 001:806.260 JLINK_WriteReg(R7, 0x00000000)
T9704 001:806.264 - 0.003ms returns 0
T9704 001:806.268 JLINK_WriteReg(R8, 0x00000000)
T9704 001:806.271 - 0.003ms returns 0
T9704 001:806.276 JLINK_WriteReg(R9, 0x20000414)
T9704 001:806.279 - 0.003ms returns 0
T9704 001:806.283 JLINK_WriteReg(R10, 0x00000000)
T9704 001:806.287 - 0.003ms returns 0
T9704 001:806.291 JLINK_WriteReg(R11, 0x00000000)
T9704 001:806.295 - 0.003ms returns 0
T9704 001:806.299 JLINK_WriteReg(R12, 0x00000000)
T9704 001:806.302 - 0.003ms returns 0
T9704 001:806.306 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:806.310 - 0.004ms returns 0
T9704 001:806.314 JLINK_WriteReg(R14, 0x20000001)
T9704 001:806.318 - 0.003ms returns 0
T9704 001:806.322 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:806.326 - 0.003ms returns 0
T9704 001:806.330 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:806.334 - 0.003ms returns 0
T9704 001:806.338 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:806.342 - 0.003ms returns 0
T9704 001:806.346 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:806.349 - 0.003ms returns 0
T9704 001:806.353 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:806.357 - 0.003ms returns 0
T9704 001:806.362 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:806.367 - 0.005ms returns 0x0000001E
T9704 001:806.371 JLINK_Go()
T9704 001:806.378   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:807.187   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:816.629 - 10.257ms 
T9704 001:816.646 JLINK_IsHalted()
T9704 001:817.377 - 0.730ms returns FALSE
T9704 001:817.392 JLINK_HasError()
T9704 001:821.460 JLINK_IsHalted()
T9704 001:822.373 - 0.912ms returns FALSE
T9704 001:822.382 JLINK_HasError()
T9704 001:823.492 JLINK_IsHalted()
T9704 001:824.271 - 0.778ms returns FALSE
T9704 001:824.281 JLINK_HasError()
T9704 001:825.606 JLINK_IsHalted()
T9704 001:834.144 - 8.536ms returns TRUE
T9704 001:834.156 JLINK_ReadReg(R15 (PC))
T9704 001:834.162 - 0.006ms returns 0x20000000
T9704 001:834.167 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T9704 001:834.172 - 0.004ms returns 0x00
T9704 001:834.176 JLINK_ReadReg(R0)
T9704 001:834.180 - 0.003ms returns 0x00000000
T9704 001:834.654 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:834.664   Data:  A4 F8 68 00 28 46 66 67 A6 67 07 B0 BD E8 F0 83 ...
T9704 001:834.679   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:852.760 - 18.105ms returns 0x3E8
T9704 001:852.776 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:852.781   Data:  9F ED 0D 0A 20 EE 80 0A 01 EE 21 0A 9F ED 0C 1A ...
T9704 001:852.797   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:854.045 - 1.268ms returns 0x18
T9704 001:854.055 JLINK_HasError()
T9704 001:854.079 JLINK_WriteReg(R0, 0x08006800)
T9704 001:854.085 - 0.006ms returns 0
T9704 001:854.090 JLINK_WriteReg(R1, 0x00000400)
T9704 001:854.094 - 0.003ms returns 0
T9704 001:854.098 JLINK_WriteReg(R2, 0x20000418)
T9704 001:854.101 - 0.003ms returns 0
T9704 001:854.105 JLINK_WriteReg(R3, 0x00000000)
T9704 001:854.109 - 0.003ms returns 0
T9704 001:854.113 JLINK_WriteReg(R4, 0x00000000)
T9704 001:854.116 - 0.003ms returns 0
T9704 001:854.120 JLINK_WriteReg(R5, 0x00000000)
T9704 001:854.124 - 0.003ms returns 0
T9704 001:854.128 JLINK_WriteReg(R6, 0x00000000)
T9704 001:854.132 - 0.003ms returns 0
T9704 001:854.136 JLINK_WriteReg(R7, 0x00000000)
T9704 001:854.140 - 0.003ms returns 0
T9704 001:854.144 JLINK_WriteReg(R8, 0x00000000)
T9704 001:854.147 - 0.003ms returns 0
T9704 001:854.151 JLINK_WriteReg(R9, 0x20000414)
T9704 001:854.155 - 0.003ms returns 0
T9704 001:854.159 JLINK_WriteReg(R10, 0x00000000)
T9704 001:854.162 - 0.003ms returns 0
T9704 001:854.167 JLINK_WriteReg(R11, 0x00000000)
T9704 001:854.172 - 0.004ms returns 0
T9704 001:854.178 JLINK_WriteReg(R12, 0x00000000)
T9704 001:854.183 - 0.005ms returns 0
T9704 001:854.189 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:854.195 - 0.005ms returns 0
T9704 001:854.200 JLINK_WriteReg(R14, 0x20000001)
T9704 001:854.204 - 0.003ms returns 0
T9704 001:854.208 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:854.212 - 0.003ms returns 0
T9704 001:854.216 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:854.219 - 0.003ms returns 0
T9704 001:854.223 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:854.227 - 0.003ms returns 0
T9704 001:854.231 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:854.235 - 0.003ms returns 0
T9704 001:854.239 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:854.242 - 0.003ms returns 0
T9704 001:854.247 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:854.252 - 0.005ms returns 0x0000001F
T9704 001:854.256 JLINK_Go()
T9704 001:854.263   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:855.045   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:864.223 - 9.966ms 
T9704 001:864.236 JLINK_IsHalted()
T9704 001:865.119 - 0.881ms returns FALSE
T9704 001:865.135 JLINK_HasError()
T9704 001:868.962 JLINK_IsHalted()
T9704 001:869.902 - 0.940ms returns FALSE
T9704 001:869.912 JLINK_HasError()
T9704 001:871.104 JLINK_IsHalted()
T9704 001:872.052 - 0.947ms returns FALSE
T9704 001:872.062 JLINK_HasError()
T9704 001:873.320 JLINK_IsHalted()
T9704 001:881.980 - 8.659ms returns TRUE
T9704 001:881.994 JLINK_ReadReg(R15 (PC))
T9704 001:882.001 - 0.006ms returns 0x20000000
T9704 001:882.025 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T9704 001:882.029 - 0.004ms returns 0x00
T9704 001:882.033 JLINK_ReadReg(R0)
T9704 001:882.037 - 0.003ms returns 0x00000000
T9704 001:882.404 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:882.413   Data:  F0 01 70 47 CC F1 80 51 01 60 BD E8 F0 01 B1 EE ...
T9704 001:882.426   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:900.495 - 18.089ms returns 0x3E8
T9704 001:900.508 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:900.512   Data:  24 A0 00 F0 AD F8 FC F7 2F FE 01 46 27 A0 00 F0 ...
T9704 001:900.526   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:901.748 - 1.239ms returns 0x18
T9704 001:901.765 JLINK_HasError()
T9704 001:901.774 JLINK_WriteReg(R0, 0x08006C00)
T9704 001:901.788 - 0.013ms returns 0
T9704 001:901.794 JLINK_WriteReg(R1, 0x00000400)
T9704 001:901.800 - 0.005ms returns 0
T9704 001:901.806 JLINK_WriteReg(R2, 0x20000418)
T9704 001:901.811 - 0.004ms returns 0
T9704 001:901.817 JLINK_WriteReg(R3, 0x00000000)
T9704 001:901.822 - 0.005ms returns 0
T9704 001:901.828 JLINK_WriteReg(R4, 0x00000000)
T9704 001:901.833 - 0.005ms returns 0
T9704 001:901.839 JLINK_WriteReg(R5, 0x00000000)
T9704 001:901.845 - 0.005ms returns 0
T9704 001:901.850 JLINK_WriteReg(R6, 0x00000000)
T9704 001:901.855 - 0.005ms returns 0
T9704 001:901.860 JLINK_WriteReg(R7, 0x00000000)
T9704 001:901.868 - 0.008ms returns 0
T9704 001:901.876 JLINK_WriteReg(R8, 0x00000000)
T9704 001:901.881 - 0.005ms returns 0
T9704 001:901.887 JLINK_WriteReg(R9, 0x20000414)
T9704 001:901.892 - 0.005ms returns 0
T9704 001:901.898 JLINK_WriteReg(R10, 0x00000000)
T9704 001:901.903 - 0.005ms returns 0
T9704 001:901.908 JLINK_WriteReg(R11, 0x00000000)
T9704 001:901.914 - 0.005ms returns 0
T9704 001:901.919 JLINK_WriteReg(R12, 0x00000000)
T9704 001:901.925 - 0.005ms returns 0
T9704 001:901.932 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:901.938 - 0.006ms returns 0
T9704 001:901.944 JLINK_WriteReg(R14, 0x20000001)
T9704 001:901.949 - 0.005ms returns 0
T9704 001:901.954 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:901.960 - 0.005ms returns 0
T9704 001:901.966 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:901.971 - 0.005ms returns 0
T9704 001:901.976 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:901.982 - 0.005ms returns 0
T9704 001:901.987 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:901.993 - 0.005ms returns 0
T9704 001:901.998 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:902.004 - 0.006ms returns 0
T9704 001:902.011 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:902.018 - 0.007ms returns 0x00000020
T9704 001:902.024 JLINK_Go()
T9704 001:902.036   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:902.823   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:912.449 - 10.424ms 
T9704 001:912.466 JLINK_IsHalted()
T9704 001:913.270 - 0.803ms returns FALSE
T9704 001:913.284 JLINK_HasError()
T9704 001:916.906 JLINK_IsHalted()
T9704 001:917.696 - 0.789ms returns FALSE
T9704 001:917.711 JLINK_HasError()
T9704 001:919.105 JLINK_IsHalted()
T9704 001:919.976 - 0.870ms returns FALSE
T9704 001:920.075 JLINK_HasError()
T9704 001:921.254 JLINK_IsHalted()
T9704 001:929.759 - 8.504ms returns TRUE
T9704 001:929.776 JLINK_ReadReg(R15 (PC))
T9704 001:929.785 - 0.008ms returns 0x20000000
T9704 001:929.792 JLINK_ClrBPEx(BPHandle = 0x00000020)
T9704 001:929.799 - 0.006ms returns 0x00
T9704 001:929.805 JLINK_ReadReg(R0)
T9704 001:929.811 - 0.005ms returns 0x00000000
T9704 001:930.282 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:930.292   Data:  29 A0 00 F0 A1 F8 FC F7 1B FE 01 46 2C A0 00 F0 ...
T9704 001:930.308   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:948.382 - 18.099ms returns 0x3E8
T9704 001:948.396 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:948.402   Data:  FF F7 AE FE F9 E7 00 00 54 65 73 74 69 6E 67 20 ...
T9704 001:948.416   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:949.617 - 1.220ms returns 0x18
T9704 001:949.629 JLINK_HasError()
T9704 001:949.653 JLINK_WriteReg(R0, 0x08007000)
T9704 001:949.660 - 0.006ms returns 0
T9704 001:949.664 JLINK_WriteReg(R1, 0x00000400)
T9704 001:949.668 - 0.003ms returns 0
T9704 001:949.672 JLINK_WriteReg(R2, 0x20000418)
T9704 001:949.675 - 0.003ms returns 0
T9704 001:949.680 JLINK_WriteReg(R3, 0x00000000)
T9704 001:949.683 - 0.003ms returns 0
T9704 001:949.687 JLINK_WriteReg(R4, 0x00000000)
T9704 001:949.691 - 0.003ms returns 0
T9704 001:949.695 JLINK_WriteReg(R5, 0x00000000)
T9704 001:949.698 - 0.003ms returns 0
T9704 001:949.702 JLINK_WriteReg(R6, 0x00000000)
T9704 001:949.706 - 0.004ms returns 0
T9704 001:949.712 JLINK_WriteReg(R7, 0x00000000)
T9704 001:949.718 - 0.005ms returns 0
T9704 001:949.724 JLINK_WriteReg(R8, 0x00000000)
T9704 001:949.727 - 0.003ms returns 0
T9704 001:949.731 JLINK_WriteReg(R9, 0x20000414)
T9704 001:949.735 - 0.003ms returns 0
T9704 001:949.740 JLINK_WriteReg(R10, 0x00000000)
T9704 001:949.745 - 0.005ms returns 0
T9704 001:949.751 JLINK_WriteReg(R11, 0x00000000)
T9704 001:949.757 - 0.006ms returns 0
T9704 001:949.763 JLINK_WriteReg(R12, 0x00000000)
T9704 001:949.769 - 0.005ms returns 0
T9704 001:949.773 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:949.777 - 0.004ms returns 0
T9704 001:949.782 JLINK_WriteReg(R14, 0x20000001)
T9704 001:949.785 - 0.003ms returns 0
T9704 001:949.789 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:949.793 - 0.003ms returns 0
T9704 001:949.800 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:949.804 - 0.005ms returns 0
T9704 001:949.809 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:949.812 - 0.003ms returns 0
T9704 001:949.816 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:949.820 - 0.003ms returns 0
T9704 001:949.824 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:949.828 - 0.003ms returns 0
T9704 001:949.833 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:949.838 - 0.005ms returns 0x00000021
T9704 001:949.842 JLINK_Go()
T9704 001:949.850   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:950.752   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 001:960.223 - 10.380ms 
T9704 001:960.235 JLINK_IsHalted()
T9704 001:961.054 - 0.818ms returns FALSE
T9704 001:961.062 JLINK_HasError()
T9704 001:964.586 JLINK_IsHalted()
T9704 001:965.378 - 0.792ms returns FALSE
T9704 001:965.386 JLINK_HasError()
T9704 001:966.672 JLINK_IsHalted()
T9704 001:967.478 - 0.805ms returns FALSE
T9704 001:967.489 JLINK_HasError()
T9704 001:968.789 JLINK_IsHalted()
T9704 001:977.418 - 8.628ms returns TRUE
T9704 001:977.432 JLINK_ReadReg(R15 (PC))
T9704 001:977.438 - 0.006ms returns 0x20000000
T9704 001:977.460 JLINK_ClrBPEx(BPHandle = 0x00000021)
T9704 001:977.464 - 0.004ms returns 0x00
T9704 001:977.469 JLINK_ReadReg(R0)
T9704 001:977.473 - 0.003ms returns 0x00000000
T9704 001:977.814 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 001:977.822   Data:  63 74 69 6F 6E 61 6C 69 74 79 2E 2E 2E 0D 0A 00 ...
T9704 001:977.835   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 001:995.881 - 18.066ms returns 0x3E8
T9704 001:995.893 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 001:995.898   Data:  4D 58 5F 47 50 49 4F 5F 49 6E 69 74 3A 20 4F 4B ...
T9704 001:995.911   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 001:997.042 - 1.148ms returns 0x18
T9704 001:997.050 JLINK_HasError()
T9704 001:997.056 JLINK_WriteReg(R0, 0x08007400)
T9704 001:997.062 - 0.005ms returns 0
T9704 001:997.067 JLINK_WriteReg(R1, 0x00000400)
T9704 001:997.073 - 0.006ms returns 0
T9704 001:997.080 JLINK_WriteReg(R2, 0x20000418)
T9704 001:997.085 - 0.005ms returns 0
T9704 001:997.089 JLINK_WriteReg(R3, 0x00000000)
T9704 001:997.093 - 0.003ms returns 0
T9704 001:997.097 JLINK_WriteReg(R4, 0x00000000)
T9704 001:997.101 - 0.003ms returns 0
T9704 001:997.105 JLINK_WriteReg(R5, 0x00000000)
T9704 001:997.108 - 0.003ms returns 0
T9704 001:997.112 JLINK_WriteReg(R6, 0x00000000)
T9704 001:997.116 - 0.003ms returns 0
T9704 001:997.120 JLINK_WriteReg(R7, 0x00000000)
T9704 001:997.124 - 0.003ms returns 0
T9704 001:997.128 JLINK_WriteReg(R8, 0x00000000)
T9704 001:997.131 - 0.003ms returns 0
T9704 001:997.135 JLINK_WriteReg(R9, 0x20000414)
T9704 001:997.139 - 0.003ms returns 0
T9704 001:997.143 JLINK_WriteReg(R10, 0x00000000)
T9704 001:997.147 - 0.003ms returns 0
T9704 001:997.151 JLINK_WriteReg(R11, 0x00000000)
T9704 001:997.154 - 0.003ms returns 0
T9704 001:997.159 JLINK_WriteReg(R12, 0x00000000)
T9704 001:997.162 - 0.003ms returns 0
T9704 001:997.166 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 001:997.170 - 0.004ms returns 0
T9704 001:997.175 JLINK_WriteReg(R14, 0x20000001)
T9704 001:997.178 - 0.003ms returns 0
T9704 001:997.182 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 001:997.186 - 0.003ms returns 0
T9704 001:997.190 JLINK_WriteReg(XPSR, 0x01000000)
T9704 001:997.194 - 0.003ms returns 0
T9704 001:997.198 JLINK_WriteReg(MSP, 0x20008000)
T9704 001:997.201 - 0.003ms returns 0
T9704 001:997.206 JLINK_WriteReg(PSP, 0x20008000)
T9704 001:997.209 - 0.003ms returns 0
T9704 001:997.213 JLINK_WriteReg(CFBP, 0x00000000)
T9704 001:997.217 - 0.003ms returns 0
T9704 001:997.222 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 001:997.227 - 0.005ms returns 0x00000022
T9704 001:997.231 JLINK_Go()
T9704 001:997.238   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 001:998.047   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 002:007.122 - 9.889ms 
T9704 002:007.139 JLINK_IsHalted()
T9704 002:008.015 - 0.875ms returns FALSE
T9704 002:008.025 JLINK_HasError()
T9704 002:011.322 JLINK_IsHalted()
T9704 002:012.116 - 0.794ms returns FALSE
T9704 002:012.126 JLINK_HasError()
T9704 002:013.421 JLINK_IsHalted()
T9704 002:014.245 - 0.823ms returns FALSE
T9704 002:014.254 JLINK_HasError()
T9704 002:015.534 JLINK_IsHalted()
T9704 002:024.103 - 8.567ms returns TRUE
T9704 002:024.116 JLINK_ReadReg(R15 (PC))
T9704 002:024.123 - 0.006ms returns 0x20000000
T9704 002:024.127 JLINK_ClrBPEx(BPHandle = 0x00000022)
T9704 002:024.132 - 0.004ms returns 0x00
T9704 002:024.136 JLINK_ReadReg(R0)
T9704 002:024.140 - 0.004ms returns 0x00000000
T9704 002:024.530 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 002:024.539   Data:  44 43 31 5F 49 6E 69 74 3A 20 4F 4B 0D 0A 00 00 ...
T9704 002:024.553   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 002:042.563 - 18.032ms returns 0x3E8
T9704 002:042.578 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 002:042.582   Data:  43 04 04 EB C3 05 01 24 DF ED 30 0A 06 EB 85 05 ...
T9704 002:042.596   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 002:043.795 - 1.216ms returns 0x18
T9704 002:043.805 JLINK_HasError()
T9704 002:043.829 JLINK_WriteReg(R0, 0x08007800)
T9704 002:043.835 - 0.006ms returns 0
T9704 002:043.839 JLINK_WriteReg(R1, 0x00000400)
T9704 002:043.843 - 0.003ms returns 0
T9704 002:043.847 JLINK_WriteReg(R2, 0x20000418)
T9704 002:043.851 - 0.003ms returns 0
T9704 002:043.855 JLINK_WriteReg(R3, 0x00000000)
T9704 002:043.859 - 0.003ms returns 0
T9704 002:043.863 JLINK_WriteReg(R4, 0x00000000)
T9704 002:043.866 - 0.003ms returns 0
T9704 002:043.870 JLINK_WriteReg(R5, 0x00000000)
T9704 002:043.874 - 0.003ms returns 0
T9704 002:043.878 JLINK_WriteReg(R6, 0x00000000)
T9704 002:043.882 - 0.003ms returns 0
T9704 002:043.886 JLINK_WriteReg(R7, 0x00000000)
T9704 002:043.889 - 0.003ms returns 0
T9704 002:043.894 JLINK_WriteReg(R8, 0x00000000)
T9704 002:043.897 - 0.003ms returns 0
T9704 002:043.901 JLINK_WriteReg(R9, 0x20000414)
T9704 002:043.905 - 0.003ms returns 0
T9704 002:043.909 JLINK_WriteReg(R10, 0x00000000)
T9704 002:043.912 - 0.003ms returns 0
T9704 002:043.917 JLINK_WriteReg(R11, 0x00000000)
T9704 002:043.920 - 0.003ms returns 0
T9704 002:043.924 JLINK_WriteReg(R12, 0x00000000)
T9704 002:043.928 - 0.003ms returns 0
T9704 002:043.932 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 002:043.936 - 0.004ms returns 0
T9704 002:043.940 JLINK_WriteReg(R14, 0x20000001)
T9704 002:043.944 - 0.003ms returns 0
T9704 002:043.948 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 002:043.952 - 0.003ms returns 0
T9704 002:043.956 JLINK_WriteReg(XPSR, 0x01000000)
T9704 002:043.960 - 0.003ms returns 0
T9704 002:043.964 JLINK_WriteReg(MSP, 0x20008000)
T9704 002:043.970 - 0.005ms returns 0
T9704 002:043.976 JLINK_WriteReg(PSP, 0x20008000)
T9704 002:043.980 - 0.005ms returns 0
T9704 002:043.985 JLINK_WriteReg(CFBP, 0x00000000)
T9704 002:043.988 - 0.003ms returns 0
T9704 002:043.993 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 002:043.998 - 0.005ms returns 0x00000023
T9704 002:044.002 JLINK_Go()
T9704 002:044.009   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 002:044.795   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 002:054.124 - 10.121ms 
T9704 002:054.136 JLINK_IsHalted()
T9704 002:054.925 - 0.788ms returns FALSE
T9704 002:054.937 JLINK_HasError()
T9704 002:058.597 JLINK_IsHalted()
T9704 002:059.368 - 0.771ms returns FALSE
T9704 002:059.379 JLINK_HasError()
T9704 002:060.703 JLINK_IsHalted()
T9704 002:061.479 - 0.776ms returns FALSE
T9704 002:061.490 JLINK_HasError()
T9704 002:062.826 JLINK_IsHalted()
T9704 002:071.541 - 8.713ms returns TRUE
T9704 002:071.560 JLINK_ReadReg(R15 (PC))
T9704 002:071.569 - 0.009ms returns 0x20000000
T9704 002:071.575 JLINK_ClrBPEx(BPHandle = 0x00000023)
T9704 002:071.581 - 0.006ms returns 0x00
T9704 002:071.612 JLINK_ReadReg(R0)
T9704 002:071.618 - 0.006ms returns 0x00000000
T9704 002:072.680 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 002:072.693   Data:  05 EB 84 07 00 EB 8C 0C 57 ED 0B 1A 9C ED 00 2A ...
T9704 002:072.715   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 002:090.810 - 18.129ms returns 0x3E8
T9704 002:090.822 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 002:090.827   Data:  00 00 00 00 00 00 00 02 00 01 00 00 10 00 00 00 ...
T9704 002:090.840   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 002:091.947 - 1.123ms returns 0x18
T9704 002:091.956 JLINK_HasError()
T9704 002:091.962 JLINK_WriteReg(R0, 0x08007C00)
T9704 002:091.968 - 0.006ms returns 0
T9704 002:091.973 JLINK_WriteReg(R1, 0x00000400)
T9704 002:091.977 - 0.003ms returns 0
T9704 002:091.981 JLINK_WriteReg(R2, 0x20000418)
T9704 002:091.984 - 0.003ms returns 0
T9704 002:091.988 JLINK_WriteReg(R3, 0x00000000)
T9704 002:091.992 - 0.003ms returns 0
T9704 002:091.996 JLINK_WriteReg(R4, 0x00000000)
T9704 002:092.000 - 0.003ms returns 0
T9704 002:092.004 JLINK_WriteReg(R5, 0x00000000)
T9704 002:092.007 - 0.003ms returns 0
T9704 002:092.011 JLINK_WriteReg(R6, 0x00000000)
T9704 002:092.015 - 0.003ms returns 0
T9704 002:092.019 JLINK_WriteReg(R7, 0x00000000)
T9704 002:092.023 - 0.003ms returns 0
T9704 002:092.027 JLINK_WriteReg(R8, 0x00000000)
T9704 002:092.030 - 0.003ms returns 0
T9704 002:092.034 JLINK_WriteReg(R9, 0x20000414)
T9704 002:092.038 - 0.003ms returns 0
T9704 002:092.042 JLINK_WriteReg(R10, 0x00000000)
T9704 002:092.046 - 0.003ms returns 0
T9704 002:092.050 JLINK_WriteReg(R11, 0x00000000)
T9704 002:092.054 - 0.003ms returns 0
T9704 002:092.058 JLINK_WriteReg(R12, 0x00000000)
T9704 002:092.061 - 0.003ms returns 0
T9704 002:092.065 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 002:092.070 - 0.004ms returns 0
T9704 002:092.074 JLINK_WriteReg(R14, 0x20000001)
T9704 002:092.077 - 0.003ms returns 0
T9704 002:092.082 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 002:092.085 - 0.003ms returns 0
T9704 002:092.090 JLINK_WriteReg(XPSR, 0x01000000)
T9704 002:092.093 - 0.003ms returns 0
T9704 002:092.097 JLINK_WriteReg(MSP, 0x20008000)
T9704 002:092.101 - 0.003ms returns 0
T9704 002:092.105 JLINK_WriteReg(PSP, 0x20008000)
T9704 002:092.109 - 0.003ms returns 0
T9704 002:092.113 JLINK_WriteReg(CFBP, 0x00000000)
T9704 002:092.116 - 0.003ms returns 0
T9704 002:092.121 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 002:092.126 - 0.005ms returns 0x00000024
T9704 002:092.130 JLINK_Go()
T9704 002:092.136   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 002:092.993   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 002:102.383 - 10.252ms 
T9704 002:102.398 JLINK_IsHalted()
T9704 002:103.230 - 0.830ms returns FALSE
T9704 002:103.246 JLINK_HasError()
T9704 002:108.328 JLINK_IsHalted()
T9704 002:109.141 - 0.812ms returns FALSE
T9704 002:109.153 JLINK_HasError()
T9704 002:110.476 JLINK_IsHalted()
T9704 002:119.041 - 8.563ms returns TRUE
T9704 002:119.057 JLINK_ReadReg(R15 (PC))
T9704 002:119.066 - 0.008ms returns 0x20000000
T9704 002:119.072 JLINK_ClrBPEx(BPHandle = 0x00000024)
T9704 002:119.078 - 0.005ms returns 0x00
T9704 002:119.084 JLINK_ReadReg(R0)
T9704 002:119.090 - 0.005ms returns 0x00000000
T9704 002:119.869 JLINK_WriteMem(0x20000418, 0x3E8 Bytes, ...)
T9704 002:119.878   Data:  29 15 44 4E D1 57 27 FC C0 DD 34 F5 99 95 62 DB ...
T9704 002:119.892   CPU_WriteMem(1000 bytes @ 0x20000418)
T9704 002:137.947 - 18.077ms returns 0x3E8
T9704 002:137.964 JLINK_WriteMem(0x20000800, 0x18 Bytes, ...)
T9704 002:137.969   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
T9704 002:137.983   CPU_WriteMem(24 bytes @ 0x20000800)
T9704 002:139.119 - 1.154ms returns 0x18
T9704 002:139.130 JLINK_HasError()
T9704 002:139.155 JLINK_WriteReg(R0, 0x08008000)
T9704 002:139.161 - 0.006ms returns 0
T9704 002:139.165 JLINK_WriteReg(R1, 0x00000278)
T9704 002:139.169 - 0.003ms returns 0
T9704 002:139.173 JLINK_WriteReg(R2, 0x20000418)
T9704 002:139.176 - 0.003ms returns 0
T9704 002:139.181 JLINK_WriteReg(R3, 0x00000000)
T9704 002:139.184 - 0.003ms returns 0
T9704 002:139.188 JLINK_WriteReg(R4, 0x00000000)
T9704 002:139.192 - 0.003ms returns 0
T9704 002:139.196 JLINK_WriteReg(R5, 0x00000000)
T9704 002:139.200 - 0.003ms returns 0
T9704 002:139.204 JLINK_WriteReg(R6, 0x00000000)
T9704 002:139.210 - 0.005ms returns 0
T9704 002:139.215 JLINK_WriteReg(R7, 0x00000000)
T9704 002:139.218 - 0.003ms returns 0
T9704 002:139.222 JLINK_WriteReg(R8, 0x00000000)
T9704 002:139.226 - 0.003ms returns 0
T9704 002:139.230 JLINK_WriteReg(R9, 0x20000414)
T9704 002:139.248 - 0.017ms returns 0
T9704 002:139.252 JLINK_WriteReg(R10, 0x00000000)
T9704 002:139.256 - 0.003ms returns 0
T9704 002:139.260 JLINK_WriteReg(R11, 0x00000000)
T9704 002:139.264 - 0.003ms returns 0
T9704 002:139.268 JLINK_WriteReg(R12, 0x00000000)
T9704 002:139.272 - 0.003ms returns 0
T9704 002:139.276 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 002:139.280 - 0.004ms returns 0
T9704 002:139.284 JLINK_WriteReg(R14, 0x20000001)
T9704 002:139.288 - 0.003ms returns 0
T9704 002:139.292 JLINK_WriteReg(R15 (PC), 0x2000029A)
T9704 002:139.296 - 0.003ms returns 0
T9704 002:139.300 JLINK_WriteReg(XPSR, 0x01000000)
T9704 002:139.304 - 0.004ms returns 0
T9704 002:139.308 JLINK_WriteReg(MSP, 0x20008000)
T9704 002:139.311 - 0.003ms returns 0
T9704 002:139.315 JLINK_WriteReg(PSP, 0x20008000)
T9704 002:139.319 - 0.003ms returns 0
T9704 002:139.323 JLINK_WriteReg(CFBP, 0x00000000)
T9704 002:139.327 - 0.003ms returns 0
T9704 002:139.331 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 002:139.336 - 0.004ms returns 0x00000025
T9704 002:139.340 JLINK_Go()
T9704 002:139.347   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 002:140.163   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 002:149.619 - 10.277ms 
T9704 002:149.631 JLINK_IsHalted()
T9704 002:150.544 - 0.912ms returns FALSE
T9704 002:150.554 JLINK_HasError()
T9704 002:155.271 JLINK_IsHalted()
T9704 002:163.920 - 8.649ms returns TRUE
T9704 002:163.933 JLINK_ReadReg(R15 (PC))
T9704 002:163.940 - 0.006ms returns 0x20000000
T9704 002:163.944 JLINK_ClrBPEx(BPHandle = 0x00000025)
T9704 002:163.949 - 0.004ms returns 0x00
T9704 002:163.953 JLINK_ReadReg(R0)
T9704 002:163.957 - 0.003ms returns 0x00000000
T9704 002:163.962 JLINK_HasError()
T9704 002:163.967 JLINK_WriteReg(R0, 0x00000002)
T9704 002:163.971 - 0.004ms returns 0
T9704 002:163.975 JLINK_WriteReg(R1, 0x00000278)
T9704 002:163.978 - 0.003ms returns 0
T9704 002:163.983 JLINK_WriteReg(R2, 0x20000418)
T9704 002:163.986 - 0.003ms returns 0
T9704 002:163.990 JLINK_WriteReg(R3, 0x00000000)
T9704 002:163.994 - 0.003ms returns 0
T9704 002:163.998 JLINK_WriteReg(R4, 0x00000000)
T9704 002:164.001 - 0.003ms returns 0
T9704 002:164.005 JLINK_WriteReg(R5, 0x00000000)
T9704 002:164.009 - 0.003ms returns 0
T9704 002:164.013 JLINK_WriteReg(R6, 0x00000000)
T9704 002:164.017 - 0.003ms returns 0
T9704 002:164.021 JLINK_WriteReg(R7, 0x00000000)
T9704 002:164.025 - 0.003ms returns 0
T9704 002:164.029 JLINK_WriteReg(R8, 0x00000000)
T9704 002:164.032 - 0.003ms returns 0
T9704 002:164.036 JLINK_WriteReg(R9, 0x20000414)
T9704 002:164.040 - 0.003ms returns 0
T9704 002:164.044 JLINK_WriteReg(R10, 0x00000000)
T9704 002:164.048 - 0.003ms returns 0
T9704 002:164.052 JLINK_WriteReg(R11, 0x00000000)
T9704 002:164.055 - 0.003ms returns 0
T9704 002:164.059 JLINK_WriteReg(R12, 0x00000000)
T9704 002:164.063 - 0.003ms returns 0
T9704 002:164.067 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 002:164.071 - 0.004ms returns 0
T9704 002:164.075 JLINK_WriteReg(R14, 0x20000001)
T9704 002:164.079 - 0.003ms returns 0
T9704 002:164.083 JLINK_WriteReg(R15 (PC), 0x2000009C)
T9704 002:164.086 - 0.003ms returns 0
T9704 002:164.091 JLINK_WriteReg(XPSR, 0x01000000)
T9704 002:164.094 - 0.003ms returns 0
T9704 002:164.098 JLINK_WriteReg(MSP, 0x20008000)
T9704 002:164.102 - 0.003ms returns 0
T9704 002:164.106 JLINK_WriteReg(PSP, 0x20008000)
T9704 002:164.110 - 0.003ms returns 0
T9704 002:164.114 JLINK_WriteReg(CFBP, 0x00000000)
T9704 002:164.117 - 0.003ms returns 0
T9704 002:164.122 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 002:164.127 - 0.004ms returns 0x00000026
T9704 002:164.131 JLINK_Go()
T9704 002:164.139   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 002:165.050   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 002:174.274 - 10.141ms 
T9704 002:174.296 JLINK_IsHalted()
T9704 002:182.845 - 8.548ms returns TRUE
T9704 002:182.860 JLINK_ReadReg(R15 (PC))
T9704 002:182.867 - 0.006ms returns 0x20000000
T9704 002:182.890 JLINK_ClrBPEx(BPHandle = 0x00000026)
T9704 002:182.895 - 0.004ms returns 0x00
T9704 002:182.899 JLINK_ReadReg(R0)
T9704 002:182.903 - 0.003ms returns 0x00000000
T9704 002:240.192 JLINK_WriteMem(0x20000000, 0x418 Bytes, ...)
T9704 002:240.202   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T9704 002:240.218   CPU_WriteMem(1048 bytes @ 0x20000000)
T9704 002:259.380 - 19.187ms returns 0x418
T9704 002:259.417 JLINK_HasError()
T9704 002:259.424 JLINK_WriteReg(R0, 0x08000000)
T9704 002:259.434 - 0.009ms returns 0
T9704 002:259.438 JLINK_WriteReg(R1, 0x017D7840)
T9704 002:259.442 - 0.003ms returns 0
T9704 002:259.446 JLINK_WriteReg(R2, 0x00000003)
T9704 002:259.450 - 0.003ms returns 0
T9704 002:259.454 JLINK_WriteReg(R3, 0x00000000)
T9704 002:259.458 - 0.003ms returns 0
T9704 002:259.462 JLINK_WriteReg(R4, 0x00000000)
T9704 002:259.465 - 0.003ms returns 0
T9704 002:259.469 JLINK_WriteReg(R5, 0x00000000)
T9704 002:259.473 - 0.003ms returns 0
T9704 002:259.477 JLINK_WriteReg(R6, 0x00000000)
T9704 002:259.481 - 0.004ms returns 0
T9704 002:259.485 JLINK_WriteReg(R7, 0x00000000)
T9704 002:259.488 - 0.003ms returns 0
T9704 002:259.493 JLINK_WriteReg(R8, 0x00000000)
T9704 002:259.496 - 0.003ms returns 0
T9704 002:259.502 JLINK_WriteReg(R9, 0x20000414)
T9704 002:259.507 - 0.005ms returns 0
T9704 002:259.513 JLINK_WriteReg(R10, 0x00000000)
T9704 002:259.518 - 0.005ms returns 0
T9704 002:259.524 JLINK_WriteReg(R11, 0x00000000)
T9704 002:259.530 - 0.005ms returns 0
T9704 002:259.535 JLINK_WriteReg(R12, 0x00000000)
T9704 002:259.541 - 0.005ms returns 0
T9704 002:259.546 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 002:259.551 - 0.004ms returns 0
T9704 002:259.555 JLINK_WriteReg(R14, 0x20000001)
T9704 002:259.558 - 0.003ms returns 0
T9704 002:259.563 JLINK_WriteReg(R15 (PC), 0x2000003E)
T9704 002:259.567 - 0.004ms returns 0
T9704 002:259.571 JLINK_WriteReg(XPSR, 0x01000000)
T9704 002:259.574 - 0.003ms returns 0
T9704 002:259.578 JLINK_WriteReg(MSP, 0x20008000)
T9704 002:259.582 - 0.003ms returns 0
T9704 002:259.586 JLINK_WriteReg(PSP, 0x20008000)
T9704 002:259.590 - 0.003ms returns 0
T9704 002:259.594 JLINK_WriteReg(CFBP, 0x00000000)
T9704 002:259.597 - 0.003ms returns 0
T9704 002:259.602 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 002:259.607 - 0.005ms returns 0x00000027
T9704 002:259.612 JLINK_Go()
T9704 002:259.620   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 002:260.375   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 002:269.636 - 10.022ms 
T9704 002:269.654 JLINK_IsHalted()
T9704 002:278.303 - 8.648ms returns TRUE
T9704 002:278.316 JLINK_ReadReg(R15 (PC))
T9704 002:278.322 - 0.006ms returns 0x20000000
T9704 002:278.327 JLINK_ClrBPEx(BPHandle = 0x00000027)
T9704 002:278.331 - 0.004ms returns 0x00
T9704 002:278.336 JLINK_ReadReg(R0)
T9704 002:278.339 - 0.003ms returns 0x00000000
T9704 002:278.344 JLINK_HasError()
T9704 002:278.349 JLINK_WriteReg(R0, 0xFFFFFFFF)
T9704 002:278.353 - 0.004ms returns 0
T9704 002:278.357 JLINK_WriteReg(R1, 0x08000000)
T9704 002:278.361 - 0.003ms returns 0
T9704 002:278.365 JLINK_WriteReg(R2, 0x00008278)
T9704 002:278.369 - 0.003ms returns 0
T9704 002:278.373 JLINK_WriteReg(R3, 0x04C11DB7)
T9704 002:278.376 - 0.003ms returns 0
T9704 002:278.380 JLINK_WriteReg(R4, 0x00000000)
T9704 002:278.384 - 0.003ms returns 0
T9704 002:278.388 JLINK_WriteReg(R5, 0x00000000)
T9704 002:278.392 - 0.004ms returns 0
T9704 002:278.396 JLINK_WriteReg(R6, 0x00000000)
T9704 002:278.400 - 0.003ms returns 0
T9704 002:278.404 JLINK_WriteReg(R7, 0x00000000)
T9704 002:278.408 - 0.003ms returns 0
T9704 002:278.412 JLINK_WriteReg(R8, 0x00000000)
T9704 002:278.415 - 0.003ms returns 0
T9704 002:278.419 JLINK_WriteReg(R9, 0x20000414)
T9704 002:278.423 - 0.003ms returns 0
T9704 002:278.427 JLINK_WriteReg(R10, 0x00000000)
T9704 002:278.431 - 0.003ms returns 0
T9704 002:278.438 JLINK_WriteReg(R11, 0x00000000)
T9704 002:278.442 - 0.003ms returns 0
T9704 002:278.446 JLINK_WriteReg(R12, 0x00000000)
T9704 002:278.449 - 0.003ms returns 0
T9704 002:278.454 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 002:278.457 - 0.004ms returns 0
T9704 002:278.461 JLINK_WriteReg(R14, 0x20000001)
T9704 002:278.465 - 0.003ms returns 0
T9704 002:278.469 JLINK_WriteReg(R15 (PC), 0x20000002)
T9704 002:278.473 - 0.003ms returns 0
T9704 002:278.477 JLINK_WriteReg(XPSR, 0x01000000)
T9704 002:278.480 - 0.003ms returns 0
T9704 002:278.484 JLINK_WriteReg(MSP, 0x20008000)
T9704 002:278.488 - 0.003ms returns 0
T9704 002:278.492 JLINK_WriteReg(PSP, 0x20008000)
T9704 002:278.496 - 0.003ms returns 0
T9704 002:278.500 JLINK_WriteReg(CFBP, 0x00000000)
T9704 002:278.504 - 0.003ms returns 0
T9704 002:278.508 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 002:278.513 - 0.004ms returns 0x00000028
T9704 002:278.517 JLINK_Go()
T9704 002:278.525   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 002:279.438   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 002:288.688 - 10.170ms 
T9704 002:288.704 JLINK_IsHalted()
T9704 002:289.477 - 0.773ms returns FALSE
T9704 002:289.489 JLINK_HasError()
T9704 002:293.401 JLINK_IsHalted()
T9704 002:294.223 - 0.821ms returns FALSE
T9704 002:294.232 JLINK_HasError()
T9704 002:295.506 JLINK_IsHalted()
T9704 002:296.358 - 0.852ms returns FALSE
T9704 002:296.366 JLINK_HasError()
T9704 002:297.590 JLINK_IsHalted()
T9704 002:298.475 - 0.884ms returns FALSE
T9704 002:298.485 JLINK_HasError()
T9704 002:299.686 JLINK_IsHalted()
T9704 002:300.553 - 0.866ms returns FALSE
T9704 002:300.568 JLINK_HasError()
T9704 002:303.464 JLINK_IsHalted()
T9704 002:304.315 - 0.850ms returns FALSE
T9704 002:304.324 JLINK_HasError()
T9704 002:305.564 JLINK_IsHalted()
T9704 002:306.437 - 0.872ms returns FALSE
T9704 002:306.446 JLINK_HasError()
T9704 002:307.656 JLINK_IsHalted()
T9704 002:308.540 - 0.883ms returns FALSE
T9704 002:308.549 JLINK_HasError()
T9704 002:309.748 JLINK_IsHalted()
T9704 002:310.611 - 0.863ms returns FALSE
T9704 002:310.618 JLINK_HasError()
T9704 002:311.842 JLINK_IsHalted()
T9704 002:312.724 - 0.882ms returns FALSE
T9704 002:312.732 JLINK_HasError()
T9704 002:313.943 JLINK_IsHalted()
T9704 002:314.931 - 0.987ms returns FALSE
T9704 002:314.941 JLINK_HasError()
T9704 002:316.043 JLINK_IsHalted()
T9704 002:316.805 - 0.761ms returns FALSE
T9704 002:316.815 JLINK_HasError()
T9704 002:318.150 JLINK_IsHalted()
T9704 002:326.920 - 8.769ms returns TRUE
T9704 002:326.932 JLINK_ReadReg(R15 (PC))
T9704 002:326.938 - 0.006ms returns 0x20000000
T9704 002:326.942 JLINK_ClrBPEx(BPHandle = 0x00000028)
T9704 002:326.947 - 0.004ms returns 0x00
T9704 002:326.951 JLINK_ReadReg(R0)
T9704 002:326.955 - 0.003ms returns 0x7D7B9315
T9704 002:328.236 JLINK_HasError()
T9704 002:328.246 JLINK_WriteReg(R0, 0x00000003)
T9704 002:328.255 - 0.008ms returns 0
T9704 002:328.259 JLINK_WriteReg(R1, 0x08000000)
T9704 002:328.263 - 0.003ms returns 0
T9704 002:328.267 JLINK_WriteReg(R2, 0x00008278)
T9704 002:328.271 - 0.003ms returns 0
T9704 002:328.275 JLINK_WriteReg(R3, 0x04C11DB7)
T9704 002:328.278 - 0.003ms returns 0
T9704 002:328.282 JLINK_WriteReg(R4, 0x00000000)
T9704 002:328.286 - 0.003ms returns 0
T9704 002:328.290 JLINK_WriteReg(R5, 0x00000000)
T9704 002:328.294 - 0.003ms returns 0
T9704 002:328.298 JLINK_WriteReg(R6, 0x00000000)
T9704 002:328.302 - 0.003ms returns 0
T9704 002:328.306 JLINK_WriteReg(R7, 0x00000000)
T9704 002:328.309 - 0.003ms returns 0
T9704 002:328.313 JLINK_WriteReg(R8, 0x00000000)
T9704 002:328.317 - 0.003ms returns 0
T9704 002:328.321 JLINK_WriteReg(R9, 0x20000414)
T9704 002:328.324 - 0.003ms returns 0
T9704 002:328.328 JLINK_WriteReg(R10, 0x00000000)
T9704 002:328.332 - 0.003ms returns 0
T9704 002:328.336 JLINK_WriteReg(R11, 0x00000000)
T9704 002:328.340 - 0.003ms returns 0
T9704 002:328.344 JLINK_WriteReg(R12, 0x00000000)
T9704 002:328.347 - 0.003ms returns 0
T9704 002:328.351 JLINK_WriteReg(R13 (SP), 0x20008000)
T9704 002:328.358 - 0.006ms returns 0
T9704 002:328.363 JLINK_WriteReg(R14, 0x20000001)
T9704 002:328.366 - 0.003ms returns 0
T9704 002:328.370 JLINK_WriteReg(R15 (PC), 0x2000009C)
T9704 002:328.374 - 0.004ms returns 0
T9704 002:328.379 JLINK_WriteReg(XPSR, 0x01000000)
T9704 002:328.382 - 0.003ms returns 0
T9704 002:328.386 JLINK_WriteReg(MSP, 0x20008000)
T9704 002:328.390 - 0.003ms returns 0
T9704 002:328.394 JLINK_WriteReg(PSP, 0x20008000)
T9704 002:328.398 - 0.003ms returns 0
T9704 002:328.402 JLINK_WriteReg(CFBP, 0x00000000)
T9704 002:328.405 - 0.003ms returns 0
T9704 002:328.410 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T9704 002:328.415 - 0.005ms returns 0x00000029
T9704 002:328.420 JLINK_Go()
T9704 002:328.432   CPU_WriteMem(4 bytes @ 0xE0002000)
T9704 002:329.182   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 002:338.495 - 10.074ms 
T9704 002:338.512 JLINK_IsHalted()
T9704 002:347.169 - 8.656ms returns TRUE
T9704 002:347.183 JLINK_ReadReg(R15 (PC))
T9704 002:347.190 - 0.006ms returns 0x20000000
T9704 002:347.212 JLINK_ClrBPEx(BPHandle = 0x00000029)
T9704 002:347.217 - 0.004ms returns 0x00
T9704 002:347.222 JLINK_ReadReg(R0)
T9704 002:347.225 - 0.003ms returns 0x00000000
T9704 002:400.296 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
T9704 002:400.307   Data:  FE E7
T9704 002:400.324   CPU_WriteMem(2 bytes @ 0x20000000)
T9704 002:401.130 - 0.834ms returns 0x2
T9704 002:401.145 JLINK_HasError()
T9704 002:407.660 JLINK_Close()
T9704 002:408.044   CPU_WriteMem(4 bytes @ 0xE0002008)
T9704 002:411.328   OnDisconnectTarget() start
T9704 002:411.341    J-Link Script File: Executing OnDisconnectTarget()
T9704 002:411.356   CPU_WriteMem(4 bytes @ 0x5C001004)
T9704 002:412.126   CPU_WriteMem(4 bytes @ 0x5C001034)
T9704 002:412.948   CPU_WriteMem(4 bytes @ 0x5C001054)
T9704 002:416.169   OnDisconnectTarget() end - Took 2.50ms
T9704 002:416.190   CPU_ReadMem(4 bytes @ 0xE0001000)
T9704 002:442.890 - 35.228ms
T9704 002:442.901   
T9704 002:442.906   Closed
