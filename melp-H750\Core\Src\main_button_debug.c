/* SPDX-License-Identifier: MIT */
/**
 * @file main_button_debug.c
 * @brief Button diagnostic program to debug button issues
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */

#include "main.h"
#include <stdio.h>
#include <stdarg.h>
#include <string.h>

/* External variables */
extern UART_HandleTypeDef huart1;

/* Function prototypes */
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_USART1_Init(void);
void Error_Handler(void);

// Debug functions
int debug_printf(const char *format, ...)
{
    static char buffer[256];
    va_list args;
    va_start(args, format);
    int len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    if (len > 0) {
        HAL_UART_Transmit(&huart1, (uint8_t*)buffer, len, 100);
    }
    
    return len;
}

/**
 * @brief Main function for button diagnostics
 */
int main(void)
{
    /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
    HAL_Init();

    /* Configure the system clock */
    SystemClock_Config();

    /* Initialize GPIO and UART */
    MX_GPIO_Init();
    MX_USART1_Init();

    debug_printf("\r\n=== STM32H750 BUTTON DIAGNOSTIC ===\r\n");
    debug_printf("This program will help diagnose button connection issues\r\n");
    
    // Test different button configurations
    debug_printf("\r\n=== Testing Button Configurations ===\r\n");
    
    // Configuration 1: PC1 with pull-up (current)
    debug_printf("Config 1: PC1 with internal pull-up\r\n");
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_1;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    
    for (int i = 0; i < 10; i++) {
        GPIO_PinState state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_1);
        debug_printf("PC1 (pull-up): %s\r\n", (state == GPIO_PIN_SET) ? "HIGH" : "LOW");
        HAL_Delay(500);
    }
    
    // Configuration 2: PC1 with pull-down
    debug_printf("\r\nConfig 2: PC1 with internal pull-down\r\n");
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    
    for (int i = 0; i < 10; i++) {
        GPIO_PinState state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_1);
        debug_printf("PC1 (pull-down): %s\r\n", (state == GPIO_PIN_SET) ? "HIGH" : "LOW");
        HAL_Delay(500);
    }
    
    // Configuration 3: PC1 floating (no pull)
    debug_printf("\r\nConfig 3: PC1 floating (no pull resistor)\r\n");
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    
    for (int i = 0; i < 10; i++) {
        GPIO_PinState state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_1);
        debug_printf("PC1 (floating): %s\r\n", (state == GPIO_PIN_SET) ? "HIGH" : "LOW");
        HAL_Delay(500);
    }
    
    // Test other pins to see if button is connected elsewhere
    debug_printf("\r\n=== Testing Other Potential Button Pins ===\r\n");
    
    // Test PC0
    debug_printf("Testing PC0...\r\n");
    GPIO_InitStruct.Pin = GPIO_PIN_0;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    
    for (int i = 0; i < 5; i++) {
        GPIO_PinState state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_0);
        debug_printf("PC0: %s\r\n", (state == GPIO_PIN_SET) ? "HIGH" : "LOW");
        HAL_Delay(500);
    }
    
    // Test PA0 (common button pin)
    debug_printf("Testing PA0...\r\n");
    __HAL_RCC_GPIOA_CLK_ENABLE();
    GPIO_InitStruct.Pin = GPIO_PIN_0;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    for (int i = 0; i < 5; i++) {
        GPIO_PinState state = HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_0);
        debug_printf("PA0: %s\r\n", (state == GPIO_PIN_SET) ? "HIGH" : "LOW");
        HAL_Delay(500);
    }
    
    // Test PB0
    debug_printf("Testing PB0...\r\n");
    __HAL_RCC_GPIOB_CLK_ENABLE();
    GPIO_InitStruct.Pin = GPIO_PIN_0;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
    
    for (int i = 0; i < 5; i++) {
        GPIO_PinState state = HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_0);
        debug_printf("PB0: %s\r\n", (state == GPIO_PIN_SET) ? "HIGH" : "LOW");
        HAL_Delay(500);
    }
    
    // Final test: Monitor PC1 continuously with detailed info
    debug_printf("\r\n=== Continuous PC1 Monitoring ===\r\n");
    debug_printf("Monitoring PC1 continuously. Press button to test...\r\n");
    debug_printf("Expected: HIGH when not pressed, LOW when pressed (if button connects to GND)\r\n");
    debug_printf("Or: LOW when not pressed, HIGH when pressed (if button connects to VCC)\r\n");
    
    // Reset PC1 to pull-up
    GPIO_InitStruct.Pin = GPIO_PIN_1;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    
    uint32_t loop_count = 0;
    GPIO_PinState last_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_1);
    
    while (1)
    {
        GPIO_PinState current_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_1);
        
        // Report any state changes immediately
        if (current_state != last_state) {
            debug_printf("*** PC1 STATE CHANGE: %s -> %s ***\r\n", 
                       (last_state == GPIO_PIN_SET) ? "HIGH" : "LOW",
                       (current_state == GPIO_PIN_SET) ? "HIGH" : "LOW");
            last_state = current_state;
        }
        
        // Report status every 5 seconds
        if (loop_count % 5000 == 0) {
            debug_printf("PC1 Status: %s (Loop: %lu)\r\n", 
                       (current_state == GPIO_PIN_SET) ? "HIGH" : "LOW",
                       loop_count / 1000);
        }
        
        loop_count++;
        HAL_Delay(1);
    }
}

/**
 * @brief System Clock Configuration
 */
void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    HAL_PWREx_ConfigSupply(PWR_LDO_SUPPLY);
    __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE2);
    while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {}

    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
    RCC_OscInitStruct.HSIState = RCC_HSI_DIV1;
    RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
    RCC_OscInitStruct.PLL.PLLM = 4;
    RCC_OscInitStruct.PLL.PLLN = 30;
    RCC_OscInitStruct.PLL.PLLP = 2;
    RCC_OscInitStruct.PLL.PLLQ = 4;
    RCC_OscInitStruct.PLL.PLLR = 2;
    RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_3;
    RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOWIDE;
    RCC_OscInitStruct.PLL.PLLFRACN = 0;
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK) {
        Error_Handler();
    }

    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                                |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2
                                |RCC_CLOCKTYPE_D3PCLK1|RCC_CLOCKTYPE_D1PCLK1;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;
    RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV1;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV1;
    RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV1;

    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK) {
        Error_Handler();
    }
}

/**
 * @brief USART1 Initialization Function
 */
static void MX_USART1_Init(void)
{
    __HAL_RCC_USART1_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
    
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_9|GPIO_PIN_10;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART1;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    huart1.Instance = USART1;
    huart1.Init.BaudRate = 921600;
    huart1.Init.WordLength = UART_WORDLENGTH_8B;
    huart1.Init.StopBits = UART_STOPBITS_1;
    huart1.Init.Parity = UART_PARITY_NONE;
    huart1.Init.Mode = UART_MODE_TX_RX;
    huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart1.Init.OverSampling = UART_OVERSAMPLING_16;
    huart1.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
    huart1.Init.ClockPrescaler = UART_PRESCALER_DIV1;
    huart1.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
    if (HAL_UART_Init(&huart1) != HAL_OK) {
        Error_Handler();
    }
}

/**
 * @brief GPIO Initialization Function
 */
static void MX_GPIO_Init(void)
{
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();

    // Configure LED (PC13)
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET); // LED OFF initially
    
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_13;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
}

/**
 * @brief Error Handler
 */
void Error_Handler(void)
{
    debug_printf("=== ERROR HANDLER CALLED ===\r\n");
    __disable_irq();
    while (1) {
        HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
        for (volatile int i = 0; i < 200000; i++);
    }
}
