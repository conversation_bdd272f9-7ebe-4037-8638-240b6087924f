/* SPDX-License-Identifier: MIT */
/**
 * @file debug_utils.c
 * @brief Debug utilities implementation
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */

#include "debug_utils.h"
#include "main.h"
#include "project_config.h"
#include <stdarg.h>
#include <string.h>

/* ========================================================================== */
/*                           EXTERNAL VARIABLES                              */
/* ========================================================================== */

extern UART_HandleTypeDef huart1;

/* ========================================================================== */
/*                           PRIVATE VARIABLES                               */
/* ========================================================================== */

static bool debug_initialized = false;

/* ========================================================================== */
/*                           FUNCTION IMPLEMENTATIONS                        */
/* ========================================================================== */

bool debug_init(void)
{
    if (debug_initialized) {
        return true;
    }
    
    // UART should already be initialized by main
    debug_initialized = true;
    
    // Send initial debug message
    debug_printf("\r\n=== STM32H750 MELP Debug System ===\r\n");
    debug_printf("System Clock: %lu Hz\r\n", HAL_RCC_GetSysClockFreq());
    debug_printf("Debug initialized successfully\r\n");
    
    return true;
}

int debug_printf(const char *format, ...)
{
    if (!debug_initialized) {
        return 0;
    }
    
    static char buffer[256];
    va_list args;
    va_start(args, format);
    int len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    if (len > 0) {
        HAL_UART_Transmit(&huart1, (uint8_t*)buffer, len, DEBUG_UART_TIMEOUT_MS);
    }
    
    return len;
}

void debug_test_led(void)
{
    DEBUG_INFO("Testing LED functionality...");
    
    // Test LED on
    HAL_GPIO_WritePin(LED_GPIO_PORT, LED_GPIO_PIN, GPIO_PIN_RESET); // Active low
    DEBUG_INFO("LED should be ON");
    HAL_Delay(1000);
    
    // Test LED off
    HAL_GPIO_WritePin(LED_GPIO_PORT, LED_GPIO_PIN, GPIO_PIN_SET); // Active low
    DEBUG_INFO("LED should be OFF");
    HAL_Delay(1000);
    
    // Test LED toggle
    for (int i = 0; i < 5; i++) {
        HAL_GPIO_TogglePin(LED_GPIO_PORT, LED_GPIO_PIN);
        DEBUG_INFO("LED toggle %d", i + 1);
        HAL_Delay(500);
    }
    
    DEBUG_INFO("LED test completed");
}

void debug_test_button(void)
{
    DEBUG_INFO("Testing button functionality...");
    DEBUG_INFO("Press and release the button (PC1) within 10 seconds...");
    
    uint32_t start_time = HAL_GetTick();
    GPIO_PinState last_state = HAL_GPIO_ReadPin(BUTTON_GPIO_PORT, BUTTON_GPIO_PIN);
    bool button_pressed = false;
    
    while ((HAL_GetTick() - start_time) < 10000) {
        GPIO_PinState current_state = HAL_GPIO_ReadPin(BUTTON_GPIO_PORT, BUTTON_GPIO_PIN);
        
        if (current_state != last_state) {
            if (current_state == GPIO_PIN_RESET) { // Button pressed (active low)
                DEBUG_INFO("Button PRESSED");
                button_pressed = true;
                // Turn on LED when button pressed
                HAL_GPIO_WritePin(LED_GPIO_PORT, LED_GPIO_PIN, GPIO_PIN_RESET);
            } else { // Button released
                DEBUG_INFO("Button RELEASED");
                // Turn off LED when button released
                HAL_GPIO_WritePin(LED_GPIO_PORT, LED_GPIO_PIN, GPIO_PIN_SET);
            }
            last_state = current_state;
            HAL_Delay(50); // Debounce
        }
        
        HAL_Delay(10);
    }
    
    if (button_pressed) {
        DEBUG_INFO("Button test PASSED");
    } else {
        DEBUG_WARN("Button test FAILED - no button press detected");
    }
}

void debug_test_uart(void)
{
    DEBUG_INFO("Testing UART functionality...");
    
    const char test_msg[] = "UART test message - if you see this, UART is working!\r\n";
    HAL_StatusTypeDef status = HAL_UART_Transmit(&huart1, (uint8_t*)test_msg, strlen(test_msg), 1000);
    
    if (status == HAL_OK) {
        DEBUG_INFO("UART test PASSED");
    } else {
        DEBUG_ERROR("UART test FAILED - status: %d", status);
    }
}

void debug_print_system_info(void)
{
    DEBUG_INFO("=== System Information ===");
    DEBUG_INFO("MCU: STM32H750VBT6");
    DEBUG_INFO("System Clock: %lu Hz", HAL_RCC_GetSysClockFreq());
    DEBUG_INFO("HAL Version: %lu", HAL_GetHalVersion());
    DEBUG_INFO("Revision ID: 0x%04lX", HAL_GetREVID());
    DEBUG_INFO("Device ID: 0x%04lX", HAL_GetDEVID());
    DEBUG_INFO("UID: 0x%08lX%08lX%08lX", 
               HAL_GetUIDw0(), HAL_GetUIDw1(), HAL_GetUIDw2());
    
    // GPIO configuration info
    DEBUG_INFO("=== GPIO Configuration ===");
    DEBUG_INFO("LED: Port %c, Pin %d", 
               (LED_GPIO_PORT == GPIOA) ? 'A' : 
               (LED_GPIO_PORT == GPIOB) ? 'B' : 
               (LED_GPIO_PORT == GPIOC) ? 'C' : 'X',
               __builtin_ctz(LED_GPIO_PIN));
    
    DEBUG_INFO("Button: Port %c, Pin %d", 
               (BUTTON_GPIO_PORT == GPIOA) ? 'A' : 
               (BUTTON_GPIO_PORT == GPIOB) ? 'B' : 
               (BUTTON_GPIO_PORT == GPIOC) ? 'C' : 'X',
               __builtin_ctz(BUTTON_GPIO_PIN));
    
    DEBUG_INFO("=== End System Information ===");
}

// Printf redirection for standard library
#ifdef __GNUC__
int _write(int file, char *ptr, int len)
{
    if (debug_initialized) {
        HAL_UART_Transmit(&huart1, (uint8_t*)ptr, len, DEBUG_UART_TIMEOUT_MS);
    }
    return len;
}
#endif

#ifdef __ARMCC_VERSION
int fputc(int ch, FILE *f)
{
    if (debug_initialized) {
        HAL_UART_Transmit(&huart1, (uint8_t*)&ch, 1, DEBUG_UART_TIMEOUT_MS);
    }
    return ch;
}
#endif
