/* SPDX-License-Identifier: MIT */
/**
 * @file main_bypass_app.c
 * @brief Main file that bypasses the application layer to isolate the problem
 * <AUTHOR> Assistant
 * @date 2025-01-22
 * 
 * This version completely bypasses the app_init() and app_run() functions
 * to test if the problem is in the application layer.
 */

#include "main.h"
#include <stdio.h>
#include <stdarg.h>
#include <string.h>

/* External variables */
extern UART_HandleTypeDef huart1;

/* Function prototypes */
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_ADC1_Init(void);
static void MX_DAC1_Init(void);
static void MX_USART1_Init(void);
void Error_Handler(void);

// Debug functions
int debug_printf(const char *format, ...)
{
    static char buffer[256];
    va_list args;
    va_start(args, format);
    int len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    if (len > 0) {
        HAL_UART_Transmit(&huart1, (uint8_t*)buffer, len, 100);
    }
    
    return len;
}

/**
 * @brief Main function that bypasses application layer
 */
int main(void)
{
    /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
    HAL_Init();

    /* Configure the system clock */
    SystemClock_Config();

    /* Initialize all configured peripherals */
    MX_GPIO_Init();
    MX_ADC1_Init();
    MX_DAC1_Init();
    MX_USART1_Init();

    // Initialize debug system
    debug_printf("\r\n=== STM32H750 BYPASS APP TEST ===\r\n");
    debug_printf("System Clock: %lu Hz\r\n", HAL_RCC_GetSysClockFreq());
    debug_printf("HAL_Init: OK\r\n");
    debug_printf("SystemClock_Config: OK\r\n");
    debug_printf("MX_GPIO_Init: OK\r\n");
    debug_printf("MX_ADC1_Init: OK\r\n");
    debug_printf("MX_DAC1_Init: OK\r\n");
    debug_printf("MX_USART1_Init: OK\r\n");
    
    // Print system information
    debug_printf("=== System Information ===\r\n");
    debug_printf("MCU: STM32H750VBT6\r\n");
    debug_printf("System Clock: %lu Hz\r\n", HAL_RCC_GetSysClockFreq());
    debug_printf("HAL Version: %lu\r\n", HAL_GetHalVersion());
    debug_printf("Revision ID: 0x%04lX\r\n", HAL_GetREVID());
    debug_printf("Device ID: 0x%04lX\r\n", HAL_GetDEVID());
    debug_printf("LED: Port C, Pin 13\r\n");
    debug_printf("Button: Port C, Pin 1\r\n");
    debug_printf("=== End System Information ===\r\n");
    
    // Test LED
    debug_printf("Testing LED...\r\n");
    for (int i = 0; i < 3; i++) {
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET); // LED ON
        debug_printf("LED ON\r\n");
        HAL_Delay(500);
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);   // LED OFF
        debug_printf("LED OFF\r\n");
        HAL_Delay(500);
    }
    
    debug_printf("*** BYPASSING APPLICATION LAYER ***\r\n");
    debug_printf("Starting simple main loop without app_init() or app_run()\r\n");
    debug_printf("This will help identify if the problem is in the application layer\r\n");
    
    uint32_t loop_count = 0;
    GPIO_PinState last_button_state = GPIO_PIN_SET;
    
    /* Main loop - completely independent of application layer */
    while (1)
    {
        // Read button state (PC1, active low)
        GPIO_PinState button_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_1);
        
        // Check for button state change
        if (button_state != last_button_state) {
            if (button_state == GPIO_PIN_RESET) {
                debug_printf("Button PRESSED\r\n");
                // Turn on LED when button pressed
                HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
            } else {
                debug_printf("Button RELEASED\r\n");
                // Turn off LED when button released
                HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);
            }
            last_button_state = button_state;
            HAL_Delay(50); // Debounce
        }
        
        // Send heartbeat every 5 seconds
        if (loop_count % 5000 == 0) {
            debug_printf("Heartbeat: %lu, Button: %s, LED: %s\r\n", 
                       loop_count / 1000,
                       (button_state == GPIO_PIN_RESET) ? "PRESSED" : "RELEASED",
                       (HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_13) == GPIO_PIN_RESET) ? "ON" : "OFF");
        }
        
        loop_count++;
        HAL_Delay(1);
    }
}

/**
 * @brief System Clock Configuration
 * @retval None
 */
void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    /** Supply configuration update enable */
    HAL_PWREx_ConfigSupply(PWR_LDO_SUPPLY);

    /** Configure the main internal regulator output voltage */
    __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE2);

    while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {}

    /** Initializes the RCC Oscillators according to the specified parameters */
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
    RCC_OscInitStruct.HSIState = RCC_HSI_DIV1;
    RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
    RCC_OscInitStruct.PLL.PLLM = 4;
    RCC_OscInitStruct.PLL.PLLN = 30;
    RCC_OscInitStruct.PLL.PLLP = 2;
    RCC_OscInitStruct.PLL.PLLQ = 4;
    RCC_OscInitStruct.PLL.PLLR = 2;
    RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_3;
    RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOWIDE;
    RCC_OscInitStruct.PLL.PLLFRACN = 0;
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
    {
        Error_Handler();
    }

    /** Initializes the CPU, AHB and APB buses clocks */
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                                |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2
                                |RCC_CLOCKTYPE_D3PCLK1|RCC_CLOCKTYPE_D1PCLK1;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;
    RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV1;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV1;
    RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV1;

    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
    {
        Error_Handler();
    }
}

/**
 * @brief ADC1 Initialization Function
 */
static void MX_ADC1_Init(void)
{
    // Simplified ADC init for debug
    debug_printf("ADC1 init placeholder\r\n");
}

/**
 * @brief DAC1 Initialization Function
 */
static void MX_DAC1_Init(void)
{
    // Simplified DAC init for debug
    debug_printf("DAC1 init placeholder\r\n");
}

/**
 * @brief USART1 Initialization Function
 */
static void MX_USART1_Init(void)
{
    // Enable clocks
    __HAL_RCC_USART1_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
    
    // Configure GPIO pins for USART1 (PA9=TX, PA10=RX)
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_9|GPIO_PIN_10;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART1;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    // Configure USART1
    huart1.Instance = USART1;
    huart1.Init.BaudRate = 921600;
    huart1.Init.WordLength = UART_WORDLENGTH_8B;
    huart1.Init.StopBits = UART_STOPBITS_1;
    huart1.Init.Parity = UART_PARITY_NONE;
    huart1.Init.Mode = UART_MODE_TX_RX;
    huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart1.Init.OverSampling = UART_OVERSAMPLING_16;
    huart1.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
    huart1.Init.ClockPrescaler = UART_PRESCALER_DIV1;
    huart1.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
    if (HAL_UART_Init(&huart1) != HAL_OK)
    {
        Error_Handler();
    }
}

/**
 * @brief GPIO Initialization Function
 */
static void MX_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* GPIO Ports Clock Enable */
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();

    /*Configure GPIO pin Output Level for LED (PC13) */
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET); // LED OFF initially

    /*Configure GPIO pin : PC13 (LED) */
    GPIO_InitStruct.Pin = GPIO_PIN_13;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    /*Configure GPIO pin : PC1 (Button) */
    GPIO_InitStruct.Pin = GPIO_PIN_1;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
}

/**
 * @brief Error Handler
 */
void Error_Handler(void)
{
    debug_printf("=== ERROR HANDLER CALLED ===\r\n");
    
    /* Disable interrupts */
    __disable_irq();
    
    /* Flash LED very rapidly to indicate error */
    while (1)
    {
        HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
        for (volatile int i = 0; i < 200000; i++); // Very fast blink
    }
}
