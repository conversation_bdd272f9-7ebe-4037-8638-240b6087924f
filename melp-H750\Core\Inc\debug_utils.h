/* SPDX-License-Identifier: MIT */
/**
 * @file debug_utils.h
 * @brief Debug utilities for system diagnostics
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */

#ifndef DEBUG_UTILS_H
#define DEBUG_UTILS_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include "stm32h7xx_hal.h"

/* ========================================================================== */
/*                           DEBUG CONFIGURATION                             */
/* ========================================================================== */

#define DEBUG_ENABLE                1
#define DEBUG_UART_TIMEOUT_MS      100

/* ========================================================================== */
/*                           DEBUG MACROS                                    */
/* ========================================================================== */

#if DEBUG_ENABLE
#define DEBUG_PRINT(fmt, ...)      debug_printf("[DEBUG] " fmt "\r\n", ##__VA_ARGS__)
#define DEBUG_INFO(fmt, ...)       debug_printf("[INFO]  " fmt "\r\n", ##__VA_ARGS__)
#define DEBUG_WARN(fmt, ...)       debug_printf("[WARN]  " fmt "\r\n", ##__VA_ARGS__)
#define DEBUG_ERROR(fmt, ...)      debug_printf("[ERROR] " fmt "\r\n", ##__VA_ARGS__)
#define DEBUG_TRACE()              debug_printf("[TRACE] %s:%d\r\n", __FUNCTION__, __LINE__)
#else
#define DEBUG_PRINT(fmt, ...)      ((void)0)
#define DEBUG_INFO(fmt, ...)       ((void)0)
#define DEBUG_WARN(fmt, ...)       ((void)0)
#define DEBUG_ERROR(fmt, ...)      ((void)0)
#define DEBUG_TRACE()              ((void)0)
#endif

/* ========================================================================== */
/*                           FUNCTION DECLARATIONS                           */
/* ========================================================================== */

/**
 * @brief Initialize debug utilities
 * @return true if successful, false otherwise
 */
bool debug_init(void);

/**
 * @brief Printf function for debug output
 * @param format Format string
 * @param ... Variable arguments
 * @return Number of characters printed
 */
int debug_printf(const char *format, ...);

/**
 * @brief Test LED functionality
 */
void debug_test_led(void);

/**
 * @brief Test button functionality
 */
void debug_test_button(void);

/**
 * @brief Test UART functionality
 */
void debug_test_uart(void);

/**
 * @brief Print system information
 */
void debug_print_system_info(void);

#ifdef __cplusplus
}
#endif

#endif /* DEBUG_UTILS_H */
